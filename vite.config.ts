import { ComboAutocomplete } from '@/components/Singles/ComboAutocomplete/ComboAutocomplete';
// import { path } from 'path';
// import { defineConfig } from 'vite';
// import react from '@vitejs/plugin-react';

// // https://vite.dev/config/
// export default defineConfig({
//   plugins: [react()],
// });


import { defineConfig } from 'vite'
import react from '@vitejs/plugin-react'
import path from 'path';
import Paths from './tsconfig.paths.json'
// import tailwindcss from '@tailwindcss/vite'

// import.meta.glob('./tsconfig.paths.json')

const aliasPaths = () => {
  let p: any = {};
  if(Paths && Paths.compilerOptions && Paths.compilerOptions.paths) {
    for (let i of Object.keys(Paths.compilerOptions.paths) ) {
      let key = i.replace('/*', '');
      p[key] =  path.resolve(__dirname, Paths.compilerOptions.paths[i][0]).replace('/*', '')
    }

  }
  console.log(p);
  return p;
}


// https://vitejs.dev/config/
export default defineConfig({
  resolve: {
    alias: aliasPaths()
    // {
    //   '@': path.resolve(__dirname, './src'),
    //   '@assets': path.resolve(__dirname, './src/assets'),
    //   '@components': path.resolve(__dirname, './src/components'),
    // },
  },
  plugins: [
    react(),
    // tailwindcss()  
  ],
  server: {
    proxy: {
      '/api': {
        
        target: 'https://dev-records.kibernetika.io/api',
        changeOrigin: true,
        rewrite: (path) => path.replace(/^\/api/, ''),
        cookieDomainRewrite: {
          "*": "",
        },
        cookiePathRewrite: {
          "*": "/",
        },
      },
    }
  }
})


