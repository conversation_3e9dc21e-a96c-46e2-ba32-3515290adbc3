{"name": "records-ui", "private": true, "version": "1.0.0", "type": "module", "scripts": {"dev": "vite ", "build": "tsc && vite build", "preview": "vite preview"}, "dependencies": {"@emotion/react": "^11.14.0", "@emotion/styled": "^11.14.0", "@mui/icons-material": "^7.0.0", "@mui/material": "^7.0.0", "@mui/x-date-pickers": "^7.28.3", "@toolpad/core": "^0.14.0", "@types/uuid": "^10.0.0", "date-fns": "^2.30.0", "dayjs": "^1.11.13", "react": "^19.0.0", "react-dom": "^19.0.0", "react-router-dom": "^7.4.0", "uuid": "^11.1.0"}, "devDependencies": {"@eslint/js": "^9.21.0", "@testing-library/jest-dom": "^6.6.3", "@testing-library/react": "^16.3.0", "@testing-library/user-event": "^14.6.1", "@types/axios": "^0.14.4", "@types/jest": "^29.5.12", "@types/material-ui": "^0.21.18", "@types/node": "^22.13.14", "@types/react": "^19.0.12", "@types/react-dom": "^19.0.4", "@types/react-router-dom": "^5.3.3", "@vitejs/plugin-react": "^4.3.4", "eslint": "^9.21.0", "eslint-plugin-react-hooks": "^5.1.0", "eslint-plugin-react-refresh": "^0.4.19", "globals": "^15.15.0", "jest": "^29.7.0", "jest-environment-jsdom": "^29.7.0", "ts-jest": "^29.3.0", "typescript": "^5.8.2", "vite": "^6.2.0"}}