import { useState, useCallback, useRef, useMemo } from 'react';
import { useDebounce } from './useDebounce';

interface UseOptimizedFormStateOptions<T> {
  /** Начальные значения */
  initialValues: T;
  /** Базовые значения для сравнения */
  baseValues?: T;
  /** Обработчик изменений (debounced) */
  onChange?: (values: T) => void;
  /** Задержка для debounce в миллисекундах */
  debounceDelay?: number;
}

interface UseOptimizedFormStateReturn<T> {
  /** Текущие значения формы */
  values: T;
  /** Обработчик изменения поля */
  handleFieldChange: (key: string, value: any) => void;
  /** Сброс формы к начальным значениям */
  reset: () => void;
  /** Проверка, изменилось ли конкретное поле */
  isFieldChanged: (key: string) => boolean;
  /** Проверка, изменилась ли форма в целом */
  hasChanges: boolean;
  /** Получить изменения относительно базовых значений */
  getChanges: () => Partial<T>;
}

/**
 * Оптимизированный хук для управления состоянием формы
 * Использует мемоизацию и debounce для предотвращения лишних ререндеров
 */
export function useOptimizedFormState<T extends Record<string, any>>({
  initialValues,
  baseValues,
  onChange,
  debounceDelay = 300,
}: UseOptimizedFormStateOptions<T>): UseOptimizedFormStateReturn<T> {
  const [values, setValues] = useState<T>(initialValues);
  const initialValuesRef = useRef(initialValues);
  const baseValuesRef = useRef(baseValues);

  // Обновляем refs при изменении пропов
  if (initialValuesRef.current !== initialValues) {
    initialValuesRef.current = initialValues;
    setValues(initialValues);
  }
  
  if (baseValuesRef.current !== baseValues) {
    baseValuesRef.current = baseValues;
  }

  // Debounced обработчик изменений
  const debouncedOnChange = useDebounce(
    useCallback((newValues: T) => {
      onChange?.(newValues);
    }, [onChange]),
    debounceDelay
  );

  // Мемоизированный обработчик изменения поля
  const handleFieldChange = useCallback((key: string, value: any) => {
    setValues(prev => {
      const newValues = { ...prev, [key]: value };
      
      // Вызываем debounced onChange
      debouncedOnChange(newValues);
      
      return newValues;
    });
  }, [debouncedOnChange]);

  // Сброс формы
  const reset = useCallback(() => {
    setValues(initialValuesRef.current);
  }, []);

  // Мемоизированная проверка изменений поля
  const isFieldChanged = useCallback((key: string) => {
    const currentValue = values[key];
    const baseValue = baseValuesRef.current?.[key];
    
    if (typeof currentValue === 'object' || typeof baseValue === 'object') {
      return JSON.stringify(currentValue) !== JSON.stringify(baseValue);
    }
    
    return currentValue !== baseValue;
  }, [values]);

  // Мемоизированная проверка общих изменений
  const hasChanges = useMemo(() => {
    if (!baseValuesRef.current) return false;
    
    return Object.keys(values).some(key => isFieldChanged(key));
  }, [values, isFieldChanged]);

  // Мемоизированное получение изменений
  const getChanges = useCallback((): Partial<T> => {
    if (!baseValuesRef.current) return {};
    
    const changes: Partial<T> = {};
    
    Object.keys(values).forEach(key => {
      if (isFieldChanged(key)) {
        changes[key as keyof T] = values[key];
      }
    });
    
    return changes;
  }, [values, isFieldChanged]);

  return {
    values,
    handleFieldChange,
    reset,
    isFieldChanged,
    hasChanges,
    getChanges,
  };
}
