import React, { useState } from 'react';
import { Box, Typography, Paper, Alert, Button, Divider, Tabs, Tab } from '@mui/material';
import { SchemaGridEdit } from '../components_lib/core/schema/SchemaGridEdit';
import { SchemaGridView } from '../components_lib/core/schema/SchemaGridView';
import { arrayExampleSchema, ArrayExampleData, defaultArrayExampleData } from '../components_lib/models/array_example';

/**
 * Страница для демонстрации работы с новыми компонентами схемы
 */
export const SchemaExamplePage: React.FC = () => {
  const [formData, setFormData] = useState<ArrayExampleData>(defaultArrayExampleData);
  const [submittedData, setSubmittedData] = useState<ArrayExampleData | null>(null);
  const [currentTab, setCurrentTab] = useState(0);

  const handleFormChange = (values: ArrayExampleData) => {
    setFormData(values);
  };

  const handleFormSubmit = (values: ArrayExampleData) => {
    setSubmittedData(values);
    console.log('Submitted form data:', values);
  };

  const handleReset = () => {
    setFormData(defaultArrayExampleData);
    setSubmittedData(null);
  };

  const handleTabChange = (event: React.SyntheticEvent, newValue: number) => {
    setCurrentTab(newValue);
  };

  return (
    <Box sx={{ p: 3, maxWidth: 1200, mx: 'auto' }}>
      <Typography variant="h4" gutterBottom>
        Пример новых компонентов схемы
      </Typography>
      
      <Typography variant="body1" sx={{ mb: 3 }}>
        Эта страница демонстрирует работу новых компонентов: SchemaGridEdit и SchemaGridView
      </Typography>

      <Alert severity="info" sx={{ mb: 3 }}>
        <Typography variant="body2">
          <strong>Новые компоненты:</strong><br/>
          • <strong>SchemaGridEdit</strong> - форма для редактирования данных<br/>
          • <strong>SchemaGridView</strong> - компонент для просмотра данных (только чтение)<br/>
          • <strong>SchemaFieldInput</strong> - поле ввода<br/>
          • <strong>SchemaFieldView</strong> - поле просмотра<br/>
          • <strong>SchemaFieldInputContainer</strong> - контейнер для поля ввода<br/>
          • <strong>SchemaFieldViewContainer</strong> - контейнер для поля просмотра
        </Typography>
      </Alert>

      <Paper sx={{ mb: 3 }}>
        <Tabs value={currentTab} onChange={handleTabChange} sx={{ borderBottom: 1, borderColor: 'divider' }}>
          <Tab label="Редактирование (SchemaGridEdit)" />
          <Tab label="Просмотр (SchemaGridView)" />
        </Tabs>

        <Box sx={{ p: 3 }}>
          {currentTab === 0 && (
            <>
              <Typography variant="h6" gutterBottom>
                Форма редактирования
              </Typography>
              
              <SchemaGridEdit<ArrayExampleData>
                schema={arrayExampleSchema}
                initialValues={formData}
                onChange={handleFormChange}
                onSubmit={handleFormSubmit}
                submitText="Отправить данные"
              />

              <Box sx={{ mt: 2, display: 'flex', gap: 2 }}>
                <Button variant="outlined" onClick={handleReset}>
                  Сбросить к значениям по умолчанию
                </Button>
              </Box>
            </>
          )}

          {currentTab === 1 && (
            <>
              <Typography variant="h6" gutterBottom>
                Просмотр данных (только чтение)
              </Typography>
              
              <SchemaGridView<ArrayExampleData>
                schema={arrayExampleSchema}
                initialValues={formData}
                baseValues={defaultArrayExampleData}
              />
            </>
          )}
        </Box>
      </Paper>

      <Paper sx={{ p: 3, mb: 3 }}>
        <Typography variant="h6" gutterBottom>
          Текущие значения формы
        </Typography>
        <pre style={{ 
          backgroundColor: '#f5f5f5', 
          padding: '16px', 
          borderRadius: '4px',
          overflow: 'auto',
          fontSize: '14px'
        }}>
          {JSON.stringify(formData, null, 2)}
        </pre>
      </Paper>

      {submittedData && (
        <Paper sx={{ p: 3 }}>
          <Typography variant="h6" gutterBottom color="success.main">
            Отправленные данные
          </Typography>
          <pre style={{ 
            backgroundColor: '#e8f5e8', 
            padding: '16px', 
            borderRadius: '4px',
            overflow: 'auto',
            fontSize: '14px'
          }}>
            {JSON.stringify(submittedData, null, 2)}
          </pre>
        </Paper>
      )}

      <Divider sx={{ my: 4 }} />

      <Paper sx={{ p: 3 }}>
        <Typography variant="h6" gutterBottom>
          Описание компонентов
        </Typography>
        
        <Typography variant="body2" component="div">
          <strong>SchemaGridEdit</strong> - основной компонент для создания форм редактирования на основе схемы. 
          Поддерживает валидацию, автоподстановку, различные типы полей включая массивы.<br/><br/>
          
          <strong>SchemaGridView</strong> - компонент для отображения данных в режиме только чтения. 
          Использует те же схемы, что и SchemaGridEdit.<br/><br/>
          
          <strong>SchemaFieldInput/SchemaFieldView</strong> - базовые компоненты для отдельных полей.<br/><br/>
          
          <strong>SchemaFieldInputContainer/SchemaFieldViewContainer</strong> - контейнеры, которые управляют 
          состоянием полей, валидацией и автоподстановкой.
        </Typography>
      </Paper>
    </Box>
  );
};
