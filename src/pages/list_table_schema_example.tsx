import React, { useState } from 'react';
import { Box, Typography, Paper, Tabs, Tab, Button } from '@mui/material';
import { ListTableSchema } from '../components_lib/core/schema/ListTableSchema';
import { SchemaData } from '../components_lib/core/schema/Schema';

// Пример схемы для таблицы
const exampleSchema: SchemaData = {
  id: {
    type: 'number',
    label: 'ID',
    editable: false,
  },
  name: {
    type: 'text',
    label: 'Название',
    required: true,
  },
  email: {
    type: 'text',
    label: 'Email',
    validation: {
      pattern: /^[^\s@]+@[^\s@]+\.[^\s@]+$/,
      message: 'Неверный формат email'
    }
  },
  status: {
    type: 'select',
    label: 'Статус',
    options: [
      { value: 'active', label: 'Активный' },
      { value: 'inactive', label: 'Неактивный' },
      { value: 'pending', label: 'Ожидает' }
    ],
    defaultValue: 'active'
  },
  tags: {
    type: 'array',
    label: 'Теги',
    array: {
      schema_value: {
        type: 'text',
        label: 'Тег',
        defaultValue: ''
      },
      variant: 'chips',
      maxItems: 5
    },
    defaultValue: []
  },
  createdAt: {
    type: 'date',
    label: 'Дата создания',
    editable: false,
  },
  isActive: {
    type: 'boolean',
    label: 'Активен',
    defaultValue: true
  }
};

// Пример данных для массива
const exampleArrayData = [
  {
    id: 1,
    name: 'Иван Иванов',
    email: '<EMAIL>',
    status: 'active',
    tags: ['разработчик', 'frontend'],
    createdAt: '2024-01-15',
    isActive: true
  },
  {
    id: 2,
    name: 'Мария Петрова',
    email: '<EMAIL>',
    status: 'pending',
    tags: ['дизайнер', 'ui/ux'],
    createdAt: '2024-01-20',
    isActive: false
  },
  {
    id: 3,
    name: 'Алексей Сидоров',
    email: '<EMAIL>',
    status: 'active',
    tags: ['backend', 'api'],
    createdAt: '2024-01-25',
    isActive: true
  },
  {
    id: 4,
    name: 'Елена Козлова',
    email: '<EMAIL>',
    status: 'inactive',
    tags: ['тестировщик', 'qa'],
    createdAt: '2024-02-01',
    isActive: false
  },
  {
    id: 5,
    name: 'Дмитрий Волков',
    email: '<EMAIL>',
    status: 'active',
    tags: ['devops', 'администратор'],
    createdAt: '2024-02-05',
    isActive: true
  }
];

interface ExampleItem {
  id?: number;
  name: string;
  email: string;
  status: string;
  tags: string[];
  createdAt: string;
  isActive: boolean;
}

/**
 * Страница для демонстрации компонента ListTableSchema
 */
export const ListTableSchemaExamplePage: React.FC = () => {
  const [currentTab, setCurrentTab] = useState(0);
  const [arrayData, setArrayData] = useState<ExampleItem[]>(exampleArrayData);

  const handleTabChange = (event: React.SyntheticEvent, newValue: number) => {
    setCurrentTab(newValue);
  };

  // Обработчики для работы с массивом данных
  const handleCreate = async (item: ExampleItem) => {
    const newItem = {
      ...item,
      id: Math.max(...arrayData.map(d => d.id || 0)) + 1,
      createdAt: new Date().toISOString().split('T')[0]
    };
    setArrayData([...arrayData, newItem]);
    console.log('Created item:', newItem);
  };

  const handleUpdate = async (item: ExampleItem) => {
    setArrayData(arrayData.map(d => d.id === item.id ? item : d));
    console.log('Updated item:', item);
  };

  const handleDelete = async (id: number | string) => {
    setArrayData(arrayData.filter(d => d.id !== id));
    console.log('Deleted item with id:', id);
  };

  const handleRowClick = (item: ExampleItem) => {
    console.log('Row clicked:', item);
  };

  const resetData = () => {
    setArrayData([...exampleArrayData]);
  };

  return (
    <Box sx={{ p: 3, maxWidth: 1400, mx: 'auto' }}>
      <Typography variant="h4" gutterBottom>
        Пример компонента ListTableSchema
      </Typography>
      
      <Typography variant="body1" sx={{ mb: 3 }}>
        Компонент ListTableSchema поддерживает работу как с API, так и с массивом данных.
        Включает CRUD операции, поиск, пагинацию и настраиваемые действия.
      </Typography>

      <Paper sx={{ mb: 3 }}>
        <Tabs value={currentTab} onChange={handleTabChange} sx={{ borderBottom: 1, borderColor: 'divider' }}>
          <Tab label="Работа с массивом данных" />
          <Tab label="Работа с API" />
          <Tab label="Только просмотр" />
        </Tabs>

        <Box sx={{ p: 3 }}>
          {currentTab === 0 && (
            <>
              <Typography variant="h6" gutterBottom>
                Таблица с массивом данных
              </Typography>
              <Typography variant="body2" sx={{ mb: 2 }}>
                Данные хранятся в локальном состоянии. Поддерживаются все CRUD операции.
              </Typography>
              
              <Box sx={{ mb: 2 }}>
                <Button variant="outlined" onClick={resetData}>
                  Сбросить данные
                </Button>
              </Box>

              <ListTableSchema<ExampleItem>
                schema={exampleSchema}
                array={arrayData}
                onCreate={handleCreate}
                onUpdate={handleUpdate}
                onDelete={handleDelete}
                onRowClick={handleRowClick}
                enableSearch={true}
                enablePagination={true}
                maxHeight="600px"
              />
            </>
          )}

          {currentTab === 1 && (
            <>
              <Typography variant="h6" gutterBottom>
                Таблица с API
              </Typography>
              <Typography variant="body2" sx={{ mb: 2 }}>
                Пример работы с API. Замените apiUrl на реальный endpoint.
              </Typography>

              <ListTableSchema<ExampleItem>
                schema={exampleSchema}
                apiUrl="/api/users"
                params={{
                  order: 'desc',
                  orderBy: 'createdAt'
                }}
                onCreate={async (item) => {
                  console.log('API Create:', item);
                  // Здесь должен быть реальный API вызов
                }}
                onUpdate={async (item) => {
                  console.log('API Update:', item);
                  // Здесь должен быть реальный API вызов
                }}
                onDelete={async (id) => {
                  console.log('API Delete:', id);
                  // Здесь должен быть реальный API вызов
                }}
                onRowClick={handleRowClick}
                enableSearch={true}
                enablePagination={true}
                maxHeight="600px"
              />
            </>
          )}

          {currentTab === 2 && (
            <>
              <Typography variant="h6" gutterBottom>
                Таблица только для просмотра
              </Typography>
              <Typography variant="body2" sx={{ mb: 2 }}>
                Отключены все операции редактирования. Только просмотр данных.
              </Typography>

              <ListTableSchema<ExampleItem>
                schema={exampleSchema}
                array={arrayData}
                enableEdit={false}
                enableDelete={false}
                enableAdd={false}
                onRowClick={handleRowClick}
                enableSearch={true}
                enablePagination={true}
                maxHeight="600px"
              />
            </>
          )}
        </Box>
      </Paper>

      <Paper sx={{ p: 3 }}>
        <Typography variant="h6" gutterBottom>
          Особенности компонента
        </Typography>
        
        <Typography variant="body2" component="div">
          <strong>Источники данных:</strong><br/>
          • <strong>API</strong> - указать apiUrl для загрузки данных с сервера<br/>
          • <strong>Массив</strong> - передать array с данными для локальной работы<br/><br/>
          
          <strong>Функциональность:</strong><br/>
          • <strong>CRUD операции</strong> - создание, редактирование, удаление записей<br/>
          • <strong>Поиск</strong> - фильтрация данных по всем видимым полям<br/>
          • <strong>Пагинация</strong> - разбивка данных на страницы<br/>
          • <strong>Сортировка</strong> - настраиваемая сортировка через параметры<br/>
          • <strong>Кастомные действия</strong> - дополнительные кнопки в строках<br/><br/>
          
          <strong>Настройки:</strong><br/>
          • <strong>Схема</strong> - определяет структуру таблицы и форм<br/>
          • <strong>Видимость колонок</strong> - через visible в схеме<br/>
          • <strong>Редактируемость полей</strong> - через editable в схеме<br/>
          • <strong>Валидация</strong> - встроенная валидация полей
        </Typography>
      </Paper>
    </Box>
  );
};
