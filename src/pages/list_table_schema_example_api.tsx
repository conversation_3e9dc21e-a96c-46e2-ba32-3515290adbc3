import React from 'react';
import { Box, Typography, Paper } from '@mui/material';
import { ListTableSchema } from '../components_lib/core/schema/ListTableSchema';
import { Subscription, subscriptionSchema } from '@/components_lib/models/subscription';

/**
 * Страница для демонстрации компонента ListTableSchema с API
 */
export const ListTableSchemaExamplePageAPI: React.FC = () => {
  // Обработчик клика по строке (опционально)
  const handleRowClick = (item: Subscription) => {
    console.log('Row clicked:', item);
  };

  return (
    <Box sx={{ p: 3 }}>
      <Typography variant="h4" gutterBottom>
        Пример ListTableSchema с API
      </Typography>

      <Typography variant="body1" sx={{ mb: 3 }}>
        Компонент автоматически работает с API endpoint `/subscriptions` и поддерживает все CRUD операции.
        Все запросы к API выполняются внутри компонента автоматически.
      </Typography>

      <Paper sx={{ p: 2, mb: 3 }}>
        <Typography variant="h6" gutterBottom>
          Особенности работы с API:
        </Typography>
        <Typography variant="body2" component="div">
          • <strong>Автоматические запросы</strong> - все CRUD операции выполняются автоматически<br/>
          • <strong>GET</strong> - загрузка данных при монтировании и после изменений<br/>
          • <strong>POST</strong> - создание новых записей через форму<br/>
          • <strong>PUT</strong> - обновление существующих записей<br/>
          • <strong>DELETE</strong> - удаление записей с подтверждением<br/>
          • <strong>Поиск и пагинация</strong> - передаются как параметры запроса<br/>
          • <strong>Обработка ошибок</strong> - автоматическое логирование в консоль
        </Typography>
      </Paper>

      <ListTableSchema<Subscription>
        schema={subscriptionSchema}
        apiUrl={'/subscriptions'}
        params={{
          order: 'desc',
        }}
        onRowClick={handleRowClick}
        enableSearch={true}
        enablePagination={true}
        enableEdit={true}
        enableDelete={true}
        enableAdd={true}
        maxHeight="600px"
        deleteConfirmTitle="Подтверждение удаления"
        deleteConfirmMessage="Вы уверены, что хотите удалить эту подписку?"
        dialogTitle="Редактирование подписки"
      />
    </Box>
  );
};
