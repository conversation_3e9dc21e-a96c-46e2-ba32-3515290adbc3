import React, { useState } from 'react';
import { Box, Typography, Alert } from '@mui/material';
import { ListTableSchema } from '../components_lib/core/schema/ListTableSchema';
import { Subscription, subscriptionSchema } from '@/components_lib/models/subscription';
import { apiClient } from '@/utils/api';

/**
 * Страница для демонстрации компонента ListTableSchema с API
 */
export const ListTableSchemaExamplePageAPI: React.FC = () => {
  const [error, setError] = useState<string | null>(null);

  // Обработчик создания новой записи
  const handleCreate = async (item: Subscription) => {
    try {
      setError(null);
      console.log('Creating subscription:', item);

      // Для демонстрации - если API недоступен, симулируем успешное создание
      try {
        const result = await apiClient.post('/subscriptions', item);
        console.log('Created subscription via API:', result);
      } catch (apiError) {
        console.warn('API недоступен, симулируем создание:', apiError);
        // Симулируем задержку API
        await new Promise(resolve => setTimeout(resolve, 500));
        console.log('Simulated creation of subscription:', item);
      }
    } catch (error) {
      console.error('Error creating subscription:', error);
      setError('Ошибка при создании записи: ' + (error as Error).message);
      throw error; // Пробрасываем ошибку, чтобы диалог не закрылся
    }
  };

  // Обработчик обновления записи
  const handleUpdate = async (item: Subscription) => {
    try {
      setError(null);
      console.log('Updating subscription:', item);

      try {
        const result = await apiClient.put(`/subscriptions/${item.id}`, item);
        console.log('Updated subscription via API:', result);
      } catch (apiError) {
        console.warn('API недоступен, симулируем обновление:', apiError);
        await new Promise(resolve => setTimeout(resolve, 500));
        console.log('Simulated update of subscription:', item);
      }
    } catch (error) {
      console.error('Error updating subscription:', error);
      setError('Ошибка при обновлении записи: ' + (error as Error).message);
      throw error; // Пробрасываем ошибку, чтобы диалог не закрылся
    }
  };

  // Обработчик удаления записи
  const handleDelete = async (id: number | string) => {
    try {
      setError(null);
      console.log('Deleting subscription with id:', id);

      try {
        await apiClient.delete(`/subscriptions/${id}`);
        console.log('Deleted subscription via API, id:', id);
      } catch (apiError) {
        console.warn('API недоступен, симулируем удаление:', apiError);
        await new Promise(resolve => setTimeout(resolve, 500));
        console.log('Simulated deletion of subscription with id:', id);
      }
    } catch (error) {
      console.error('Error deleting subscription:', error);
      setError('Ошибка при удалении записи: ' + (error as Error).message);
      throw error; // Пробрасываем ошибку, чтобы показать пользователю
    }
  };

  // Обработчик клика по строке
  const handleRowClick = (item: Subscription) => {
    console.log('Row clicked:', item);
  };

  return (
    <Box sx={{ p: 3 }}>
      <Typography variant="h4" gutterBottom>
        Пример ListTableSchema с API
      </Typography>

      <Typography variant="body1" sx={{ mb: 3 }}>
        Компонент работает с API endpoint `/subscriptions` и поддерживает все CRUD операции.
      </Typography>

      {error && (
        <Alert severity="error" sx={{ mb: 2 }} onClose={() => setError(null)}>
          {error}
        </Alert>
      )}

      <ListTableSchema<Subscription>
        schema={subscriptionSchema}
        apiUrl={'/subscriptions'}
        params={{
          order: 'desc',
        }}
        onCreate={handleCreate}
        onUpdate={handleUpdate}
        onDelete={handleDelete}
        onRowClick={handleRowClick}
        enableSearch={true}
        enablePagination={true}
        enableEdit={true}
        enableDelete={true}
        enableAdd={true}
        maxHeight="600px"
        deleteConfirmTitle="Подтверждение удаления"
        deleteConfirmMessage="Вы уверены, что хотите удалить эту подписку?"
        dialogTitle="Редактирование подписки"
      />
    </Box>
  );
};
