import React, { useState } from 'react';
import { Box} from '@mui/material';
import { ListTableSchema } from '../components_lib/core/schema/ListTableSchema';
import { Subscription, subscriptionSchema } from '@/components_lib/models/subscription';

/**
 * Страница для демонстрации компонента ListTableSchema
 */
export const ListTableSchemaExamplePageAPI: React.FC = () => {
  return (
      <Box>
        <ListTableSchema<Subscription>
          schema={subscriptionSchema}
          apiUrl={'/subscriptions'}
          params={{
            order: 'desc',
          }}
        />
      </Box>
    );
};
