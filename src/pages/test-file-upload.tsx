import React, { useState } from 'react';
import {
  Container,
  Typography,
  Box,
  Paper,
  Grid,
  Divider,
  Alert,
} from '@mui/material';
import { FileUpload } from '@/components_lib/core/FileUpload';
import { SimpleFileUpload } from '@/components_lib/core/SimpleFileUpload';

export const TestFileUploadPage: React.FC = () => {
  const [uploadResults, setUploadResults] = useState<any[]>([]);

  const handleUploadComplete = (response: any, file: File) => {
    console.log('Upload completed:', { response, fileName: file.name });
    setUploadResults(prev => [...prev, {
      fileName: file.name,
      response,
      timestamp: new Date().toLocaleTimeString(),
      status: 'success'
    }]);
  };

  const handleUploadError = (error: any, file: File) => {
    console.error('Upload error:', { error, fileName: file.name });
    setUploadResults(prev => [...prev, {
      fileName: file.name,
      error: error.message,
      timestamp: new Date().toLocaleTimeString(),
      status: 'error'
    }]);
  };

  const handleUploadProgress = (progress: number, file: File) => {
    console.log('Upload progress:', { progress, fileName: file.name });
  };

  return (
    <Container maxWidth="lg" sx={{ py: 4 }}>
      <Typography variant="h4" gutterBottom>
        File Upload Components Test
      </Typography>
      
      <Alert severity="info" sx={{ mb: 3 }}>
        This page demonstrates file upload components. 
        Note: Upload endpoints may not exist, so you might see network errors.
      </Alert>

      <Grid container spacing={3}>
        {/* Полнофункциональный компонент */}
        <Grid size={{ xs:12, md:6}} >
          <Paper sx={{ p: 3 }}>
            <Typography variant="h6" gutterBottom>
              Full Featured Upload
            </Typography>
            <Typography variant="body2" color="text.secondary" sx={{ mb: 2 }}>
              Multiple files, drag & drop, progress tracking
            </Typography>
            
            <FileUpload
              url="/api/upload"
              additionalParams={{ 
                category: 'documents',
                userId: 123 
              }}
              onComplete={handleUploadComplete}
              onError={handleUploadError}
              onProgress={handleUploadProgress}
              accept="image/*,.pdf,.doc,.docx"
              maxSize={10 * 1024 * 1024} // 10MB
              multiple={true}
              buttonText="Upload Documents"
              showProgress={true}
              showFileInfo={true}
            />
          </Paper>
        </Grid>

        {/* Простой компонент - кнопка */}
        <Grid size={{ xs:12, md:6}} >
          <Paper sx={{ p: 3 }}>
            <Typography variant="h6" gutterBottom>
              Simple Button Upload
            </Typography>
            <Typography variant="body2" color="text.secondary" sx={{ mb: 2 }}>
              Single file, button interface
            </Typography>
            
            <SimpleFileUpload
              url="/api/upload/avatar"
              additionalParams={{ type: 'avatar' }}
              onComplete={handleUploadComplete}
              onError={handleUploadError}
              onProgress={handleUploadProgress}
              accept="image/*"
              maxSize={5 * 1024 * 1024} // 5MB
              buttonText="Upload Avatar"
              variant="button"
              size="large"
              showProgress={true}
            />
          </Paper>
        </Grid>

        {/* Простой компонент - dropzone */}
        <Grid size={{ xs:12, md:6}} >
          <Paper sx={{ p: 3 }}>
            <Typography variant="h6" gutterBottom>
              Simple Dropzone Upload
            </Typography>
            <Typography variant="body2" color="text.secondary" sx={{ mb: 2 }}>
              Single file, dropzone interface
            </Typography>
            
            <SimpleFileUpload
              url="/api/upload/file"
              additionalParams={{ 
                folder: 'uploads',
                public: true 
              }}
              onComplete={handleUploadComplete}
              onError={handleUploadError}
              onProgress={handleUploadProgress}
              accept=".pdf,.doc,.docx,.txt"
              maxSize={20 * 1024 * 1024} // 20MB
              buttonText="Upload Document"
              variant="dropzone"
              showProgress={true}
            />
          </Paper>
        </Grid>

        {/* Результаты загрузки */}
        <Grid size={{ xs:12, md:6}} >
          <Paper sx={{ p: 3 }}>
            <Typography variant="h6" gutterBottom>
              Upload Results
            </Typography>
            <Typography variant="body2" color="text.secondary" sx={{ mb: 2 }}>
              Real-time upload results and errors
            </Typography>
            
            <Box sx={{ maxHeight: 300, overflow: 'auto' }}>
              {uploadResults.length === 0 ? (
                <Typography variant="body2" color="text.secondary">
                  No uploads yet...
                </Typography>
              ) : (
                uploadResults.map((result, index) => (
                  <Box key={index} sx={{ mb: 1 }}>
                    <Alert 
                      severity={result.status === 'success' ? 'success' : 'error'}
                      sx={{ fontSize: '0.875rem' }}
                    >
                      <Typography variant="body2">
                        <strong>{result.fileName}</strong> - {result.timestamp}
                      </Typography>
                      {result.status === 'success' ? (
                        <Typography variant="caption">
                          Upload successful
                        </Typography>
                      ) : (
                        <Typography variant="caption">
                          Error: {result.error}
                        </Typography>
                      )}
                    </Alert>
                  </Box>
                ))
              )}
            </Box>
          </Paper>
        </Grid>
      </Grid>

      <Divider sx={{ my: 4 }} />

      {/* Документация */}
      <Paper sx={{ p: 3 }}>
        <Typography variant="h6" gutterBottom>
          Component Usage Examples
        </Typography>
        
        <Box component="pre" sx={{ 
          backgroundColor: '#f5f5f5', 
          p: 2, 
          borderRadius: 1,
          fontSize: '0.875rem',
          overflow: 'auto',
          whiteSpace: 'pre-wrap'
        }}>
{`// Full Featured Upload
<FileUpload
  url="/api/upload"
  additionalParams={{ category: 'documents' }}
  onComplete={(response, file) => console.log('Done!', response)}
  onError={(error, file) => console.error('Error!', error)}
  onProgress={(progress, file) => console.log('Progress:', progress)}
  accept="image/*,.pdf"
  maxSize={10 * 1024 * 1024}
  multiple={true}
/>

// Simple Button Upload
<SimpleFileUpload
  url="/api/upload"
  onComplete={(response, file) => console.log('Done!')}
  variant="button"
  buttonText="Upload File"
/>

// Simple Dropzone Upload
<SimpleFileUpload
  url="/api/upload"
  onComplete={(response, file) => console.log('Done!')}
  variant="dropzone"
  buttonText="Drop files here"
/>

// Using the hook directly
const { uploadFile, isUploading, progress, error } = useFileUpload({
  url: '/api/upload',
  onComplete: (response) => console.log('Done!', response)
});`}
        </Box>
      </Paper>
    </Container>
  );
};

export default TestFileUploadPage;
