import { <PERSON>, <PERSON><PERSON>, <PERSON>, Checkbox, Container, <PERSON><PERSON>r, FormControl, FormControlLabel, FormLabel, Link, TextField, Typography } from "@mui/material";
import axios from "axios";
import React from "react";

export default function SignUp() {

    const [emailError, setEmailError] = React.useState(false);
  const [emailErrorMessage, setEmailErrorMessage] = React.useState('');
  const [passwordError, setPasswordError] = React.useState(false);
  const [passwordErrorMessage, setPasswordErrorMessage] = React.useState('');
  const [nameError, setNameError] = React.useState(false);
  const [nameErrorMessage, setNameErrorMessage] = React.useState('');

  const validateInputs = () => {
    const email = document.getElementById('email') as HTMLInputElement;
    const password = document.getElementById('password') as HTMLInputElement;
    const name = document.getElementById('name') as HTMLInputElement;

    let isValid = true;

    if (!email.value || !/\S+@\S+\.\S+/.test(email.value)) {
      setEmailError(true);
      setEmailErrorMessage('Please enter a valid email address.');
      isValid = false;
    } else {
      setEmailError(false);
      setEmailErrorMessage('');
    }

    if (!password.value || password.value.length < 6) {
      setPasswordError(true);
      setPasswordErrorMessage('Password must be at least 6 characters long.');
      isValid = false;
    } else {
      setPasswordError(false);
      setPasswordErrorMessage('');
    }

    if (!name.value || name.value.length < 1) {
      setNameError(true);
      setNameErrorMessage('Name is required.');
      isValid = false;
    } else {
      setNameError(false);
      setNameErrorMessage('');
    }

    return isValid;
  };

  const handleSubmit = (event: React.FormEvent<HTMLFormElement>) => {
    event.preventDefault();
    if (nameError || emailError || passwordError) {
      event.preventDefault();
      return;
    }
    const data = new FormData(event.currentTarget);
    debugger

    console.log({
      name: data.get('name'),
      lastName: data.get('lastName'),
      email: data.get('email'),
      password: data.get('password'),
    });
    debugger

    const formDataObj: any = {};
    data.forEach((value, key) => (formDataObj[key] = value));
    axios.post('/api/v1/auth/register', formDataObj)
    .then((r) => {
        debugger
    })
    .catch((err) => {
        debugger
    })
  };


    return (

        <Box
      sx={{
        minHeight: '100vh',
        display: 'flex',
        flexDirection: 'column',
        justifyContent: 'center',
        alignItems: 'center',
        // ...sx,
      }}
    >
      <Container component="main" maxWidth="xs">
    <Card variant="outlined">
    {/* <SitemarkIcon /> */}
    <Typography
      component="h1"
      variant="h4"
      sx={{ width: '100%', fontSize: 'clamp(2rem, 10vw, 2.15rem)' }}
    >
      Sign up
    </Typography>
    <Box
      component="form"
      onSubmit={handleSubmit}
      sx={{ display: 'flex', flexDirection: 'column', gap: 2 }}
    >
      <FormControl>
        <FormLabel htmlFor="name">Full name</FormLabel>
        <TextField
          autoComplete="name"
          name="name"
          required
          fullWidth
          id="name"
          placeholder="Jon Snow"
          error={nameError}
          helperText={nameErrorMessage}
          color={nameError ? 'error' : 'primary'}
        />
      </FormControl>
      <FormControl>
        <FormLabel htmlFor="email">Email</FormLabel>
        <TextField
          required
          fullWidth
          id="email"
          placeholder="<EMAIL>"
          name="login"
          autoComplete="email"
          variant="outlined"
          error={emailError}
          helperText={emailErrorMessage}
          color={passwordError ? 'error' : 'primary'}
        />
      </FormControl>
      <FormControl>
        <FormLabel htmlFor="password">Password</FormLabel>
        <TextField
          required
          fullWidth
          name="password"
          placeholder="••••••"
          type="password"
          id="password"
          autoComplete="new-password"
          variant="outlined"
          error={passwordError}
          helperText={passwordErrorMessage}
          color={passwordError ? 'error' : 'primary'}
        />
      </FormControl>
      <FormControlLabel
        control={<Checkbox value="allowExtraEmails" color="primary" />}
        label="I want to receive updates via email."
      />
      <Button
        type="submit"
        fullWidth
        variant="contained"
        onClick={validateInputs}
      >
        Sign up
      </Button>
    </Box>
    <Divider>
      <Typography sx={{ color: 'text.secondary' }}>or</Typography>
    </Divider>
    <Box sx={{ display: 'flex', flexDirection: 'column', gap: 2 }}>
      {/* <Button
        fullWidth
        variant="outlined"
        onClick={() => alert('Sign up with Google')}
        startIcon={<GoogleIcon />}
      >
        Sign up with Google
      </Button>
      <Button
        fullWidth
        variant="outlined"
        onClick={() => alert('Sign up with Facebook')}
        startIcon={<FacebookIcon />}
      >
        Sign up with Facebook
      </Button> */}
      <Typography sx={{ textAlign: 'center' }}>
        Already have an account?{' '}
        <Link
          href="/material-ui/getting-started/templates/sign-in/"
          variant="body2"
          sx={{ alignSelf: 'center' }}
        >
          Sign in
        </Link>
      </Typography>
    </Box>
  </Card>
  </Container>
  </Box>
    )
}