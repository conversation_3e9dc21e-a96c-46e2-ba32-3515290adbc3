'use client';
import * as React from 'react';
import { SignInPage } from '@toolpad/core/SignInPage';
import type { Session } from '@toolpad/core/AppProvider';
import { Navigate, useNavigate, useSearchParams } from 'react-router';
import { useSession } from '../../context/SessionContext';
import axios from 'axios';
import { AuthContext } from '@/context/AuthContext';
import { LinearProgress } from '@mui/material';

export default function SignIn() {
  debugger
  const { setSession } = useSession();
  const navigate = useNavigate();
  const [searchParams, setSearchParams] = useSearchParams();
  const {user, userLoading, logIn, isAuth} = React.useContext(AuthContext)

  // const query = new URLSearchParams(props.location.search);

  console.log('searchParams', searchParams)

  if (user && !userLoading && !isAuth) {
    
    return (
      <SignInPage
        providers={[{ id: 'credentials', name: 'Credentials' }]}
        signIn={async (provider, formData, callbackUrl) => {
          try {
            const formDataObj: any = {};
            formData.forEach((value: any, key: any) => (formDataObj[key] = value));
            formDataObj['login'] = formDataObj['email']
            await logIn(formDataObj)
            if(isAuth) {
              navigate(callbackUrl || '/', { replace: true });
              return {}
            }
          } catch (error) {
            return { error: error instanceof Error ? error.message : 'An error occurred' };
          }
          return {};
        }}
      />
    );
  }
  else if (user && isAuth) {
    const redirectTo = '/' //`/sign-in?callbackUrl=${encodeURIComponent(location.pathname)}`;
    return (<Navigate to={redirectTo} replace />)
  } else {
    return (<><LinearProgress /></>)
  }

  
}