import React, { useState } from 'react';
import { Box, Typography, Paper, Alert, Button, Divider } from '@mui/material';
import { FormBySchema } from '../components_lib/FormBySchema';
import { arrayExampleSchema, ArrayExampleData, defaultArrayExampleData } from '../components_lib/models/array_example';

/**
 * Страница для демонстрации работы с новым типом поля 'array'
 */
export const ArrayExamplePage: React.FC = () => {
  const [formData, setFormData] = useState<ArrayExampleData>(defaultArrayExampleData);
  const [submittedData, setSubmittedData] = useState<ArrayExampleData | null>(null);

  const handleFormChange = (values: ArrayExampleData) => {
    setFormData(values);
  };

  const handleFormSubmit = (values: ArrayExampleData) => {
    setSubmittedData(values);
    console.log('Submitted form data:', values);
  };

  const handleReset = () => {
    setFormData(defaultArrayExampleData);
    setSubmittedData(null);
  };

  return (
    <Box sx={{ p: 3, maxWidth: 1200, mx: 'auto' }}>
      <Typography variant="h4" gutterBottom>
        Пример использования типа поля 'array'
      </Typography>
      
      <Typography variant="body1" sx={{ mb: 3 }}>
        Эта страница демонстрирует различные способы работы с массивами в схемах форм:
      </Typography>

      <Alert severity="info" sx={{ mb: 3 }}>
        <Typography variant="body2">
          <strong>Типы ввода массивов:</strong><br/>
          • <strong>text</strong> - ввод через текстовое поле с разделителями<br/>
          • <strong>select</strong> - множественный выбор из списка<br/>
          • <strong>autocomplete</strong> - автоподстановка с множественным выбором
        </Typography>
      </Alert>

      <Paper sx={{ p: 3, mb: 3 }}>
        <Typography variant="h6" gutterBottom>
          Форма с массивами
        </Typography>
        
        <FormBySchema<ArrayExampleData>
          schema={arrayExampleSchema}
          initialValues={formData}
          onChange={handleFormChange}
          onSubmit={handleFormSubmit}
          submitText="Отправить данные"
        />

        <Box sx={{ mt: 2, display: 'flex', gap: 2 }}>
          <Button variant="outlined" onClick={handleReset}>
            Сбросить к значениям по умолчанию
          </Button>
        </Box>
      </Paper>

      <Paper sx={{ p: 3, mb: 3 }}>
        <Typography variant="h6" gutterBottom>
          Текущие значения формы
        </Typography>
        <pre style={{ 
          backgroundColor: '#f5f5f5', 
          padding: '16px', 
          borderRadius: '4px',
          overflow: 'auto',
          fontSize: '14px'
        }}>
          {JSON.stringify(formData, null, 2)}
        </pre>
      </Paper>

      {submittedData && (
        <Paper sx={{ p: 3 }}>
          <Typography variant="h6" gutterBottom color="success.main">
            Отправленные данные
          </Typography>
          <pre style={{ 
            backgroundColor: '#e8f5e8', 
            padding: '16px', 
            borderRadius: '4px',
            overflow: 'auto',
            fontSize: '14px'
          }}>
            {JSON.stringify(submittedData, null, 2)}
          </pre>
        </Paper>
      )}

      <Divider sx={{ my: 4 }} />

      <Paper sx={{ p: 3 }}>
        <Typography variant="h6" gutterBottom>
          Описание полей
        </Typography>
        
        <Typography variant="body2" component="div">
          <strong>Теги</strong> - текстовое поле с разделителем запятая, максимум 10 элементов, без дубликатов<br/>
          <strong>Ключевые слова</strong> - текстовое поле с разделителем точка с запятой, максимум 5 элементов, дубликаты разрешены<br/>
          <strong>Категории</strong> - множественный select, максимум 3 элемента, без дубликатов<br/>
          <strong>Навыки</strong> - autocomplete с множественным выбором, максимум 8 элементов, без дубликатов
        </Typography>
      </Paper>
    </Box>
  );
};
