import React, { useState } from 'react';
import { Box, Typography, Alert, Button, Paper } from '@mui/material';
import { ListTableSchema } from '../components_lib/core/schema/ListTableSchema';
import { SchemaData } from '../components_lib/core/schema/Schema';

// Простая схема для тестирования
const testSchema: SchemaData = {
  id: {
    type: 'number',
    label: 'ID',
    editable: false,
  },
  name: {
    type: 'text',
    label: 'Название',
    required: true,
  },
  email: {
    type: 'text',
    label: 'Email',
    validation: {
      pattern: /^[^\s@]+@[^\s@]+\.[^\s@]+$/,
      message: 'Неверный формат email'
    }
  },
  status: {
    type: 'select',
    label: 'Статус',
    options: [
      { value: 'active', label: 'Активный' },
      { value: 'inactive', label: 'Неактивный' },
      { value: 'pending', label: 'Ожидает' }
    ],
    defaultValue: 'active'
  },
  createdAt: {
    type: 'date',
    label: 'Дата создания',
    editable: false,
  },
  isActive: {
    type: 'boolean',
    label: 'Активен',
    defaultValue: true
  }
};

interface TestItem {
  id?: number;
  name: string;
  email: string;
  status: string;
  createdAt: string;
  isActive: boolean;
}

// Мок API для тестирования
class MockAPI {
  private static data: TestItem[] = [
    {
      id: 1,
      name: 'Иван Иванов',
      email: '<EMAIL>',
      status: 'active',
      createdAt: '2024-01-15',
      isActive: true
    },
    {
      id: 2,
      name: 'Мария Петрова',
      email: '<EMAIL>',
      status: 'pending',
      createdAt: '2024-01-20',
      isActive: false
    },
    {
      id: 3,
      name: 'Алексей Сидоров',
      email: '<EMAIL>',
      status: 'active',
      createdAt: '2024-01-25',
      isActive: true
    }
  ];

  static async get(url: string, params: any) {
    console.log('MockAPI.get:', { url, params });
    
    // Симулируем задержку API
    await new Promise(resolve => setTimeout(resolve, 500));
    
    let filteredData = [...this.data];
    
    // Применяем поиск
    if (params.q) {
      filteredData = filteredData.filter(item => 
        Object.values(item).some(value => 
          String(value).toLowerCase().includes(params.q.toLowerCase())
        )
      );
    }
    
    const total = filteredData.length;
    
    // Применяем пагинацию
    const page = params.page || 1;
    const limit = params.limit || 100;
    const startIndex = (page - 1) * limit;
    const endIndex = startIndex + limit;
    
    const items = filteredData.slice(startIndex, endIndex);
    
    return {
      items,
      count: total,
      page,
      limit
    };
  }

  static async post(url: string, data: TestItem) {
    console.log('MockAPI.post:', { url, data });
    
    await new Promise(resolve => setTimeout(resolve, 500));
    
    const newItem = {
      ...data,
      id: Math.max(...this.data.map(item => item.id || 0)) + 1,
      createdAt: new Date().toISOString().split('T')[0]
    };
    
    this.data.push(newItem);
    console.log('MockAPI.post result:', newItem);
    
    return newItem;
  }

  static async put(url: string, data: TestItem) {
    console.log('MockAPI.put:', { url, data });
    
    await new Promise(resolve => setTimeout(resolve, 500));
    
    const index = this.data.findIndex(item => item.id === data.id);
    if (index !== -1) {
      this.data[index] = data;
    }
    
    return data;
  }

  static async delete(url: string) {
    console.log('MockAPI.delete:', { url });
    
    await new Promise(resolve => setTimeout(resolve, 500));
    
    const id = parseInt(url.split('/').pop() || '0');
    this.data = this.data.filter(item => item.id !== id);
    
    return { success: true };
  }
}

/**
 * Тестовая страница для ListTableSchema с мок API
 */
export const ListTableSchemaTestPage: React.FC = () => {
  const [error, setError] = useState<string | null>(null);
  const [success, setSuccess] = useState<string | null>(null);

  // Обработчик создания новой записи
  const handleCreate = async (item: TestItem) => {
    try {
      setError(null);
      setSuccess(null);
      console.log('Creating item:', item);
      
      const result = await MockAPI.post('/test-items', item);
      console.log('Created item:', result);
      setSuccess('Запись успешно создана!');
    } catch (error) {
      console.error('Error creating item:', error);
      setError('Ошибка при создании записи: ' + (error as Error).message);
      throw error;
    }
  };

  // Обработчик обновления записи
  const handleUpdate = async (item: TestItem) => {
    try {
      setError(null);
      setSuccess(null);
      console.log('Updating item:', item);
      
      const result = await MockAPI.put(`/test-items/${item.id}`, item);
      console.log('Updated item:', result);
      setSuccess('Запись успешно обновлена!');
    } catch (error) {
      console.error('Error updating item:', error);
      setError('Ошибка при обновлении записи: ' + (error as Error).message);
      throw error;
    }
  };

  // Обработчик удаления записи
  const handleDelete = async (id: number | string) => {
    try {
      setError(null);
      setSuccess(null);
      console.log('Deleting item with id:', id);
      
      await MockAPI.delete(`/test-items/${id}`);
      console.log('Deleted item with id:', id);
      setSuccess('Запись успешно удалена!');
    } catch (error) {
      console.error('Error deleting item:', error);
      setError('Ошибка при удалении записи: ' + (error as Error).message);
      throw error;
    }
  };

  // Обработчик клика по строке
  const handleRowClick = (item: TestItem) => {
    console.log('Row clicked:', item);
  };

  // Мок функция для получения данных
  const mockFetchData = async (url: string, params: any) => {
    return MockAPI.get(url, params);
  };

  // Переопределяем apiClient для этого компонента
  React.useEffect(() => {
    // Сохраняем оригинальный apiClient
    const originalApiClient = (window as any).originalApiClient;
    if (!originalApiClient) {
      (window as any).originalApiClient = require('@/utils/api').apiClient;
    }
    
    // Подменяем apiClient на мок
    const mockApiClient = {
      get: MockAPI.get,
      post: MockAPI.post,
      put: MockAPI.put,
      delete: MockAPI.delete,
    };
    
    // Заменяем в модуле
    require('@/utils/api').apiClient = mockApiClient;
    
    return () => {
      // Восстанавливаем оригинальный apiClient при размонтировании
      if ((window as any).originalApiClient) {
        require('@/utils/api').apiClient = (window as any).originalApiClient;
      }
    };
  }, []);

  const clearMessages = () => {
    setError(null);
    setSuccess(null);
  };

  return (
    <Box sx={{ p: 3 }}>
      <Typography variant="h4" gutterBottom>
        Тест ListTableSchema с мок API
      </Typography>
      
      <Typography variant="body1" sx={{ mb: 3 }}>
        Этот пример использует мок API для демонстрации работы ListTableSchema.
        Все операции CRUD работают с локальными данными.
      </Typography>

      {error && (
        <Alert severity="error" sx={{ mb: 2 }} onClose={clearMessages}>
          {error}
        </Alert>
      )}

      {success && (
        <Alert severity="success" sx={{ mb: 2 }} onClose={clearMessages}>
          {success}
        </Alert>
      )}

      <Paper sx={{ p: 2, mb: 2 }}>
        <Typography variant="h6" gutterBottom>
          Инструкции:
        </Typography>
        <Typography variant="body2" component="div">
          1. Нажмите кнопку "+" для создания новой записи<br/>
          2. Нажмите кнопку редактирования (карандаш) для изменения записи<br/>
          3. Нажмите кнопку удаления (корзина) для удаления записи<br/>
          4. Используйте поиск для фильтрации данных<br/>
          5. Проверьте консоль браузера для подробных логов
        </Typography>
      </Paper>

      <ListTableSchema
        schema={testSchema}
        apiUrl="/test-items"
        params={{
          order: 'desc',
        }}
        onCreate={handleCreate}
        onUpdate={handleUpdate}
        onDelete={handleDelete}
        onRowClick={handleRowClick}
        enableSearch={true}
        enablePagination={true}
        enableEdit={true}
        enableDelete={true}
        enableAdd={true}
        maxHeight="600px"
        deleteConfirmTitle="Подтверждение удаления"
        deleteConfirmMessage="Вы уверены, что хотите удалить эту запись?"
        dialogTitle="Редактирование записи"
      />
    </Box>
  );
};
