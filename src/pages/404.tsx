import { Box, Button, Typography } from "@mui/material";
import { Link, useNavigate } from "react-router";

export function page404 () {

    let navigate = useNavigate();
    const routeChange = () =>{
        let path = `/`;
        navigate(path);
    }
    return (
        <Box
            sx={{
                display: 'flex',
                justifyContent: 'center',
                alignItems: 'center',
                flexDirection: 'column',
                minHeight: '100vh',
            }}
            >
            <Typography variant="h1" >404</Typography>
            <Typography variant="h6" >The page you’re looking for doesn’t exist.</Typography>
            <Button variant="contained" onClick={routeChange}>Back Home</Button>
        </Box>
    )
}