import React, { useRef, useState } from 'react';
import {
  Container,
  Typography,
  Box,
  Paper,
  Button,
  Grid,
  Alert,
  Chip,
  Divider,
} from '@mui/material';
import { ListTableSchema, ListTableSchemaRef } from '@/components_lib/core/schema/ListTableSchema';
import { subscriptionSchema } from '@/components_lib/models/subscription';

export const TestListTableRefPage: React.FC = () => {
  const tableRef = useRef<ListTableSchemaRef>(null);
  const [stats, setStats] = useState<any>(null);
  const [data, setData] = useState<any[]>([]);

  // Тестовые данные
  const testData = [
    {
      id: 1,
      title: 'Basic Plan',
      price: 99.99,
      price_type: 'Monthly',
      test: {
        id: 'service-1',
        title: 'Web Development',
        price: 150
      },
      test1: {
        id: 2,
        name: 'Option 2'
      }
    },
    {
      id: 2,
      title: 'Premium Plan',
      price: 199.99,
      price_type: 'Yearly',
      test: {
        id: 'service-2',
        title: 'Mobile Development',
        price: 200
      },
      test1: {
        id: 3,
        name: 'Option 3'
      }
    },
    {
      id: 3,
      title: 'Enterprise Plan',
      price: 499.99,
      price_type: 'Monthly',
      test: null,
      test1: {
        id: 1,
        name: 'Option 1'
      }
    }
  ];

  const handleRefreshData = async () => {
    if (tableRef.current) {
      await tableRef.current.refreshData();
      console.log('Data refreshed!');
    }
  };

  const handleGetData = () => {
    if (tableRef.current) {
      const currentData = tableRef.current.getData();
      setData(currentData);
      console.log('Current data:', currentData);
    }
  };

  const handleGetStats = () => {
    if (tableRef.current) {
      const currentStats = tableRef.current.getStats();
      setStats(currentStats);
      console.log('Current stats:', currentStats);
    }
  };

  const handleClearSearch = () => {
    if (tableRef.current) {
      tableRef.current.clearSearch();
      console.log('Search cleared!');
    }
  };

  const handleSetSearch = () => {
    if (tableRef.current) {
      tableRef.current.setSearch('Premium');
      console.log('Search set to "Premium"');
    }
  };

  const handleGoToPage = () => {
    if (tableRef.current) {
      tableRef.current.goToPage(1);
      console.log('Navigated to page 2');
    }
  };

  return (
    <Container maxWidth="lg" sx={{ py: 4 }}>
      <Typography variant="h4" gutterBottom>
        ListTableSchema External API Test
      </Typography>
      
      <Alert severity="info" sx={{ mb: 3 }}>
        This page demonstrates the external API methods available through ref.
        Use the buttons below to interact with the table programmatically.
      </Alert>

      <Grid container spacing={3}>
        {/* Control Panel */}
        <Grid size={{xs:12, md:4}} >
          <Paper sx={{ p: 3 }}>
            <Typography variant="h6" gutterBottom>
              External API Controls
            </Typography>
            
            <Box sx={{ display: 'flex', flexDirection: 'column', gap: 2 }}>
              <Button 
                variant="contained" 
                onClick={handleRefreshData}
                fullWidth
              >
                Refresh Data
              </Button>
              
              <Button 
                variant="outlined" 
                onClick={handleGetData}
                fullWidth
              >
                Get Current Data
              </Button>
              
              <Button 
                variant="outlined" 
                onClick={handleGetStats}
                fullWidth
              >
                Get Statistics
              </Button>
              
              <Divider />
              
              <Button 
                variant="outlined" 
                onClick={handleClearSearch}
                fullWidth
              >
                Clear Search
              </Button>
              
              <Button 
                variant="outlined" 
                onClick={handleSetSearch}
                fullWidth
              >
                Search "Premium"
              </Button>
              
              <Button 
                variant="outlined" 
                onClick={handleGoToPage}
                fullWidth
              >
                Go to Page 2
              </Button>
            </Box>
          </Paper>

          {/* Statistics Display */}
          {stats && (
            <Paper sx={{ p: 3, mt: 2 }}>
              <Typography variant="h6" gutterBottom>
                Current Statistics
              </Typography>
              <Box sx={{ display: 'flex', flexDirection: 'column', gap: 1 }}>
                <Chip label={`Total: ${stats.total}`} variant="outlined" />
                <Chip label={`Displayed: ${stats.displayed}`} variant="outlined" />
                <Chip label={`Page: ${stats.page + 1}`} variant="outlined" />
                <Chip label={`Per Page: ${stats.rowsPerPage}`} variant="outlined" />
                <Chip label={`Search: "${stats.searchTerm}"`} variant="outlined" />
                <Chip 
                  label={`Loading: ${stats.loading ? 'Yes' : 'No'}`} 
                  color={stats.loading ? 'warning' : 'success'}
                />
              </Box>
            </Paper>
          )}

          {/* Data Display */}
          {data.length > 0 && (
            <Paper sx={{ p: 3, mt: 2 }}>
              <Typography variant="h6" gutterBottom>
                Current Data ({data.length} items)
              </Typography>
              <Box sx={{ maxHeight: 200, overflow: 'auto' }}>
                {data.map((item, index) => (
                  <Alert key={index} severity="info" sx={{ mb: 1 }}>
                    <Typography variant="body2">
                      <strong>ID:</strong> {item.id} | <strong>Title:</strong> {item.title}
                    </Typography>
                  </Alert>
                ))}
              </Box>
            </Paper>
          )}
        </Grid>

        {/* Table */}
        <Grid size={{xs: 12, md: 8}} >
          <Paper sx={{ p: 3 }}>
            <Typography variant="h6" gutterBottom>
              Array Data Table with External API
            </Typography>
            
            <ListTableSchema
              ref={tableRef}
              schema={subscriptionSchema}
              array={testData}
              enableEdit={true}
              enableDelete={true}
              enableAdd={true}
              enablePagination={true}
              enableSearch={true}
            />
          </Paper>
        </Grid>
      </Grid>

      <Divider sx={{ my: 4 }} />

      {/* Usage Examples */}
      <Paper sx={{ p: 3 }}>
        <Typography variant="h6" gutterBottom>
          External API Usage Examples
        </Typography>
        
        <Box component="pre" sx={{ 
          backgroundColor: '#f5f5f5', 
          p: 2, 
          borderRadius: 1,
          fontSize: '0.875rem',
          overflow: 'auto',
          whiteSpace: 'pre-wrap'
        }}>
{`// 1. Create ref
const tableRef = useRef<ListTableSchemaRef>(null);

// 2. Use ref with component
<ListTableSchema
  ref={tableRef}
  schema={schema}
  apiUrl="/api/data"
  // ... other props
/>

// 3. Call external methods
// Refresh data
await tableRef.current?.refreshData();

// Get current data
const data = tableRef.current?.getData();

// Get statistics
const stats = tableRef.current?.getStats();
// Returns: { total, displayed, page, rowsPerPage, searchTerm, loading }

// Control search
tableRef.current?.clearSearch();
tableRef.current?.setSearch('search term');

// Navigate
tableRef.current?.goToPage(2);`}
        </Box>
      </Paper>
    </Container>
  );
};

export default TestListTableRefPage;
