import React, { useState } from 'react';
import { Container, Typography, Box, Paper } from '@mui/material';
import { SchemaFieldInput } from '@/components_lib/core/schema/SchemaFieldInput';
import { subscriptionSchema } from '@/components_lib/models/subscription';

export const TestAutocompletePage: React.FC = () => {
  const [values, setValues] = useState<any>({});

  const handleChange = (fieldKey: string, value: any) => {
    console.log('🔄 Field change:', { fieldKey, value });
    setValues(prev => ({
      ...prev,
      [fieldKey]: value
    }));
  };

  return (
    <Container maxWidth="md" sx={{ py: 4 }}>
      <Typography variant="h4" gutterBottom>
        Test Autocomplete
      </Typography>
      
      <Paper sx={{ p: 3, mb: 3 }}>
        <Typography variant="h6" gutterBottom>
          API Autocomplete (Services)
        </Typography>
        <SchemaFieldInput
          fieldKey="test"
          field={subscriptionSchema.test}
          value={values.test}
          values={values}
          onChange={handleChange}
        />
      </Paper>

      <Paper sx={{ p: 3, mb: 3 }}>
        <Typography variant="h6" gutterBottom>
          Static Autocomplete (Test1)
        </Typography>
        <SchemaFieldInput
          fieldKey="test1"
          field={subscriptionSchema.test1}
          value={values.test1}
          values={values}
          onChange={handleChange}
        />
      </Paper>

      <Paper sx={{ p: 3 }}>
        <Typography variant="h6" gutterBottom>
          Current Values:
        </Typography>
        <Box component="pre" sx={{ 
          backgroundColor: '#f5f5f5', 
          p: 2, 
          borderRadius: 1,
          fontSize: '0.875rem',
          overflow: 'auto'
        }}>
          {JSON.stringify(values, null, 2)}
        </Box>
      </Paper>
    </Container>
  );
};

export default TestAutocompletePage;
