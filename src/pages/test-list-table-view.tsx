import React from 'react';
import { Container, Typography, Box, Paper } from '@mui/material';
import { ListTableSchema } from '@/components_lib/core/schema/ListTableSchema';
import { subscriptionSchema } from '@/components_lib/models/subscription';

export const TestListTableViewPage: React.FC = () => {
  // Тестовые данные с разными типами полей
  const testData = [
    {
      id: 1,
      title: 'Basic Plan',
      price: 99.99,
      price_type: 'Monthly',
      test: {
        id: 'service-1',
        title: 'Web Development',
        price: 150
      },
      test1: {
        id: 2,
        name: 'Option 2'
      }
    },
    {
      id: 2,
      title: 'Premium Plan',
      price: 199.99,
      price_type: 'Yearly',
      test: {
        id: 'service-2',
        title: 'Mobile Development',
        price: 200
      },
      test1: {
        id: 3,
        name: 'Option 3'
      }
    },
    {
      id: 3,
      title: 'Enterprise Plan',
      price: 499.99,
      price_type: 'Monthly',
      test: null, // Тест с пустым значением
      test1: {
        id: 1,
        name: 'Option 1'
      }
    }
  ];

  return (
    <Container maxWidth="lg" sx={{ py: 4 }}>
      <Typography variant="h4" gutterBottom>
        Test ListTableSchema with SchemaFieldViewContainer
      </Typography>
      
      <Paper sx={{ p: 3, mb: 3 }}>
        <Typography variant="h6" gutterBottom>
          Array Data (using SchemaFieldViewContainer for display)
        </Typography>
        <ListTableSchema
          schema={subscriptionSchema}
          array={testData}
          enableEdit={true}
          enableDelete={true}
          enableAdd={true}
          enablePagination={false}
        />
      </Paper>

      <Paper sx={{ p: 3, mb: 3 }}>
        <Typography variant="h6" gutterBottom>
          API Data (using SchemaFieldViewContainer for display)
        </Typography>
        <ListTableSchema
          schema={subscriptionSchema}
          apiUrl="/subscriptions"
          enableEdit={true}
          enableDelete={true}
          enableAdd={true}
          enablePagination={true}
        />
      </Paper>

      <Paper sx={{ p: 3 }}>
        <Typography variant="h6" gutterBottom>
          Schema Information:
        </Typography>
        <Box component="pre" sx={{ 
          backgroundColor: '#f5f5f5', 
          p: 2, 
          borderRadius: 1,
          fontSize: '0.875rem',
          overflow: 'auto',
          maxHeight: '300px'
        }}>
          {JSON.stringify(subscriptionSchema, null, 2)}
        </Box>
      </Paper>
    </Container>
  );
};

export default TestListTableViewPage;
