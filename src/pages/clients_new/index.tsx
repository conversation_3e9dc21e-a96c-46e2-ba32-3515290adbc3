import React from 'react';
import { Box, Typography } from '@mui/material';
import { TableByApi } from '@/components_lib/TableByApi';
import { Client, clientListSchema } from '@/components_lib/models/client';
import { useLocation, useNavigate } from 'react-router-dom';
import {
  Routes,
  Route,
  Link,
  useSearchParams,
} from 'react-router';

export const ClientsListNew: React.FC = () => {
  const navigate = useNavigate();
  // const { search } = useLocation();
  // const history = useHistouseSearchParamsry();
   const [search, setSearch] = useSearchParams();
    const location = useLocation();

  const handleRowClick = (item: Client) => {
    navigate(`/clients/${item.id}`);
  };

  const handleCreateClick = () => {
    navigate(`/clients/new`);
  };

  const handleChangeParam = (params: any) => {
    // console.info('list params -> ' , params)
    // setSearch(params)
  };



  return (
    <Box>
      {/* <Typography variant="h5" gutterBottom>
        Clients
      </Typography> */}
      <Box>
        <TableByApi<Client>
          schema={clientListSchema}
          apiUrl="/clients"
          params={{
            order: 'desc',
          }}
          enableEdit={false}
          enableDelete={false}
          // enableAdd={false}
          onRowClick={handleRowClick}
          onCreateClick={handleCreateClick}
          onChangeParams={handleChangeParam}
        />
      </Box>
    </Box>
  );
};