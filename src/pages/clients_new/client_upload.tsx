import React from 'react';
import { Box, Typography } from '@mui/material';
import { TableByApi } from '@/components_lib/TableByApi';
import { Service, serviceSchema } from '@/components_lib/models/service';
import { SchemaData } from '@/components_lib/core/schema/Schema';
import { ListTableSchema } from '@/components_lib/core/schema';
import { ClientUpload, clientUploadTableSchema } from '@/components_lib/models/client_upload';

interface PageSettingsTableApiParams {
  schema: SchemaData,
  apiUrl: string
}

export const ClientUploadPage = () => {
  return (
    <Box>
      <ListTableSchema<ClientUpload>
        schema={clientUploadTableSchema}
        apiUrl={'/client_uploads'}
        params={{
          order: 'desc',
        }}
      />

      {/* <TableByApi<T>
        schema={schema}
        apiUrl={apiUrl}
        params={{
          order: 'desc',
        }}
      /> */}
    </Box>
  );
} 