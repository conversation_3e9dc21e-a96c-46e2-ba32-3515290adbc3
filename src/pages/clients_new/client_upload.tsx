import React from 'react';
import { Box, Typography } from '@mui/material';
import { TableByApi } from '@/components_lib/TableByApi';
import { Service, serviceSchema } from '@/components_lib/models/service';
import { SchemaData } from '@/components_lib/core/schema/Schema';
import { ListTableSchema } from '@/components_lib/core/schema';
import { ClientUpload, clientUploadTableSchema } from '@/components_lib/models/client_upload';
import { GlobalFileUpload } from '@/components_lib/core';

interface PageSettingsTableApiParams {
  schema: SchemaData,
  apiUrl: string
}

export const ClientUploadPage = () => {

  const handleUploadComplete = (response: any, file: File) => {
    console.log('Upload completed:', { response, fileName: file.name });
  };

  const handleUploadError = (error: any, file: File) => {
    console.error('Upload error:', { error, fileName: file.name });
  };

  return (
    <Box>
      <GlobalFileUpload
                        url="/client_uploads"
                        // additionalParams={{ folder: 'uploads' }}
                        onComplete={handleUploadComplete}
                        onError={handleUploadError}
                        accept="image/*,.pdf,.doc,.docx,.txt"
                        maxSize={20 * 1024 * 1024} // 20MB
                        multiple={true}
                        buttonText="Drop files here or click to upload"
                        variant="dropzone"
                      />
      <ListTableSchema<ClientUpload>
        schema={clientUploadTableSchema}
        apiUrl={'/client_uploads'}
        params={{
          order: 'desc',
        }}
        enableAdd={false}
      />

      {/* <TableByApi<T>
        schema={schema}
        apiUrl={apiUrl}
        params={{
          order: 'desc',
        }}
      /> */}
    </Box>
  );
} 