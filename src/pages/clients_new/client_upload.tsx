import React, { useEffect } from 'react';
import { <PERSON><PERSON>, Box, Button, CircularProgress, Dialog, DialogActions, DialogContent, DialogTitle, IconButton, LinearProgress, Stack, Typography } from '@mui/material';
import { TableByApi } from '@/components_lib/TableByApi';
import { Service, serviceSchema } from '@/components_lib/models/service';
import { SchemaData } from '@/components_lib/core/schema/Schema';
import { ListTableSchema, SchemaGridEdit, SchemaGridView } from '@/components_lib/core/schema';
import { ClientUpload, clientUploadTableSchema, clientUploadViewSchema } from '@/components_lib/models/client_upload';
import { GlobalFileUpload, ListTableSchemaRef } from '@/components_lib/core';
import { apiClient } from '@/utils/api';
import { Client } from '@/components_lib/models/client';
import { useNavigate } from 'react-router-dom';
import PreviewIcon from '@mui/icons-material/Preview';
import EditIcon from '@mui/icons-material/Edit';

interface PageSettingsTableApiParams {
  schema: SchemaData,
  apiUrl: string
}
const apiUrl = '/client_uploads'
const apiParams = {
          order: 'desc',
        }

export const ClientUploadPage = () => {


  const navigate = useNavigate();
  const tableRef = React.useRef<ListTableSchemaRef>(null);
  const [selectedRow, setSelectedRow] = React.useState<any>(null);
  const [clientDialogOpen, setClientDialogOpen] = React.useState(false);
  const [error, setError] = React.useState<any>(null)
  const [loading, setLoading] = React.useState(false)
  const [loadingBase, setLoadingBase] = React.useState(false)
  const [readonly, setReadonly] = React.useState(true)
  const [existData, setExistData] = React.useState<any | Client>(null)
  const [newData, setNewData] = React.useState<any | Client>(null)


  const handleUploadComplete = async (response: any, file: File) => {
    console.log('Upload completed:', { response, fileName: file.name });
    await tableRef.current?.refreshData()
    const rows = tableRef.current?.getData()
    debugger
    if(rows && rows.length)
      rows[0].status = 'test'

  };

  const handleUploadError = (error: any, file: File) => {
    console.error('Upload error:', { error, fileName: file.name });
  };

  const onRowClick = (row: any)=> {
    setSelectedRow(row)
    setClientDialogOpen(true)
  }

  const getData = async (id: any) => {
    try{
      if(selectedRow.ex)
      setLoadingBase(true)
      let res = await apiClient.get<ClientUpload>(`${apiUrl}/${id}` )
      setExistData(res.client)
      setNewData(res.exist ? res.client_output : res.output)
      setLoadingBase(false)
      // debugger
    }
    catch(err) {
      debugger

    }
  }

  useEffect(()=> {
    if(selectedRow) {
      getData(selectedRow.id)
    } else {
      setExistData(null)
      setNewData(null)
    }
  }, [selectedRow])

  const handleSaveClient = async (values: any) => {
    setLoading(true)
    try {
      let data: any = {
        ...selectedRow.output, 
        internal_data_source:{
          "type": "client_upload",
          "id": selectedRow.id+''
          } 
        }
      
        let res = await apiClient.save<Client>(`/clients`, data )
        if(res.id && !data?.id) {
          navigate(`/clients/${res.id}`, { replace: true })
        }
        handleCancelClient()
      } catch (err: any) {
        // debugger
        setError(err?.response?.data?.msg ? err?.response?.data?.msg : err.message)
      }
    setLoading(false)
  }

  const handleCancelClient = (): void  =>{
    setClientDialogOpen(false)
    setSelectedRow(null)  
    setError(null)
  }

  let ts: any;
  const onChangeData = (row: any[]) => {
    let aRows = row.filter(r => r.status !== 'SUCCESS' && r.status !== 'ERROR')
    if(aRows.length) {
      if (ts) clearTimeout(ts);
      ts = setTimeout(() => {
        tableRef.current?.refreshData()
      }, 5000);
      
    }
  }

  return (
    <Box>
      <Box sx={{ mb: 3 }}>
        <GlobalFileUpload
          url={apiUrl}
          // additionalParams={{ folder: 'uploads' }}
          onComplete={handleUploadComplete}
          onError={handleUploadError}
          // accept="image/*,.pdf,.doc,.docx,.txt"
          maxSize={20 * 1024 * 1024} // 20MB
          multiple={true}
          buttonText="Drop files here or click to upload"
          variant="dropzone"
        />
      </Box>
      <ListTableSchema
        ref={tableRef}
        schema={clientUploadTableSchema}
        apiUrl={apiUrl}
        params={apiParams}
        enableAdd={false}
        onRowClick={onRowClick}
        onChangeData={onChangeData}
        enableEdit={false}
      />
      <Dialog
        open={clientDialogOpen}
        onClose={handleCancelClient}
        maxWidth="lg"
        fullWidth
      >
        <Stack
          direction="row"
          spacing={2}
          sx={{
            justifyContent: "space-between",
            alignItems: "center",
          }}
        >
          <DialogTitle>Client info</DialogTitle>
          <Box component='div' sx={{px: 3}}>
            { readonly 
              ? <Button variant="outlined" onClick={() => {setReadonly(false)}} startIcon={<EditIcon />}> Edit </Button>  
              : <Button variant="outlined" onClick={() => {setReadonly(true)}} startIcon={<PreviewIcon />}> Preview </Button>
              }
          </Box>
        </Stack>
        
        
        <DialogContent sx={{position:'relative'}}>
          {loadingBase && (
            <Box
              sx={{
                position: 'absolute',
                top: 0,
                left: 0,
                right: 0,
                zIndex: 1
              }}
            >
              <LinearProgress />
            </Box>
          )}
          {newData && 
          <SchemaGridEdit
            schema={clientUploadViewSchema}
            initialValues={newData}
            baseValues={existData}
            actions={false}
            readonly={readonly}
            onChange={(values) => {
              // debugger
              console.log(values)
            }}
          />
          }
          {/* <pre>
            {JSON.stringify(clientUploadViewSchema, null, 2)}
          </pre>
          <pre>
            {JSON.stringify(selectedRow, null, 2)}
          </pre> */}

        </DialogContent>
        <DialogActions>
          <Stack>
            {error && (
                  <Alert severity="error" sx={{ mb: 2 }} onClose={() => setError(null)}>
                    {error}
                  </Alert>
                )}
            <Box sx={{ mt: 3, display: 'flex', gap: 2, justifyContent: 'flex-end' }}>
              <Button onClick={handleCancelClient} disabled={loading}>Cancel</Button>
              <Button onClick={handleSaveClient}  variant="contained" disabled={loading}>
                {loading ? <CircularProgress size={24} /> : 'Create'}
              </Button>
            </Box>
          </Stack>
          
        </DialogActions>
      </Dialog>
      
    </Box>
  );
} 