import { SchemaGridEdit } from "@/components_lib/core"
import { Client } from "@/components_lib/models/client"
import { clientUploadViewSchema } from "@/components_lib/models/client_upload"
import { <PERSON>, <PERSON><PERSON>, <PERSON>ack, Typography, Chip } from "@mui/material"
import React, { useCallback, useMemo } from "react"
import PreviewIcon from '@mui/icons-material/Preview';
import EditIcon from '@mui/icons-material/Edit';
import { useDebounce } from "@/hooks/useDebounce";

const res: any = {"id":16,"manager_id":"a8b00550-7c6c-4878-b106-3ca0d5e86ce9","detection_id":78,"detection_item_id":342,"output":{"accounting_method":"Cash","active_since":"2017-04-12","addresses":[{"address_type":"Tax return Address","renewal_date":null,"phone":null,"paid_by":null,"note":null,"address":{"street":"233 Needham St, Suite 540","city":"Newton","state":"MA","zip":"02464","country":"USA","pobox":null,"full_address":"233 Needham St, Suite 540, Newton MA 02464, USA"}},{"address_type":"Mailing Address","renewal_date":null,"phone":null,"paid_by":null,"note":null,"address":{"street":"2464 Massachusetts Ave, Suite 220","city":"Cambridge","state":"MA","zip":"02140","country":"USA","pobox":null,"full_address":"2464 Massachusetts Ave, Suite 220, Cambridge MA 02140, USA"}}],"agr_signed":null,"agreement_sum":null,"bank_accounts":[{"bank_name":"Bank Of America","aba_number":"*********","account_number":"************","bank_contact":null,"controlled_by":null,"date_opened":null,"last_renewal":null,"notes":null,"authorized_signers":[],"bank_cards":[]},{"bank_name":"Bank Of America, NA","aba_number":"*********","account_number":"************","bank_contact":null,"controlled_by":null,"date_opened":null,"last_renewal":null,"notes":"Old account - closed","authorized_signers":[],"bank_cards":[]}],"billing_method":null,"business_model":"Hardware","bookkeeping":null,"capitalization_table":[{"share_amount":null,"date":null,"issued_percentage":100,"authorized_percentage":null,"notes":null,"person":{"firstname":"Pavel","lastname":"Savin","email":null,"phone":null,"contact_info":null,"citizenship":"Russia","address":"Cliff And Park, 88/15 Moo 3, T.Najomtien, A.Sattahip, Chonburi, Thailand","companies":null}}],"company_phone":null,"contacts":[{"position":"Director","email":null,"phone":null,"contact_info":null,"note":null,"person":{"firstname":"Pavel","lastname":"Savin","email":null,"phone":null,"contact_info":null,"citizenship":"Russia","address":"Cliff And Park, 88/15 Moo 3, T.Najomtien, A.Sattahip, Chonburi, Thailand","companies":null}},{"position":"CEO","email":null,"phone":null,"contact_info":null,"note":null,"person":{"firstname":"Pavel","lastname":"Savin","email":null,"phone":null,"contact_info":null,"citizenship":"Russia","address":"Cliff And Park, 88/15 Moo 3, T.Najomtien, A.Sattahip, Chonburi, Thailand","companies":null}}],"cpa":null,"description":"Development, manufacture and sale of hardware for industrial applications","dissolution_date":null,"ein":"82-1274238","fedtaxforms":null,"financial_year_end":"Dec.","financial_year_end_for_subsidiary":"Dec.","incorp_by":null,"legal_ent_type":"C-CORP","llc_shareholders":[],"monthly_bill":null,"name":"Redwood Technology Inc","naicscode":"541519","notes_accounting":null,"notes_address":null,"notes_agreement":null,"notes_contacts":null,"notes_main":"Company name changed from Easycase to Redwood Technologies Inc filed Sept 14, 2020.","notes_shareholders":null,"optional_share_count":null,"paid_by":null,"paid_by_mail":null,"payment_cards":[],"payment_services":[],"payroll":false,"primary_registration":{"registered_date":"2017-04-12","terminated_date":null,"last_renewal_date":null,"annual_compliance_due_date":null,"state_of_incorporation":"DE","billed_to":null,"last_soi_filed":null,"state_entity":"6377166","notes":null,"registered_agent":null},"renewal_date":null,"renewal_date_mail":null,"secondary_registrations":[{"registered_date":"2020-12-24","terminated_date":null,"last_renewal_date":null,"annual_compliance_due_date":null,"state_of_incorporation":"MA","billed_to":null,"last_soi_filed":null,"state_entity":"************","notes":null,"registered_agent":null}],"since":"2017-04-12","shares":[{"type":"Common","stock_authorized":0,"stock_issued":0,"notes":null}],"statetaxforms":null,"status":"active","subjurisd":null,"subsidiary_legal_entity_type":null,"subsidiary_to_consolidate":null,"total_shares":0,"withdrawal_date":null},"file_id":"63f63563-71f3-49b5-9a25-99927aac1a3c","message":null,"error_message":null,"created_at":"2025-07-03 20:20:05","updated_at":"2025-07-03 20:20:22","status":"SUCCESS","client":{"id":"84d8bb15-e550-4000-83e2-2fad1849a532","accounting_method":"Cash","active_since":null,"agr_signed":null,"agreement_sum":0.0,"billing_method":null,"bookkeeping":true,"business_model":null,"name":"Redwood Technology Inc","company_phone":"","cpa":"MB","description":"development, manufacture and sale of hardware for industrial applications","dissolution_date":null,"ein":"82-1274238","fedtaxforms":"1120","financial_year_end":"Dec.","financial_year_end_for_subsidiary":"Dec.","incorp_by":"ISC","internal_draft_flag":false,"legal_ent_type":"C-CORP","manager_id":null,"monthly_bill":0.0,"naicscode":"541519","notes_accounting":"","notes_address":"","notes_agreement":"","notes_contacts":"","notes_main":"Name change from Easycase to Redwood Technologies Inc filed Sept 14, 2020\nContact : Alex Tarasov Tel : ****** 241 0176","notes_shareholders":"","optional_share_count":0,"paid_by":null,"paid_by_mail":null,"payroll":true,"phone_main":null,"renewal_date":null,"renewal_date_mail":null,"since":"2016-12-31 23:00:00","source_id":"748a8ee9-ef3a-4f30-a4ca-196f179ad52f","statetaxforms":null,"status":"subscription","subjurisd":null,"subsidiary_legal_entity_type":null,"subsidiary_to_consolidate":null,"total_shares":100,"withdrawal_date":null,"created_at":"2021-06-28 10:04:26","updated_at":"2023-01-25 00:51:16","addresses":[{"id":"e6f15f2c-516e-4a29-b83b-5a2e2a04fe01","client_id":"84d8bb15-e550-4000-83e2-2fad1849a532","address_type":"Mailing Address","address_id":"02d21864-0d92-4a45-a345-88da8a2834ab","renewal_date":"2020-10-11 22:00:00","since_date":null,"phone":null,"paid_by":null,"note":"","created_at":"2025-02-10 15:34:00","updated_at":"2025-02-12 17:44:02","address":{"id":"02d21864-0d92-4a45-a345-88da8a2834ab","full_address":"233 Needham St, Suite 540, Newton, MA 02464, USA","street":"233 Needham St, Suite 540","pobox":"","city":"Newton","state":"MA","zip":"02464","country":"USA","created_at":"2025-02-10 15:34:00","updated_at":"2025-02-10 15:34:00"}},{"id":"92538df1-9848-420a-aa15-96c4c4ef2534","client_id":"84d8bb15-e550-4000-83e2-2fad1849a532","address_type":"Legal Address","address_id":"8a417afd-156f-4fb8-b901-7b92033b8272","renewal_date":null,"since_date":null,"phone":null,"paid_by":null,"note":"","created_at":"2025-02-10 15:34:00","updated_at":"2025-02-12 17:44:02","address":{"id":"8a417afd-156f-4fb8-b901-7b92033b8272","full_address":"1209 N Orange St Wilmington DE, 19801","street":"1209 N Orange St","pobox":"","city":"Wilmington","state":"DE","zip":"19801","country":"USA","created_at":"2025-02-10 15:34:00","updated_at":"2025-04-02 20:42:36"}},{"id":"482e9139-dc7c-48e1-95be-42deb5e5fd31","client_id":"84d8bb15-e550-4000-83e2-2fad1849a532","address_type":"Tax return Address","address_id":"02d21864-0d92-4a45-a345-88da8a2834ab","renewal_date":null,"since_date":null,"phone":null,"paid_by":null,"note":null,"created_at":"2025-02-10 15:34:00","updated_at":"2025-02-12 17:44:02","address":{"id":"02d21864-0d92-4a45-a345-88da8a2834ab","full_address":"233 Needham St, Suite 540, Newton, MA 02464, USA","street":"233 Needham St, Suite 540","pobox":"","city":"Newton","state":"MA","zip":"02464","country":"USA","created_at":"2025-02-10 15:34:00","updated_at":"2025-02-10 15:34:00"}},{"id":"6085c4a4-dab1-4045-bc58-ae43b1d00e9d","client_id":"84d8bb15-e550-4000-83e2-2fad1849a532","address_type":"Physical Address","address_id":"549c102c-082b-4fb8-9287-fe9be4fc1069","renewal_date":null,"since_date":null,"phone":null,"paid_by":null,"note":null,"created_at":"2025-02-10 15:34:00","updated_at":"2025-02-10 15:34:00","address":{"id":"549c102c-082b-4fb8-9287-fe9be4fc1069","full_address":"233 Needham St, Suite 540, Newton, MA 02464, United States","street":"233 Needham St, Suite 540","pobox":null,"city":"Newton","state":"MA","zip":"02464","country":"United States","created_at":"2025-02-10 15:34:00","updated_at":"2025-02-10 15:34:00"}}],"bank_accounts":[{"id":"d2967b3a-a134-4288-af49-8c373247e296","client_id":"84d8bb15-e550-4000-83e2-2fad1849a532","bank_name":"Bank Of America","aba_number":"*********","account_number":"************","bank_contact":"","controlled_by":"ISC","date_opened":null,"last_renewal":null,"notes":"","authorized_signers":[],"bank_cards":[]}],"contacts":[{"id":587,"client_id":"84d8bb15-e550-4000-83e2-2fad1849a532","client_person_id":"4ce8b890-42d2-4593-a29a-0e90a84012e9","position":"Contact person | Finance","is_main":null,"in_mailing_list":null,"email":"","phone":"","contact_info":"","note":"","created_at":null,"updated_at":null,"person":{"id":"4ce8b890-42d2-4593-a29a-0e90a84012e9","full_title":"Elena Korobova","firstname":"Elena","lastname":"Korobova","email":"<EMAIL>","phone":null,"contact_info":null,"citizenship":null,"address":null,"companies":null,"created_at":null,"updated_at":null}},{"id":588,"client_id":"84d8bb15-e550-4000-83e2-2fad1849a532","client_person_id":"79d1687b-00dd-46ce-897d-918381b24b61","position":"Contact person","is_main":null,"in_mailing_list":null,"email":"","phone":"","contact_info":"","note":"","created_at":null,"updated_at":null,"person":{"id":"79d1687b-00dd-46ce-897d-918381b24b61","full_title":"Alexander Tarasov","firstname":"Alexander","lastname":"Tarasov","email":null,"phone":"****** 241 0176","contact_info":null,"citizenship":null,"address":null,"companies":null,"created_at":null,"updated_at":null}},{"id":589,"client_id":"84d8bb15-e550-4000-83e2-2fad1849a532","client_person_id":"a6b5a414-ced5-4b03-9c65-1db42aa55675","position":"Signing officer | CEO","is_main":null,"in_mailing_list":null,"email":"<EMAIL>","phone":"","contact_info":"","note":"","created_at":null,"updated_at":null,"person":{"id":"a6b5a414-ced5-4b03-9c65-1db42aa55675","full_title":"Pavel Savin","firstname":"Pavel","lastname":"Savin","email":"<EMAIL>","phone":"tel +************","contact_info":"","citizenship":"Russia","address":"0","companies":"Redwood Technology Inc, PROGRADE SPACE INC","created_at":null,"updated_at":null}},{"id":590,"client_id":"84d8bb15-e550-4000-83e2-2fad1849a532","client_person_id":"a6b5a414-ced5-4b03-9c65-1db42aa55675","position":"Contact person","is_main":null,"in_mailing_list":null,"email":"<EMAIL>","phone":"","contact_info":"","note":"","created_at":null,"updated_at":null,"person":{"id":"a6b5a414-ced5-4b03-9c65-1db42aa55675","full_title":"Pavel Savin","firstname":"Pavel","lastname":"Savin","email":"<EMAIL>","phone":"tel +************","contact_info":"","citizenship":"Russia","address":"0","companies":"Redwood Technology Inc, PROGRADE SPACE INC","created_at":null,"updated_at":null}},{"id":591,"client_id":"84d8bb15-e550-4000-83e2-2fad1849a532","client_person_id":"14f82827-6eda-4e6c-b7c7-07a218b970b1","position":"Director","is_main":null,"in_mailing_list":null,"email":"","phone":"","contact_info":"","note":"","created_at":null,"updated_at":null,"person":{"id":"14f82827-6eda-4e6c-b7c7-07a218b970b1","full_title":"Alla Rezina","firstname":"Alla","lastname":"Rezina","email":"","phone":"","contact_info":"","citizenship":null,"address":"0","companies":"Redwood Technology Inc","created_at":null,"updated_at":null}},{"id":592,"client_id":"84d8bb15-e550-4000-83e2-2fad1849a532","client_person_id":"a6b5a414-ced5-4b03-9c65-1db42aa55675","position":"Director","is_main":null,"in_mailing_list":null,"email":"<EMAIL>","phone":"","contact_info":"","note":"","created_at":null,"updated_at":null,"person":{"id":"a6b5a414-ced5-4b03-9c65-1db42aa55675","full_title":"Pavel Savin","firstname":"Pavel","lastname":"Savin","email":"<EMAIL>","phone":"tel +************","contact_info":"","citizenship":"Russia","address":"0","companies":"Redwood Technology Inc, PROGRADE SPACE INC","created_at":null,"updated_at":null}}],"files":[{"id":"08cb75e3-f451-40a7-93b3-5756ed75e75e","date":null,"name":"+MB-Tax Questiоnnaire CORP 2021 REDWOOD.xlsx","size":null,"description":null,"hash":"e7cb12ffcc14c801364d7d3f58c4ee2b694489755900cdc2c632937841fa4f65","file_type":"application/vnd.openxmlformats-officedocument.spreadsheetml.sheet","doc_type":"Tax Questionnaire","client_id":"84d8bb15-e550-4000-83e2-2fad1849a532","client_person_id":null,"manager_id":"a8b00550-7c6c-4878-b106-3ca0d5e86ce9","created_at":"2025-04-23 22:52:11","updated_at":"2025-04-23 22:52:11"},{"id":"1a7f62cc-e0e2-41bd-ab61-4d6de731c0c6","date":null,"name":"2019 DE FTB Annual Filing EasyCase.pdf","size":null,"description":null,"hash":"968946307a695fc102e354a2202ec260e6d8cfa62ac6dbcab95a31424c6d9a7f","file_type":"application/pdf","doc_type":"Delaware FTB Tax report CORP","client_id":"84d8bb15-e550-4000-83e2-2fad1849a532","client_person_id":null,"manager_id":"a8b00550-7c6c-4878-b106-3ca0d5e86ce9","created_at":"2025-04-23 22:50:35","updated_at":"2025-04-23 22:50:35"},{"id":"22b48ce3-9203-4984-8edb-53fe721f6094","date":null,"name":"EASYSYST4238_19C_SIGNED.pdf","size":null,"description":null,"hash":"93c4bb560e1e56c0a13624c23aab64c5d798d1b7a0f162cefa6ed558b5256a6d","file_type":"application/pdf","doc_type":"1120 Federal IRS CORP Tax form 1120","client_id":"84d8bb15-e550-4000-83e2-2fad1849a532","client_person_id":null,"manager_id":"a8b00550-7c6c-4878-b106-3ca0d5e86ce9","created_at":"2025-04-23 22:34:05","updated_at":"2025-04-23 22:34:05"},{"id":"286966f9-195e-40a8-88c8-74c186d91191","date":null,"name":"+MB-Tax Questiоnnaire CORP 2018 EasyCase Systems Inc.xlsx","size":null,"description":null,"hash":"9719e1f627077946785ff119efe4c639790103fd6f25f8c8c30dcfcc07f6226b","file_type":"application/vnd.openxmlformats-officedocument.spreadsheetml.sheet","doc_type":"Tax Questionnaire","client_id":"84d8bb15-e550-4000-83e2-2fad1849a532","client_person_id":null,"manager_id":"a8b00550-7c6c-4878-b106-3ca0d5e86ce9","created_at":"2025-04-23 22:51:55","updated_at":"2025-04-23 22:51:55"},{"id":"4bb46169-ded1-4f4b-8954-19de449c9499","date":null,"name":"RT_REG_MA (1) (1).pdf","size":null,"description":null,"hash":"1fa4d05f1b6fbb4d346358e09f0b1a187df503d8c0a18adaa7f8701b9a7768e3","file_type":"application/pdf","doc_type":"01-Cert_Of_Incorp","client_id":"84d8bb15-e550-4000-83e2-2fad1849a532","client_person_id":null,"manager_id":"a8b00550-7c6c-4878-b106-3ca0d5e86ce9","created_at":"2025-04-23 22:33:18","updated_at":"2025-04-23 22:33:18"},{"id":"55178705-1700-4707-b53e-9a720bda8f98","date":null,"name":"REDWOOD easy4238_21C_CC SIGNED.pdf","size":null,"description":null,"hash":"12577e7f05f723283c9d0174721df9c2ed922948a81312d2a717c50c9806af45","file_type":"application/pdf","doc_type":"1120 Federal IRS CORP Tax form 1120","client_id":"84d8bb15-e550-4000-83e2-2fad1849a532","client_person_id":null,"manager_id":"a8b00550-7c6c-4878-b106-3ca0d5e86ce9","created_at":"2025-04-23 22:34:30","updated_at":"2025-04-23 22:34:30"},{"id":"72bada14-a6f8-4068-b1e4-1ead48dd2048","date":null,"name":"EASYCASE4238_18c_CC-SIGNED.pdf","size":null,"description":null,"hash":"170e927125f387a6ec27fe537eadf21f634e3b621f94aee4a56c980499b4d44e","file_type":"application/pdf","doc_type":"1120 Federal IRS CORP Tax form 1120","client_id":"84d8bb15-e550-4000-83e2-2fad1849a532","client_person_id":null,"manager_id":"a8b00550-7c6c-4878-b106-3ca0d5e86ce9","created_at":"2025-04-23 22:33:58","updated_at":"2025-04-23 22:33:58"},{"id":"7494d002-3573-4fcd-921e-c55ddab5ce8e","date":null,"name":"2020 DE FTB Annual Filing Redwood Technology.pdf","size":null,"description":null,"hash":"4148204f123f2325c187f12e2ba6a7867e66b177158258d432cfaeb5ee6368d0","file_type":"application/pdf","doc_type":"Delaware FTB Tax report CORP","client_id":"84d8bb15-e550-4000-83e2-2fad1849a532","client_person_id":null,"manager_id":"a8b00550-7c6c-4878-b106-3ca0d5e86ce9","created_at":"2025-04-23 22:51:03","updated_at":"2025-04-23 22:51:03"},{"id":"9a0708f9-8717-47a9-8a3e-0d6b56792e00","date":null,"name":"+MB-Tax Questiоnnaire CORP 2019 EasyCase Systems Inc.xlsx","size":null,"description":null,"hash":"ff92213d8b27aaa775e91b7fb095b61e510235e334c9ca498ffba4b70bdfb32b","file_type":"application/vnd.openxmlformats-officedocument.spreadsheetml.sheet","doc_type":"Tax Questionnaire","client_id":"84d8bb15-e550-4000-83e2-2fad1849a532","client_person_id":null,"manager_id":"a8b00550-7c6c-4878-b106-3ca0d5e86ce9","created_at":"2025-04-23 22:51:59","updated_at":"2025-04-23 22:51:59"},{"id":"b786dc3e-19b3-42f2-a46f-81908cfef713","date":null,"name":"2018 DE FTB Annual Filing Easycase.pdf","size":null,"description":null,"hash":"a434daed32882e5de467f68545d81ffee89dee781c582da4dffa4d32d5330379","file_type":"application/pdf","doc_type":"Delaware FTB Tax report CORP","client_id":"84d8bb15-e550-4000-83e2-2fad1849a532","client_person_id":null,"manager_id":"a8b00550-7c6c-4878-b106-3ca0d5e86ce9","created_at":"2025-04-23 22:50:04","updated_at":"2025-04-23 22:50:04"},{"id":"ba4751f8-d183-4f0c-abee-0b7dcde265e1","date":null,"name":"2021 DE FTB Annual Filing REDWOOD TECHNOLOGY .pdf","size":null,"description":null,"hash":"dc501c3d2410c89d371766e65aa35f8a3a9f2e4faf3581f852db1b229fcdd3b6","file_type":"application/pdf","doc_type":"Delaware FTB Tax report CORP","client_id":"84d8bb15-e550-4000-83e2-2fad1849a532","client_person_id":null,"manager_id":"a8b00550-7c6c-4878-b106-3ca0d5e86ce9","created_at":"2025-04-23 22:51:29","updated_at":"2025-04-23 22:51:29"},{"id":"cc372eb2-97af-4168-a3d6-2ddc5897f727","date":null,"name":"REDWOODTECH4238_20C_CC (1).pdf","size":null,"description":null,"hash":"e1827ade6fe518f46031e1619cc9b323e97ebdbb9e9a10a1bfd2a81a621af6c5","file_type":"application/pdf","doc_type":"1120 Federal IRS CORP Tax form 1120","client_id":"84d8bb15-e550-4000-83e2-2fad1849a532","client_person_id":null,"manager_id":"a8b00550-7c6c-4878-b106-3ca0d5e86ce9","created_at":"2025-04-23 22:42:51","updated_at":"2025-04-23 22:42:51"},{"id":"e66c3e74-3eeb-4982-bf68-f1f5a0670c20","date":null,"name":"+MB-Tax Questiоnnaire CORP 2020 EasyCase Systems Inc-REDWOOD.xlsx","size":null,"description":null,"hash":"c6667ab072ad84f13b3e2a736801f1e150fe9775f533491cbefca9a6cf0f47d9","file_type":"application/vnd.openxmlformats-officedocument.spreadsheetml.sheet","doc_type":"Tax Questionnaire","client_id":"84d8bb15-e550-4000-83e2-2fad1849a532","client_person_id":null,"manager_id":"a8b00550-7c6c-4878-b106-3ca0d5e86ce9","created_at":"2025-04-23 22:52:04","updated_at":"2025-04-23 22:52:04"}],"payment_cards":[],"payment_services":[],"primary_registration":{"id":172,"client_id":"84d8bb15-e550-4000-83e2-2fad1849a532","registered_agent_id":"e41da580-59ce-47ef-a643-170a80896200","is_primary":true,"registered_date":"2017-04-11 22:00:00","terminated_date":null,"last_renewal_date":null,"annual_compliance_due_date":null,"state_of_incorporation":"DE","billed_to":null,"last_soi_filed":null,"state_entity":"6377166","notes":"","created_at":"2025-02-10 15:34:00","updated_at":"2025-02-10 15:34:00","registered_agent":{"id":"e41da580-59ce-47ef-a643-170a80896200","title":"National Registered Agents Inc. (NRAI)","nickname":"NRAI","address_id":"8c29fa19-9ecf-48e6-a128-25060ae2ecc0","created_at":"2025-02-10 15:34:00","updated_at":"2025-02-10 15:34:00","address":{"id":"8c29fa19-9ecf-48e6-a128-25060ae2ecc0","full_address":"1209 N Orange St, Wilmington, DE 19801, USA","street":"1209 N Orange St","pobox":null,"city":"Wilmington","state":"DE","zip":"19801","country":"USA","created_at":"2025-06-18 18:46:49","updated_at":"2025-06-18 18:46:49"}}},"secondary_registrations":[],"services":[],"shares":[],"llc_shareholders":[{"id":"22b36940-d6ad-4613-b3bc-e9f068d500b5","client_id":"84d8bb15-e550-4000-83e2-2fad1849a532","client_person_id":"a6b5a414-ced5-4b03-9c65-1db42aa55675","position":"Shareholder","ownership":"100","is_managing_member":null,"note":"","person":{"id":"a6b5a414-ced5-4b03-9c65-1db42aa55675","full_title":"Pavel Savin","firstname":"Pavel","lastname":"Savin","email":"<EMAIL>","phone":"tel +************","contact_info":"","citizenship":"Russia","address":"0","companies":"Redwood Technology Inc, PROGRADE SPACE INC","created_at":null,"updated_at":null}}],"tasks":[],"tax_reporting":[],"capitalization_table":[],"manager":null,"source":{"id":"748a8ee9-ef3a-4f30-a4ca-196f179ad52f","title":"Kokorich"}},"client_output":{"id":"84d8bb15-e550-4000-83e2-2fad1849a532","accounting_method":"Cash","active_since":"2017-04-12","agr_signed":null,"agreement_sum":0.0,"billing_method":null,"bookkeeping":true,"business_model":"Hardware","name":"Redwood Technology Inc","company_phone":"","cpa":"MB","description":"Development, manufacture and sale of hardware for industrial applications","dissolution_date":null,"ein":"82-1274238","fedtaxforms":"1120","financial_year_end":"Dec.","financial_year_end_for_subsidiary":"Dec.","incorp_by":"ISC","internal_draft_flag":false,"legal_ent_type":"C-CORP","manager_id":null,"monthly_bill":0.0,"naicscode":"541519","notes_accounting":"","notes_address":"","notes_agreement":"","notes_contacts":"","notes_main":"Company name changed from Easycase to Redwood Technologies Inc filed Sept 14, 2020.","notes_shareholders":"","optional_share_count":0,"paid_by":null,"paid_by_mail":null,"payroll":true,"phone_main":null,"renewal_date":null,"renewal_date_mail":null,"since":"2017-04-12","source_id":"748a8ee9-ef3a-4f30-a4ca-196f179ad52f","statetaxforms":null,"status":"active","subjurisd":null,"subsidiary_legal_entity_type":null,"subsidiary_to_consolidate":null,"total_shares":100,"withdrawal_date":null,"created_at":"2021-06-28 10:04:26","updated_at":"2023-01-25 00:51:16","addresses":[{"id":"e6f15f2c-516e-4a29-b83b-5a2e2a04fe01","client_id":"84d8bb15-e550-4000-83e2-2fad1849a532","address_type":"Mailing Address","address_id":"02d21864-0d92-4a45-a345-88da8a2834ab","renewal_date":"2020-10-11 22:00:00","since_date":null,"phone":null,"paid_by":null,"note":"","created_at":"2025-02-10 15:34:00","updated_at":"2025-02-12 17:44:02","address":{"id":"02d21864-0d92-4a45-a345-88da8a2834ab","full_address":"233 Needham St, Suite 540, Newton, MA 02464, USA","street":"233 Needham St, Suite 540","pobox":"","city":"Newton","state":"MA","zip":"02464","country":"USA","created_at":"2025-02-10 15:34:00","updated_at":"2025-02-10 15:34:00"}},{"id":"92538df1-9848-420a-aa15-96c4c4ef2534","client_id":"84d8bb15-e550-4000-83e2-2fad1849a532","address_type":"Legal Address","address_id":"8a417afd-156f-4fb8-b901-7b92033b8272","renewal_date":null,"since_date":null,"phone":null,"paid_by":null,"note":"","created_at":"2025-02-10 15:34:00","updated_at":"2025-02-12 17:44:02","address":{"id":"8a417afd-156f-4fb8-b901-7b92033b8272","full_address":"1209 N Orange St Wilmington DE, 19801","street":"1209 N Orange St","pobox":"","city":"Wilmington","state":"DE","zip":"19801","country":"USA","created_at":"2025-02-10 15:34:00","updated_at":"2025-04-02 20:42:36"}},{"id":"482e9139-dc7c-48e1-95be-42deb5e5fd31","client_id":"84d8bb15-e550-4000-83e2-2fad1849a532","address_type":"Tax return Address","address_id":"02d21864-0d92-4a45-a345-88da8a2834ab","renewal_date":null,"since_date":null,"phone":null,"paid_by":null,"note":null,"created_at":"2025-02-10 15:34:00","updated_at":"2025-02-12 17:44:02","address":{"id":"02d21864-0d92-4a45-a345-88da8a2834ab","full_address":"233 Needham St, Suite 540, Newton, MA 02464, USA","street":"233 Needham St, Suite 540","pobox":"","city":"Newton","state":"MA","zip":"02464","country":"USA","created_at":"2025-02-10 15:34:00","updated_at":"2025-02-10 15:34:00"}},{"id":"6085c4a4-dab1-4045-bc58-ae43b1d00e9d","client_id":"84d8bb15-e550-4000-83e2-2fad1849a532","address_type":"Physical Address","address_id":"549c102c-082b-4fb8-9287-fe9be4fc1069","renewal_date":null,"since_date":null,"phone":null,"paid_by":null,"note":null,"created_at":"2025-02-10 15:34:00","updated_at":"2025-02-10 15:34:00","address":{"id":"549c102c-082b-4fb8-9287-fe9be4fc1069","full_address":"233 Needham St, Suite 540, Newton, MA 02464, United States","street":"233 Needham St, Suite 540","pobox":null,"city":"Newton","state":"MA","zip":"02464","country":"United States","created_at":"2025-02-10 15:34:00","updated_at":"2025-02-10 15:34:00"}},{"address_type":"Tax return Address","renewal_date":null,"phone":null,"paid_by":null,"note":null,"address":{"street":"233 Needham St, Suite 540","city":"Newton","state":"MA","zip":"02464","country":"USA","pobox":null,"full_address":"233 Needham St, Suite 540, Newton MA 02464, USA"}},{"address_type":"Mailing Address","renewal_date":null,"phone":null,"paid_by":null,"note":null,"address":{"street":"2464 Massachusetts Ave, Suite 220","city":"Cambridge","state":"MA","zip":"02140","country":"USA","pobox":null,"full_address":"2464 Massachusetts Ave, Suite 220, Cambridge MA 02140, USA"}}],"bank_accounts":[{"id":"d2967b3a-a134-4288-af49-8c373247e296","client_id":"84d8bb15-e550-4000-83e2-2fad1849a532","bank_name":"Bank Of America","aba_number":"*********","account_number":"************","bank_contact":"","controlled_by":"ISC","date_opened":null,"last_renewal":null,"notes":"","authorized_signers":[],"bank_cards":[]},{"bank_name":"Bank Of America","aba_number":"*********","account_number":"************","bank_contact":null,"controlled_by":null,"date_opened":null,"last_renewal":null,"notes":null,"authorized_signers":[],"bank_cards":[]},{"bank_name":"Bank Of America, NA","aba_number":"*********","account_number":"************","bank_contact":null,"controlled_by":null,"date_opened":null,"last_renewal":null,"notes":"Old account - closed","authorized_signers":[],"bank_cards":[]}],"contacts":[{"id":587,"client_id":"84d8bb15-e550-4000-83e2-2fad1849a532","client_person_id":"4ce8b890-42d2-4593-a29a-0e90a84012e9","position":"Contact person | Finance","is_main":null,"in_mailing_list":null,"email":"","phone":"","contact_info":"","note":"","created_at":null,"updated_at":null,"person":{"id":"4ce8b890-42d2-4593-a29a-0e90a84012e9","full_title":"Elena Korobova","firstname":"Elena","lastname":"Korobova","email":"<EMAIL>","phone":null,"contact_info":null,"citizenship":null,"address":null,"companies":null,"created_at":null,"updated_at":null}},{"id":588,"client_id":"84d8bb15-e550-4000-83e2-2fad1849a532","client_person_id":"79d1687b-00dd-46ce-897d-918381b24b61","position":"Contact person","is_main":null,"in_mailing_list":null,"email":"","phone":"","contact_info":"","note":"","created_at":null,"updated_at":null,"person":{"id":"79d1687b-00dd-46ce-897d-918381b24b61","full_title":"Alexander Tarasov","firstname":"Alexander","lastname":"Tarasov","email":null,"phone":"****** 241 0176","contact_info":null,"citizenship":null,"address":null,"companies":null,"created_at":null,"updated_at":null}},{"id":589,"client_id":"84d8bb15-e550-4000-83e2-2fad1849a532","client_person_id":"a6b5a414-ced5-4b03-9c65-1db42aa55675","position":"Signing officer | CEO","is_main":null,"in_mailing_list":null,"email":"<EMAIL>","phone":"","contact_info":"","note":"","created_at":null,"updated_at":null,"person":{"id":"a6b5a414-ced5-4b03-9c65-1db42aa55675","full_title":"Pavel Savin","firstname":"Pavel","lastname":"Savin","email":"<EMAIL>","phone":"tel +************","contact_info":"","citizenship":"Russia","address":"0","companies":"Redwood Technology Inc, PROGRADE SPACE INC","created_at":null,"updated_at":null}},{"id":590,"client_id":"84d8bb15-e550-4000-83e2-2fad1849a532","client_person_id":"a6b5a414-ced5-4b03-9c65-1db42aa55675","position":"Contact person","is_main":null,"in_mailing_list":null,"email":"<EMAIL>","phone":"","contact_info":"","note":"","created_at":null,"updated_at":null,"person":{"id":"a6b5a414-ced5-4b03-9c65-1db42aa55675","full_title":"Pavel Savin","firstname":"Pavel","lastname":"Savin","email":"<EMAIL>","phone":"tel +************","contact_info":"","citizenship":"Russia","address":"0","companies":"Redwood Technology Inc, PROGRADE SPACE INC","created_at":null,"updated_at":null}},{"id":591,"client_id":"84d8bb15-e550-4000-83e2-2fad1849a532","client_person_id":"14f82827-6eda-4e6c-b7c7-07a218b970b1","position":"Director","is_main":null,"in_mailing_list":null,"email":"","phone":"","contact_info":"","note":"","created_at":null,"updated_at":null,"person":{"id":"14f82827-6eda-4e6c-b7c7-07a218b970b1","full_title":"Alla Rezina","firstname":"Alla","lastname":"Rezina","email":"","phone":"","contact_info":"","citizenship":null,"address":"0","companies":"Redwood Technology Inc","created_at":null,"updated_at":null}},{"id":592,"client_id":"84d8bb15-e550-4000-83e2-2fad1849a532","client_person_id":"a6b5a414-ced5-4b03-9c65-1db42aa55675","position":"Director","is_main":null,"in_mailing_list":null,"email":"<EMAIL>","phone":"","contact_info":"","note":"","created_at":null,"updated_at":null,"person":{"id":"a6b5a414-ced5-4b03-9c65-1db42aa55675","full_title":"Pavel Savin","firstname":"Pavel","lastname":"Savin","email":"<EMAIL>","phone":"tel +************","contact_info":"","citizenship":"Russia","address":"0","companies":"Redwood Technology Inc, PROGRADE SPACE INC","created_at":null,"updated_at":null}},{"position":"Director","email":null,"phone":null,"contact_info":null,"note":null,"person":{"firstname":"Pavel","lastname":"Savin","email":null,"phone":null,"contact_info":null,"citizenship":"Russia","address":"Cliff And Park, 88/15 Moo 3, T.Najomtien, A.Sattahip, Chonburi, Thailand","companies":null}},{"position":"CEO","email":null,"phone":null,"contact_info":null,"note":null,"person":{"firstname":"Pavel","lastname":"Savin","email":null,"phone":null,"contact_info":null,"citizenship":"Russia","address":"Cliff And Park, 88/15 Moo 3, T.Najomtien, A.Sattahip, Chonburi, Thailand","companies":null}}],"files":[{"id":"08cb75e3-f451-40a7-93b3-5756ed75e75e","date":null,"name":"+MB-Tax Questiоnnaire CORP 2021 REDWOOD.xlsx","size":null,"description":null,"hash":"e7cb12ffcc14c801364d7d3f58c4ee2b694489755900cdc2c632937841fa4f65","file_type":"application/vnd.openxmlformats-officedocument.spreadsheetml.sheet","doc_type":"Tax Questionnaire","client_id":"84d8bb15-e550-4000-83e2-2fad1849a532","client_person_id":null,"manager_id":"a8b00550-7c6c-4878-b106-3ca0d5e86ce9","created_at":"2025-04-23 22:52:11","updated_at":"2025-04-23 22:52:11"},{"id":"1a7f62cc-e0e2-41bd-ab61-4d6de731c0c6","date":null,"name":"2019 DE FTB Annual Filing EasyCase.pdf","size":null,"description":null,"hash":"968946307a695fc102e354a2202ec260e6d8cfa62ac6dbcab95a31424c6d9a7f","file_type":"application/pdf","doc_type":"Delaware FTB Tax report CORP","client_id":"84d8bb15-e550-4000-83e2-2fad1849a532","client_person_id":null,"manager_id":"a8b00550-7c6c-4878-b106-3ca0d5e86ce9","created_at":"2025-04-23 22:50:35","updated_at":"2025-04-23 22:50:35"},{"id":"22b48ce3-9203-4984-8edb-53fe721f6094","date":null,"name":"EASYSYST4238_19C_SIGNED.pdf","size":null,"description":null,"hash":"93c4bb560e1e56c0a13624c23aab64c5d798d1b7a0f162cefa6ed558b5256a6d","file_type":"application/pdf","doc_type":"1120 Federal IRS CORP Tax form 1120","client_id":"84d8bb15-e550-4000-83e2-2fad1849a532","client_person_id":null,"manager_id":"a8b00550-7c6c-4878-b106-3ca0d5e86ce9","created_at":"2025-04-23 22:34:05","updated_at":"2025-04-23 22:34:05"},{"id":"286966f9-195e-40a8-88c8-74c186d91191","date":null,"name":"+MB-Tax Questiоnnaire CORP 2018 EasyCase Systems Inc.xlsx","size":null,"description":null,"hash":"9719e1f627077946785ff119efe4c639790103fd6f25f8c8c30dcfcc07f6226b","file_type":"application/vnd.openxmlformats-officedocument.spreadsheetml.sheet","doc_type":"Tax Questionnaire","client_id":"84d8bb15-e550-4000-83e2-2fad1849a532","client_person_id":null,"manager_id":"a8b00550-7c6c-4878-b106-3ca0d5e86ce9","created_at":"2025-04-23 22:51:55","updated_at":"2025-04-23 22:51:55"},{"id":"4bb46169-ded1-4f4b-8954-19de449c9499","date":null,"name":"RT_REG_MA (1) (1).pdf","size":null,"description":null,"hash":"1fa4d05f1b6fbb4d346358e09f0b1a187df503d8c0a18adaa7f8701b9a7768e3","file_type":"application/pdf","doc_type":"01-Cert_Of_Incorp","client_id":"84d8bb15-e550-4000-83e2-2fad1849a532","client_person_id":null,"manager_id":"a8b00550-7c6c-4878-b106-3ca0d5e86ce9","created_at":"2025-04-23 22:33:18","updated_at":"2025-04-23 22:33:18"},{"id":"55178705-1700-4707-b53e-9a720bda8f98","date":null,"name":"REDWOOD easy4238_21C_CC SIGNED.pdf","size":null,"description":null,"hash":"12577e7f05f723283c9d0174721df9c2ed922948a81312d2a717c50c9806af45","file_type":"application/pdf","doc_type":"1120 Federal IRS CORP Tax form 1120","client_id":"84d8bb15-e550-4000-83e2-2fad1849a532","client_person_id":null,"manager_id":"a8b00550-7c6c-4878-b106-3ca0d5e86ce9","created_at":"2025-04-23 22:34:30","updated_at":"2025-04-23 22:34:30"},{"id":"72bada14-a6f8-4068-b1e4-1ead48dd2048","date":null,"name":"EASYCASE4238_18c_CC-SIGNED.pdf","size":null,"description":null,"hash":"170e927125f387a6ec27fe537eadf21f634e3b621f94aee4a56c980499b4d44e","file_type":"application/pdf","doc_type":"1120 Federal IRS CORP Tax form 1120","client_id":"84d8bb15-e550-4000-83e2-2fad1849a532","client_person_id":null,"manager_id":"a8b00550-7c6c-4878-b106-3ca0d5e86ce9","created_at":"2025-04-23 22:33:58","updated_at":"2025-04-23 22:33:58"},{"id":"7494d002-3573-4fcd-921e-c55ddab5ce8e","date":null,"name":"2020 DE FTB Annual Filing Redwood Technology.pdf","size":null,"description":null,"hash":"4148204f123f2325c187f12e2ba6a7867e66b177158258d432cfaeb5ee6368d0","file_type":"application/pdf","doc_type":"Delaware FTB Tax report CORP","client_id":"84d8bb15-e550-4000-83e2-2fad1849a532","client_person_id":null,"manager_id":"a8b00550-7c6c-4878-b106-3ca0d5e86ce9","created_at":"2025-04-23 22:51:03","updated_at":"2025-04-23 22:51:03"},{"id":"9a0708f9-8717-47a9-8a3e-0d6b56792e00","date":null,"name":"+MB-Tax Questiоnnaire CORP 2019 EasyCase Systems Inc.xlsx","size":null,"description":null,"hash":"ff92213d8b27aaa775e91b7fb095b61e510235e334c9ca498ffba4b70bdfb32b","file_type":"application/vnd.openxmlformats-officedocument.spreadsheetml.sheet","doc_type":"Tax Questionnaire","client_id":"84d8bb15-e550-4000-83e2-2fad1849a532","client_person_id":null,"manager_id":"a8b00550-7c6c-4878-b106-3ca0d5e86ce9","created_at":"2025-04-23 22:51:59","updated_at":"2025-04-23 22:51:59"},{"id":"b786dc3e-19b3-42f2-a46f-81908cfef713","date":null,"name":"2018 DE FTB Annual Filing Easycase.pdf","size":null,"description":null,"hash":"a434daed32882e5de467f68545d81ffee89dee781c582da4dffa4d32d5330379","file_type":"application/pdf","doc_type":"Delaware FTB Tax report CORP","client_id":"84d8bb15-e550-4000-83e2-2fad1849a532","client_person_id":null,"manager_id":"a8b00550-7c6c-4878-b106-3ca0d5e86ce9","created_at":"2025-04-23 22:50:04","updated_at":"2025-04-23 22:50:04"},{"id":"ba4751f8-d183-4f0c-abee-0b7dcde265e1","date":null,"name":"2021 DE FTB Annual Filing REDWOOD TECHNOLOGY .pdf","size":null,"description":null,"hash":"dc501c3d2410c89d371766e65aa35f8a3a9f2e4faf3581f852db1b229fcdd3b6","file_type":"application/pdf","doc_type":"Delaware FTB Tax report CORP","client_id":"84d8bb15-e550-4000-83e2-2fad1849a532","client_person_id":null,"manager_id":"a8b00550-7c6c-4878-b106-3ca0d5e86ce9","created_at":"2025-04-23 22:51:29","updated_at":"2025-04-23 22:51:29"},{"id":"cc372eb2-97af-4168-a3d6-2ddc5897f727","date":null,"name":"REDWOODTECH4238_20C_CC (1).pdf","size":null,"description":null,"hash":"e1827ade6fe518f46031e1619cc9b323e97ebdbb9e9a10a1bfd2a81a621af6c5","file_type":"application/pdf","doc_type":"1120 Federal IRS CORP Tax form 1120","client_id":"84d8bb15-e550-4000-83e2-2fad1849a532","client_person_id":null,"manager_id":"a8b00550-7c6c-4878-b106-3ca0d5e86ce9","created_at":"2025-04-23 22:42:51","updated_at":"2025-04-23 22:42:51"},{"id":"e66c3e74-3eeb-4982-bf68-f1f5a0670c20","date":null,"name":"+MB-Tax Questiоnnaire CORP 2020 EasyCase Systems Inc-REDWOOD.xlsx","size":null,"description":null,"hash":"c6667ab072ad84f13b3e2a736801f1e150fe9775f533491cbefca9a6cf0f47d9","file_type":"application/vnd.openxmlformats-officedocument.spreadsheetml.sheet","doc_type":"Tax Questionnaire","client_id":"84d8bb15-e550-4000-83e2-2fad1849a532","client_person_id":null,"manager_id":"a8b00550-7c6c-4878-b106-3ca0d5e86ce9","created_at":"2025-04-23 22:52:04","updated_at":"2025-04-23 22:52:04"}],"payment_cards":[],"payment_services":[],"primary_registration":{"id":172,"client_id":"84d8bb15-e550-4000-83e2-2fad1849a532","registered_agent_id":"e41da580-59ce-47ef-a643-170a80896200","is_primary":true,"registered_date":"2017-04-12","terminated_date":null,"last_renewal_date":null,"annual_compliance_due_date":null,"state_of_incorporation":"DE","billed_to":null,"last_soi_filed":null,"state_entity":"6377166","notes":"","created_at":"2025-02-10 15:34:00","updated_at":"2025-02-10 15:34:00","registered_agent":{"id":"e41da580-59ce-47ef-a643-170a80896200","title":"National Registered Agents Inc. (NRAI)","nickname":"NRAI","address_id":"8c29fa19-9ecf-48e6-a128-25060ae2ecc0","created_at":"2025-02-10 15:34:00","updated_at":"2025-02-10 15:34:00","address":{"id":"8c29fa19-9ecf-48e6-a128-25060ae2ecc0","full_address":"1209 N Orange St, Wilmington, DE 19801, USA","street":"1209 N Orange St","pobox":null,"city":"Wilmington","state":"DE","zip":"19801","country":"USA","created_at":"2025-06-18 18:46:49","updated_at":"2025-06-18 18:46:49"}}},"secondary_registrations":[{"registered_date":"2020-12-24","terminated_date":null,"last_renewal_date":null,"annual_compliance_due_date":null,"state_of_incorporation":"MA","billed_to":null,"last_soi_filed":null,"state_entity":"************","notes":null,"registered_agent":null}],"services":[],"shares":[{"type":"Common","stock_authorized":0,"stock_issued":0,"notes":null}],"llc_shareholders":[{"id":"22b36940-d6ad-4613-b3bc-e9f068d500b5","client_id":"84d8bb15-e550-4000-83e2-2fad1849a532","client_person_id":"a6b5a414-ced5-4b03-9c65-1db42aa55675","position":"Shareholder","ownership":"100","is_managing_member":null,"note":"","person":{"id":"a6b5a414-ced5-4b03-9c65-1db42aa55675","full_title":"Pavel Savin","firstname":"Pavel","lastname":"Savin","email":"<EMAIL>","phone":"tel +************","contact_info":"","citizenship":"Russia","address":"0","companies":"Redwood Technology Inc, PROGRADE SPACE INC","created_at":null,"updated_at":null}}],"tasks":[],"tax_reporting":[],"capitalization_table":[{"share_amount":null,"date":null,"issued_percentage":100,"authorized_percentage":null,"notes":null,"person":{"firstname":"Pavel","lastname":"Savin","email":null,"phone":null,"contact_info":null,"citizenship":"Russia","address":"Cliff And Park, 88/15 Moo 3, T.Najomtien, A.Sattahip, Chonburi, Thailand","companies":null}}],"manager":null,"source":{"id":"748a8ee9-ef3a-4f30-a4ca-196f179ad52f","title":"Kokorich"}},"exists":true,"file":{"id":"63f63563-71f3-49b5-9a25-99927aac1a3c","date":null,"name":"+MB-Tax Questiоnnaire CORP 2024 REDWOOD TECHNOLOGY.xlsx","size":110071,"description":null,"hash":"bfe5440faa4adf5551034a2d9dffe24c76c6258e1969faab4882d85cbb27bde4","file_type":"application/vnd.openxmlformats-officedocument.spreadsheetml.sheet","doc_type":null,"client_id":null,"client_person_id":null,"manager_id":"a8b00550-7c6c-4878-b106-3ca0d5e86ce9","created_at":"2025-07-03 20:20:05","updated_at":"2025-07-03 20:20:05"}}

export function TestFormComponent () {
    const [readonly, setReadonly] = React.useState(false)
    const [newData, setNewData] = React.useState<Client | null >(null)
    const [existData, setExistData] = React.useState<Client | null | undefined>(null)
    const [hasChanges, setHasChanges] = React.useState(false)
    const [isProcessing, setIsProcessing] = React.useState(false)

    // Мемоизируем данные для предотвращения лишних ререндеров
    const initialData = useMemo(() => res.client_output || res.output, [])
    const baseData = useMemo(() => res.client, [])

    React.useEffect(() => {
        setNewData(initialData)
        setExistData(baseData)
    }, [initialData, baseData])

    // Функция обработки изменений (без debounce)
    const handleDataChange = useCallback((data: any) => {
        console.log('Form data changed:', data)
        setIsProcessing(false)
    }, [])

    // Создаем debounced версию обработчика
    const debouncedHandleDataChange = useDebounce(handleDataChange, 300)

    // Оптимизированный обработчик изменений с debounce
    const onChangeData = useCallback((data: any) => {
        // Отмечаем, что есть изменения
        setHasChanges(true)
        setIsProcessing(true)

        // Вызываем debounced функцию
        debouncedHandleDataChange(data)
    }, [debouncedHandleDataChange])

    // Очищаем debounce при размонтировании компонента
    React.useEffect(() => {
        return () => {
            if ((debouncedHandleDataChange as any).cleanup) {
                (debouncedHandleDataChange as any).cleanup()
            }
        }
    }, [debouncedHandleDataChange])

    // Мемоизированные обработчики кнопок
    const handleEditMode = useCallback(() => {
        setReadonly(false)
    }, [])

    const handlePreviewMode = useCallback(() => {
        setReadonly(true)
    }, [])

    return (<>
        <Stack
          direction="row"
          spacing={2}
          sx={{
            justifyContent: "space-between",
            alignItems: "center",
          }}
        >
            <Box sx={{ display: 'flex', alignItems: 'center', gap: 2 }}>
                <Typography variant="h4" gutterBottom>
                    Test form
                </Typography>
                {hasChanges && (
                    <Chip
                        label={isProcessing ? "Processing..." : "Modified"}
                        color={isProcessing ? "warning" : "info"}
                        size="small"
                    />
                )}
            </Box>
            <Box component='div' sx={{px: 3}}>
            { readonly
              ? <Button variant="outlined" onClick={handleEditMode} startIcon={<EditIcon />}> Edit </Button>
              : <Button variant="outlined" onClick={handlePreviewMode} startIcon={<PreviewIcon />}> Preview </Button>
              }
          </Box>
        </Stack>
        { newData && (
            <SchemaGridEdit
                schema={clientUploadViewSchema}
                initialValues={newData}
                baseValues={existData || {}}
                actions={false}
                readonly={readonly}
                onChange={onChangeData}
            />
        )}
    </>)
}