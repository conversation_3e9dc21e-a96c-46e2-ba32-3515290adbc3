import React, { useState } from 'react';
import {
  Container,
  Typography,
  Box,
  Paper,
  Grid,
  Button,
  Di<PERSON>r,
  Alert,
} from '@mui/material';
import { 
  GlobalFileUpload, 
  UploadStatus, 
  useGlobalUpload 
} from '@/components_lib/core/upload';

export const TestGlobalUploadPage: React.FC = () => {
  const [uploadResults, setUploadResults] = useState<any[]>([]);
  const { uploadFile, uploadFiles, getUploadStats } = useGlobalUpload();

  const handleUploadComplete = (response: any, file: File) => {
    console.log('Upload completed:', { response, fileName: file.name });
    setUploadResults(prev => [...prev, {
      fileName: file.name,
      response,
      timestamp: new Date().toLocaleTimeString(),
      status: 'success'
    }]);
  };

  const handleUploadError = (error: any, file: File) => {
    console.error('Upload error:', { error, fileName: file.name });
    setUploadResults(prev => [...prev, {
      fileName: file.name,
      error: error.message,
      timestamp: new Date().toLocaleTimeString(),
      status: 'error'
    }]);
  };

  const handleProgrammaticUpload = async () => {
    // Создаем тестовый файл
    const testContent = 'This is a test file content';
    const testFile = new File([testContent], 'test-file.txt', { type: 'text/plain' });

    try {
      await uploadFile(testFile, {
        url: '/api/upload/test',
        additionalParams: { source: 'programmatic' },
        onComplete: handleUploadComplete,
        onError: handleUploadError,
      });
    } catch (error) {
      console.error('Programmatic upload failed:', error);
    }
  };

  const handleMultipleUpload = async () => {
    // Создаем несколько тестовых файлов
    const files = [
      new File(['File 1 content'], 'file1.txt', { type: 'text/plain' }),
      new File(['File 2 content'], 'file2.txt', { type: 'text/plain' }),
      new File(['File 3 content'], 'file3.txt', { type: 'text/plain' }),
    ];

    try {
      await uploadFiles(files, {
        url: '/api/upload/batch',
        additionalParams: { batch: true },
        onComplete: handleUploadComplete,
        onError: handleUploadError,
      });
    } catch (error) {
      console.error('Multiple upload failed:', error);
    }
  };

  const stats = getUploadStats();

  return (
    <Container maxWidth="lg" sx={{ py: 4 }}>
      <Typography variant="h4" gutterBottom>
        Global Upload System Test
      </Typography>
      
      <Alert severity="info" sx={{ mb: 3 }}>
        This page demonstrates the global upload system with Snackbar progress notifications.
        Upload progress appears in the bottom-right corner, notifications in bottom-left.
      </Alert>

      <Grid container spacing={3}>
        {/* Upload Status */}
        <Grid item xs={12} md={4}>
          <UploadStatus />
        </Grid>

        {/* Upload Components */}
        <Grid item xs={12} md={8}>
          <Grid container spacing={2}>
            {/* Button Upload */}
            <Grid item xs={12} sm={6}>
              <Paper sx={{ p: 3 }}>
                <Typography variant="h6" gutterBottom>
                  Button Upload
                </Typography>
                <GlobalFileUpload
                  url="/api/upload/documents"
                  additionalParams={{ category: 'documents' }}
                  onComplete={handleUploadComplete}
                  onError={handleUploadError}
                  accept="image/*,.pdf,.doc,.docx"
                  maxSize={10 * 1024 * 1024} // 10MB
                  multiple={true}
                  buttonText="Upload Documents"
                  variant="button"
                  size="large"
                />
              </Paper>
            </Grid>

            {/* Icon Upload */}
            <Grid item xs={12} sm={6}>
              <Paper sx={{ p: 3 }}>
                <Typography variant="h6" gutterBottom>
                  Icon Upload
                </Typography>
                <Box sx={{ display: 'flex', alignItems: 'center', gap: 2 }}>
                  <Typography variant="body2">
                    Quick attach:
                  </Typography>
                  <GlobalFileUpload
                    url="/api/upload/attachments"
                    additionalParams={{ type: 'attachment' }}
                    onComplete={handleUploadComplete}
                    onError={handleUploadError}
                    accept="*/*"
                    multiple={true}
                    variant="icon"
                    size="large"
                  />
                </Box>
              </Paper>
            </Grid>

            {/* Dropzone Upload */}
            <Grid item xs={12}>
              <Paper sx={{ p: 3 }}>
                <Typography variant="h6" gutterBottom>
                  Dropzone Upload
                </Typography>
                <GlobalFileUpload
                  url="/api/upload/files"
                  additionalParams={{ folder: 'uploads' }}
                  onComplete={handleUploadComplete}
                  onError={handleUploadError}
                  accept="image/*,.pdf,.doc,.docx,.txt"
                  maxSize={20 * 1024 * 1024} // 20MB
                  multiple={true}
                  buttonText="Drop files here or click to upload"
                  variant="dropzone"
                />
              </Paper>
            </Grid>

            {/* Programmatic Upload */}
            <Grid item xs={12}>
              <Paper sx={{ p: 3 }}>
                <Typography variant="h6" gutterBottom>
                  Programmatic Upload
                </Typography>
                <Box sx={{ display: 'flex', gap: 2, flexWrap: 'wrap' }}>
                  <Button 
                    variant="outlined" 
                    onClick={handleProgrammaticUpload}
                  >
                    Upload Single File
                  </Button>
                  <Button 
                    variant="outlined" 
                    onClick={handleMultipleUpload}
                  >
                    Upload Multiple Files
                  </Button>
                </Box>
                <Typography variant="body2" color="text.secondary" sx={{ mt: 1 }}>
                  These buttons create test files and upload them programmatically
                </Typography>
              </Paper>
            </Grid>
          </Grid>
        </Grid>

        {/* Upload Statistics */}
        <Grid item xs={12}>
          <Paper sx={{ p: 3 }}>
            <Typography variant="h6" gutterBottom>
              Upload Statistics
            </Typography>
            <Box sx={{ display: 'flex', gap: 2, flexWrap: 'wrap', mb: 2 }}>
              <Typography variant="body2">
                Total: {stats.total}
              </Typography>
              <Typography variant="body2" color="primary.main">
                Uploading: {stats.uploading}
              </Typography>
              <Typography variant="body2" color="success.main">
                Completed: {stats.completed}
              </Typography>
              <Typography variant="body2" color="error.main">
                Failed: {stats.failed}
              </Typography>
            </Box>
            
            {uploadResults.length > 0 && (
              <Box>
                <Typography variant="subtitle2" gutterBottom>
                  Recent Results:
                </Typography>
                <Box sx={{ maxHeight: 200, overflow: 'auto' }}>
                  {uploadResults.slice(-10).map((result, index) => (
                    <Alert 
                      key={index}
                      severity={result.status === 'success' ? 'success' : 'error'}
                      sx={{ mb: 1, fontSize: '0.875rem' }}
                    >
                      <Typography variant="body2">
                        <strong>{result.fileName}</strong> - {result.timestamp}
                      </Typography>
                      {result.status === 'success' ? (
                        <Typography variant="caption">
                          Upload successful
                        </Typography>
                      ) : (
                        <Typography variant="caption">
                          Error: {result.error}
                        </Typography>
                      )}
                    </Alert>
                  ))}
                </Box>
              </Box>
            )}
          </Paper>
        </Grid>
      </Grid>

      <Divider sx={{ my: 4 }} />

      {/* Usage Examples */}
      <Paper sx={{ p: 3 }}>
        <Typography variant="h6" gutterBottom>
          Usage Examples
        </Typography>
        
        <Box component="pre" sx={{ 
          backgroundColor: '#f5f5f5', 
          p: 2, 
          borderRadius: 1,
          fontSize: '0.875rem',
          overflow: 'auto',
          whiteSpace: 'pre-wrap'
        }}>
{`// 1. Wrap your app with UploadProvider
<UploadProvider>
  <App />
</UploadProvider>

// 2. Use GlobalFileUpload components
<GlobalFileUpload
  url="/api/upload"
  additionalParams={{ folder: 'documents' }}
  onComplete={(response, file) => console.log('Done!')}
  variant="dropzone"
  multiple={true}
/>

// 3. Use programmatic upload
const { uploadFile, uploadFiles } = useGlobalUpload();

await uploadFile(file, {
  url: '/api/upload',
  additionalParams: { category: 'images' }
});

// 4. Show upload status
<UploadStatus compact={true} activeOnly={true} />`}
        </Box>
      </Paper>
    </Container>
  );
};

export default TestGlobalUploadPage;
