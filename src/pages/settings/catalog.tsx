import React from 'react';
import { <PERSON>, Stack, TextField, Typography, Grid, styled, Paper } from '@mui/material';
import { TableByApi } from '@/components_lib/TableByApi';
import { Service, serviceSchema } from '@/components_lib/models/service';
import { useCatalog } from '@/context/CatalogContext';

const Item = styled(Paper)(({ theme }) => ({
  backgroundColor: '#fff',
  ...theme.typography.body2,
  padding: theme.spacing(1),
  textAlign: 'center',
  color: theme.palette.text.secondary,
  ...theme.applyStyles('dark', {
    backgroundColor: '#1A2027',
  }),
}));


export default function PageSettingsCatalog() {
  const catalog = useCatalog();
  return (
    <Stack spacing={2}>
      {
        catalog.catalogItems.map((element: any, index: number) => {
          return (
            <Item>
              <Grid container spacing={2}>
                <Grid size={4}>
                  <TextField
                    value={element.name} 
                    variant="filled"
                    size="small"
                  />
                </Grid>
                <Grid>
                  <Stack spacing={2}>
                    {element.options.map((opt: string, i: number) => {
                      return (
                        <Item>
                          <TextField
                            value={opt} 
                            variant="filled"
                            size="small"
                          />      
                        </Item>
                      )
                    })}
                  </Stack>
                </Grid>
              </Grid>
                {/* <pre> {JSON.stringify(element)}</pre> */}
              
            </Item>
        )})
      }
    </Stack>
  );
} 