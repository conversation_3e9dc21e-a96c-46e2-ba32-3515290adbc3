import React from 'react';
import { Box, Typography } from '@mui/material';
import { TableByApi } from '@/components_lib/TableByApi';
import { Service, serviceSchema } from '@/components_lib/models/service';
import { SchemaData } from '@/components_lib/core/schema/Schema';

interface PageSettingsTableApiParams {
  schema: SchemaData,
  apiUrl: string
}

export const PageSettingsTableApi = <T extends { id?: number|string }>({ schema, apiUrl}: PageSettingsTableApiParams) => {
  return (
    <Box>
      <TableByApi<T>
        schema={schema}
        apiUrl={apiUrl}
        params={{
          order: 'desc',
        }}
      />
    </Box>
  );
} 