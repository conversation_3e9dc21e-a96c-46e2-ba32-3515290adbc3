# Catalogs Management Component

## Overview
The Catalogs component (`/settings/catalogs`) provides a comprehensive interface for managing catalog structures with options. It supports creating, editing, and deleting catalogs that contain lists of options.

## Features

### 1. **View Catalogs**
- Displays all catalogs in a card-based grid layout
- Shows catalog name and number of options
- Displays options as chips for easy viewing

### 2. **Create New Catalog**
- Click "Создать каталог" (Create Catalog) button
- Enter catalog name
- Add multiple options by typing and clicking "Добавить" (Add)
- Options are displayed as removable chips

### 3. **Edit Existing Catalog**
- Click the edit icon on any catalog card
- Modify catalog name
- Add or remove options
- Changes are saved to the server

### 4. **Delete Catalog**
- Click the delete icon on any catalog card
- Confirmation dialog prevents accidental deletion
- Catalog is permanently removed from the server

## Data Structure

The component manages catalogs with the following structure:

```typescript
interface CatalogItem {
  id: number;
  name: string;
  options: string[];
}
```

Example data:
```json
[
  {
    "id": 1,
    "name": "status",
    "options": ["closed", "inactive", "hourly", "subscription"]
  },
  {
    "id": 2,
    "name": "legal_ent_type", 
    "options": ["LLC", "S-CORP", "C-CORP", "SMLLC", "LP"]
  },
  {
    "id": 3,
    "name": "fed_tax_forms",
    "options": ["1120", "1065", "PROFORMA1120+5472", "1120+CA", "DISREG", "N/A", "SMLLC"]
  }
]
```

## API Integration

The component integrates with the following API endpoints:

- `GET /catalogs` - Fetch all catalogs
- `POST /catalogs` - Create new catalog
- `PUT /catalogs/:id` - Update existing catalog
- `DELETE /catalogs/:id` - Delete catalog

## Usage in Forms

Once catalogs are created, they can be used in form schemas by referencing the catalog name:

```typescript
const mySchema = {
  status: {
    type: 'select',
    label: 'Status',
    catalog: 'status', // References the catalog by name
    required: true
  },
  legalEntityType: {
    type: 'select',
    label: 'Legal Entity Type',
    catalog: 'legal_ent_type',
    required: false
  }
};
```

## Navigation

The component is accessible via:
- URL: `/settings/catalogs`
- Navigation menu: Settings → Catalogs

## Error Handling

The component includes comprehensive error handling:
- Network errors are displayed as alerts
- Validation errors prevent invalid submissions
- Success messages confirm completed actions
- Loading states prevent multiple simultaneous operations

## User Interface

- **Responsive Design**: Works on desktop and mobile devices
- **Material-UI Components**: Consistent with application design
- **Intuitive Icons**: Clear visual indicators for actions
- **Confirmation Dialogs**: Prevent accidental data loss
- **Real-time Updates**: Changes are immediately reflected in the UI
