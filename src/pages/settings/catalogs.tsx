import React, { useState, useEffect } from 'react';
import {
  <PERSON>,
  <PERSON>ack,
  TextField,
  Typography,
  Grid,
  styled,
  Paper,
  IconButton,
  Button,
  Dialog,
  DialogTitle,
  DialogContent,
  DialogActions,
  Chip,
  Alert,
  Tooltip,
  Card,
  CardContent,
  CardActions,
  Divider,
} from '@mui/material';
import {
  Add as AddIcon,
  Delete as DeleteIcon,
  Edit as EditIcon,
  Save as SaveIcon,
  Cancel as CancelIcon,
  AddCircle as AddCircleIcon,
} from '@mui/icons-material';
import { useCatalog } from '@/context/CatalogContext';
import { apiClient } from '@/utils/api';
import { v4 as uuidv4 } from 'uuid';

// const Item = styled(Paper)(({ theme }) => ({
//   backgroundColor: '#fff',
//   ...theme.typography.body2,
//   padding: theme.spacing(2),
//   color: (theme.vars ?? theme).palette.text.secondary,
//   ...theme.applyStyles('dark', {
//     backgroundColor: '#1A2027',
//   }),
// }));

interface CatalogItem {
  id: number;
  name: string;
  options: string[];
}

interface EditingState {
  id?: number | null;
  name: string;
  options: string[];
  newOption: string;
}

const clearItem: EditingState = {
      id: null,
      name: '',
      options: [],
      newOption: '',
    }

export default function PageSettingsCatalogs() {
  const catalog = useCatalog();
  const [editingState, setEditingState] = useState<EditingState>({...clearItem});
  const [dialogOpen, setDialogOpen] = useState(false);
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState<string>('');
  const [success, setSuccess] = useState<string>('');

  const resetEditingState = () => {
    setEditingState({...clearItem});
  };

  const handleCreate = () => {
    resetEditingState();
    setDialogOpen(true);
  };

  const handleEdit = (item: CatalogItem) => {
    setEditingState({ ...item, newOption: '' });
    setDialogOpen(true);
  };

  const handleAddOption = () => {
    if (editingState && editingState.newOption.trim() && !editingState.options.includes(editingState.newOption.trim())) {
      setEditingState(prev => ({
        ...prev,
        options: [...prev.options, prev.newOption.trim()],
        newOption: '',
      }));
    }
  };

  const handleRemoveOption = (optionToRemove: string) => {
    setEditingState(prev => ({
      ...prev,
      options: prev.options.filter(option => option !== optionToRemove),
    }));
  };

  const handleSave = async () => {

    if (!editingState.name.trim()) {
      setError('Название каталога обязательно');
      return;
    }

    if (editingState.options.length === 0) {
      setError('Добавьте хотя бы одну опцию');
      return;
    }

    setLoading(true);
    setError('');

    try {
      const catalogData = {
        name: editingState.name.trim(),
        options: editingState.options,
      };

      if (editingState.id) {
        // Update existing catalog
        await apiClient.put(`/catalogs/${editingState.id}`, {id: editingState.id, ...catalogData});
        setSuccess('Каталог успешно обновлен');
      } else {
        // Create new catalog
        await apiClient.post('/catalogs', catalogData);
        setSuccess('Каталог успешно создан');
      }

      // Refresh catalog data
      await catalog.updateCatalogItems();
      setDialogOpen(false);
      resetEditingState();
    } catch (err: any) {
      setError(err.response?.data?.message || 'Ошибка при сохранении каталога');
    } finally {
      setLoading(false);
    }
  };

  const handleDelete = async (item: CatalogItem) => {
    if (!window.confirm(`Вы уверены, что хотите удалить каталог "${item.name}"?`)) {
      return;
    }

    setLoading(true);
    setError('');

    try {
      await apiClient.delete(`/catalogs/${item.id}`);
      setSuccess('Каталог успешно удален');
      await catalog.updateCatalogItems();
    } catch (err: any) {
      setError(err.response?.data?.message || 'Ошибка при удалении каталога');
    } finally {
      setLoading(false);
    }
  };

  const handleCloseDialog = () => {
    setDialogOpen(false);
    resetEditingState();
    setError('');
  };

  useEffect(() => {
    if (success) {
      const timer = setTimeout(() => setSuccess(''), 3000);
      return () => clearTimeout(timer);
    }
  }, [success]);

  useEffect(() => {
    if (error) {
      const timer = setTimeout(() => setError(''), 5000);
      return () => clearTimeout(timer);
    }
  }, [error]);

  return (
    <Box>
      <Box sx={{ mb: 3, display: 'flex', justifyContent: 'space-between', alignItems: 'center' }}>
        <Typography variant="h4" component="h1">
          Управление каталогами
        </Typography>
        <Button
          variant="contained"
          startIcon={<AddIcon />}
          onClick={handleCreate}
          disabled={loading}
        >
          Создать каталог
        </Button>
      </Box>

      {error && (
        <Alert severity="error" sx={{ mb: 2 }} onClose={() => setError('')}>
          {error}
        </Alert>
      )}

      {success && (
        <Alert severity="success" sx={{ mb: 2 }} onClose={() => setSuccess('')}>
          {success}
        </Alert>
      )}

      <Grid container spacing={3}>
        {catalog.catalogItems.map((item: CatalogItem) => (
          // <Grid  size={{xs:12, md:6, lg:4}} key={item.id}>
          <Grid  size={12} key={item.id}>
            <Card>
              <CardContent>
                <Grid container spacing={2}>
                  <Grid size={4}>
                    <Typography variant="h6" component="h2" gutterBottom>
                      {item.name}
                    </Typography>
                    <Tooltip title="Редактировать">
                      <IconButton
                        size="small"
                        onClick={() => handleEdit(item)}
                        disabled={loading}
                      >
                        <EditIcon />
                      </IconButton>
                    </Tooltip>
                    <Tooltip title="Удалить">
                      <IconButton
                        size="small"
                        color="error"
                        onClick={() => handleDelete(item)}
                        disabled={loading}
                      >
                        <DeleteIcon />
                      </IconButton>
                    </Tooltip>
                  </Grid>
                  <Grid size={8}>
                    <Typography variant="body2" color="text.secondary" gutterBottom>
                      Options ({item.options.length}):
                    </Typography>
                    <Box sx={{ display: 'flex', flexWrap: 'wrap', gap: 0.5, mt: 1 }}>
                      {item.options.map((option: string, index: number) => (
                        <Chip
                          key={index}
                          label={option}
                          size="small"
                          variant="outlined"
                        />
                      ))}
                    </Box>
                  </Grid>
                </Grid>
              </CardContent>
            </Card>
          </Grid>
        ))}
      </Grid>

      {/* Edit/Create Dialog */}
      <Dialog
        open={dialogOpen}
        onClose={handleCloseDialog}
        maxWidth="md"
        fullWidth
      >
        <DialogTitle>
          {editingState.id ? 'Edit catalog' : 'Create catalog'}
        </DialogTitle>
        <DialogContent>
          <Box sx={{ mt: 2 }}>
            <TextField
              fullWidth
              label="Catalog name"
              value={editingState.name}
              onChange={(e) => setEditingState(prev => ({ ...prev, name: e.target.value }))}
              margin="normal"
              required
            />

            <Typography variant="h6" sx={{ mt: 3, mb: 2 }}>
              Options
            </Typography>

            <Box sx={{ display: 'flex', flexWrap: 'wrap', gap: 1 }}>
              {editingState.options.map((option: string, index: number) => (
                <Chip
                  key={index}
                  label={option}
                  onDelete={() => handleRemoveOption(option)}
                  color="primary"
                />
              ))}
              <TextField
                size="small"
                // fullWidth
                label="New option"
                value={editingState.newOption}
                onChange={(e) => setEditingState(prev => ({ ...prev, newOption: e.target.value }))}
                onKeyPress={(e) => {
                  if (e.key === 'Enter') {
                    e.preventDefault();
                    handleAddOption();
                  }
                }}
              />
              <IconButton
                size="small"
                onClick={handleAddOption}
                disabled={!editingState.newOption.trim()}
                ><AddCircleIcon />
              </IconButton>
            </Box>
          </Box>
        </DialogContent>
        <DialogActions>
          <Button onClick={handleCloseDialog} disabled={loading}>
            Cancel
          </Button>
          <Button
            onClick={handleSave}
            variant="contained"
            disabled={loading || !editingState.name.trim() || editingState.options.length === 0}
            startIcon={<SaveIcon />}
          >
            {editingState.id ? 'Save' : 'Create'}
          </Button>
        </DialogActions>
      </Dialog>
    </Box>
  );
}
