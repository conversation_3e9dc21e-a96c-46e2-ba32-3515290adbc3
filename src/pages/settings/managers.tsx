import React from 'react';
import { <PERSON>, Typography, Fab, IconButton } from '@mui/material';
import { TableByApi } from '@/components_lib/TableByApi';
import { Manager, managerSchema } from '@/components_lib/models/manager';
import { apiClient } from '@/utils/api';
import { SchemaData } from '@/components_lib/core/schema/Schema';
import { Settings } from '@mui/icons-material';
import { PageSettingsTableApi } from './settings_table_api';
// import { SchemaData } from '@/components_lib/core/schemaDate';

export const PageSettingsManagers: React.FC = () => {
  const [schema, setSchema] = React.useState<SchemaData>(managerSchema)

  const getRoles = async () => {
    const roles = await apiClient.get<any>('/managers/roles')
    console.log('roles', roles)
    const role_name = managerSchema.role_name
    role_name.options = roles.items.map((role: any) => role.name)
    setSchema({
      ...schema,
      role_name
    })
  }

  React.useEffect(() => {
    getRoles()
  }, [])

  return (
    <PageSettingsTableApi<Manager> schema={schema} apiUrl='/managers'></PageSettingsTableApi>
  );
}; 

