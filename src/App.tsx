import * as React from 'react';
import DashboardIcon from '@mui/icons-material/Dashboard';
import ShoppingCartIcon from '@mui/icons-material/ShoppingCart';
import { ReactRouterAppProvider } from '@toolpad/core/react-router';
import Diversity2Icon from '@mui/icons-material/Diversity2';
import { Outlet, useNavigate } from 'react-router';
import type { Navigation } from '@toolpad/core/AppProvider';
import {Session, SessionContext } from '@/context/SessionContext';
import { authInfo } from './api/user';
import { LocalizationProvider } from '@mui/x-date-pickers';
import { AdapterDayjs } from '@mui/x-date-pickers/AdapterDayjs';
import BusinessSharpIcon from '@mui/icons-material/BusinessSharp';
import SupervisorAccountSharpIcon from '@mui/icons-material/SupervisorAccountSharp';
import { debounce } from '@mui/material';

import { AppProvider, Router } from '@toolpad/core/AppProvider';
import { CatalogProvider } from './context/CatalogContext';
import { AuthContext, AuthProvider } from './context/AuthContext';
import ManageAccountsIcon from '@mui/icons-material/ManageAccounts';
import RecentActorsIcon from '@mui/icons-material/RecentActors';
import SettingsSuggestIcon from '@mui/icons-material/SettingsSuggest';
import SourceIcon from '@mui/icons-material/Source';
import CategoryIcon from '@mui/icons-material/Category';

const NAVIGATION: Navigation = [
  {
    kind: 'header',
    title: 'Main items',
  },
  // {
  //   title: 'Dashboard',
  //   // icon: <Dashboard />,
  //   icon: <DashboardIcon />,
  // },
  {
    segment: 'clients',
    title: 'Clients',
    // icon: <ShoppingCart />,
    icon: <Diversity2Icon />,
  },
  {
    segment: 'settings/addresses',
    title: 'Addresses',
    // icon: <ShoppingCart />,
    icon: <BusinessSharpIcon />,
  },
  {
    segment: 'settings/peoples',
    title: 'People',
    icon: <SupervisorAccountSharpIcon />,
  },
  {
    segment: 'settings/managers',
    title: 'Managers',
    icon: <ManageAccountsIcon />,
  },
  {
    segment: 'settings/reg_agents',
    title: 'Reg Aggents',
    icon: <RecentActorsIcon/>
  },
  {
    segment: 'settings/services',
    title: 'Services',
    icon: <SettingsSuggestIcon/>
  },
  {
    segment: 'settings/sources',
    title: 'Sources',
    icon: <SourceIcon />
  },
  {
    segment: 'settings/catalogs',
    title: 'Catalogs',
    icon: <CategoryIcon />
  }
  // {
  //   segment: 'orders',
  //   title: 'Orders',
  //   // icon: <ShoppingCart />,
  //   icon: <ShoppingCartIcon />,
  // },
];

const BRANDING = {
  title: 'ICS',
};


export default function App() {

  const navigate = useNavigate();
  const [loading, setLoading] = React.useState(true);

  const {user, session, logout} = React.useContext(AuthContext)
  console.log('Init app');

  const signIn = React.useCallback(() => {
    navigate('/sign-in');
  }, [navigate]);

  const signOut = React.useCallback(() => {
    logout()
  }, [navigate]);  

  return (
    <>
    {/* <AuthProvider></AuthProvider> */}
      {/* <SessionContext.Provider value={sessionContextValue}>  */}
        <CatalogProvider>
          <LocalizationProvider dateAdapter={AdapterDayjs}>
            {/* <AppProvider navigation={NAVIGATION} router={router} theme={theme}>

            </AppProvider> */}
            <ReactRouterAppProvider 
              navigation={NAVIGATION} 
              branding={BRANDING}
              session={session}
              authentication={{ signIn, signOut }}
              >
                {/* <div>{JSON.stringify(session)}</div> */}
              <Outlet />
            </ReactRouterAppProvider>
          </LocalizationProvider>
        </CatalogProvider>
      {/* </SessionContext.Provider> */}
    </>
  );
}
