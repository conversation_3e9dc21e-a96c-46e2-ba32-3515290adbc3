export default async function fetcher(url, payload) {
    debugger
    return fetch(url, {
        method: "POST",
        headers: {
            "Content-Type": "application/json",
        },
        body: JSON.stringify(payload),
    }).then((response) => {
        if (!response) {
            throw new Error("Network response was not ok");
        } else {
            return response.json();
        }
    });
};