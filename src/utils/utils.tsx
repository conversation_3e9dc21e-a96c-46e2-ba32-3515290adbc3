import CheckCircleIcon from '@mui/icons-material/CheckCircle';
import PendingIcon from '@mui/icons-material/Pending';
import ErrorIcon from '@mui/icons-material/Error';
import { Tooltip, Typography } from '@mui/material';
import React from 'react';

export const StatusIcon =  ({status}:any) => {
    let Icon = PendingIcon;
    let color: any = 'warning'
    switch(status.status) {
        case 'ERROR': 
            Icon = ErrorIcon
            color = 'error'
        case 'SUCCESS':
            Icon = CheckCircleIcon
            color = 'success'
    }

    let i = <Icon 
                fontSize="small" 
                color={color} />
    let tooltip = <React.Fragment>
            <Typography color="inherit" variant='h6'>{status.status}</Typography>
            {status.message && <p>{status.message}</p>}
          </React.Fragment>
          
    return <Tooltip title={tooltip}>{i}</Tooltip>
}

export const createQueryString = (queryObject: any = {}) => {
  let queryString = Object.keys(queryObject)
    .filter((key) => queryObject[key] && !(Array.isArray(queryObject[key]) && !queryObject[key].length))
    .map((key) => {
      return Array.isArray(queryObject[key]) ? queryObject[key].map(item => `${encodeURIComponent(key)}=${encodeURIComponent(item)}`).join('&') : `${encodeURIComponent(key)}=${encodeURIComponent(queryObject[key])}`;
    }).join('&');
  return queryString ? `?${queryString}` : "";
};

export const queryStringToObject = (queryString = "", options: any = {}) => {
  let queryObject: any = {};
  queryString && decodeURIComponent(queryString.replace('?', '')).split('&').map((itemString) => {
    let [itemKey, itemValue] = itemString.split("=");
    if (options.hasOwnProperty(itemKey)) {
      if (!queryObject[itemKey] && Array.isArray(options[itemKey])) {
        queryObject[itemKey] = [];
      }
      Array.isArray(options[itemKey]) ? queryObject[itemKey].push(itemValue) : queryObject[itemKey] = typeof options[itemKey] === "number" ? parseInt(itemValue) : itemValue;}
  });
  return queryObject;
};