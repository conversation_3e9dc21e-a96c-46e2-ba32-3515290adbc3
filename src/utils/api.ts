import axios, { AxiosResponse } from 'axios';


export interface DataItem {
    id: number | string;
    [key: string]: any;
  }
  

export interface PaginatedData<T> {
  items: T[];
  count: number;
  limit: number;
  page: number
}

interface PaginationParams {
  page?: number;
  limit?: number;
  [key: string]: any;
}

interface ApiResponse<T> {
  items: T[];
  count: number;
}

// console.log(import.meta.env.VITE_API_BASE_URL)
// debugger
const axiosInstance = axios.create({
  baseURL: import.meta.env.VITE_API_BASE_URL || 'http://localhost:5174',
  headers: {
    'Content-Type': 'application/json',
  },
});

// Добавляем токен к каждому запросу
axiosInstance.interceptors.request.use((config) => {
  const token = localStorage.getItem('token');
  if (token) {
    config.headers.Authorization = `Bearer ${token}`;
  }
  return config;
});

const objectToFormData = (obj: any): FormData  => {
  let data = new FormData();
  Object.keys(obj).forEach((key) => {
      data.append(key, obj[key]);
  })
  return data; 
}

axiosInstance.interceptors.response.use(
  (response) => response,
  (error) => {
    console.error('API Error:', error);
    return Promise.reject(error);
  }
);

export const apiClient = {
  get: async <T>(url: string, params?: PaginationParams): Promise<T> => {
    const response = await axiosInstance.get<T>(url, { params });
    return response.data;
  },

  post: async <T>(url: string, data: any): Promise<T> => {
    const response = await axiosInstance.post<T>(url, data);
    return response.data;
  },

  put: async <T>(url: string, data: any): Promise<T> => {
    const response = await axiosInstance.put<T>(url, data);
    return response.data;
  },

  save: async <T>(url: string, data: any): Promise<T> => {
    const response = data.id 
            ? await axiosInstance.put<T>(`${url}/${data.id}`, data) 
            : await axiosInstance.post<T>(url, data);
    return response.data;
  },

  saveSync: <T>(url: string, data: any): Promise<AxiosResponse<T, any>> => {
    const response = data.id 
            ?  axiosInstance.put<T>(`${url}/${data.id}`, data) 
            :  axiosInstance.post<T>(url, data);
    return response // .data;
  },

  delete: async <T>(url: string): Promise<T> => {
    const response = await axiosInstance.delete<T>(url);
    return response.data;
  },

  upload: async <T>( url: string, data: any, cb : Function = () => {}): Promise<AxiosResponse<T, any>> => {
    // let formData = this.objectToFormData({...data})
    return  await axiosInstance.post( url, objectToFormData({...data}), {
        headers: { "Content-Type": "multipart/form-data" },
        onUploadProgress: (axiosProgressEvent) => {cb(axiosProgressEvent)}
        // function (axiosProgressEvent) {
          /*{
            loaded: number;
            total?: number;
            progress?: number; // in range [0..1]
            bytes: number; // how many bytes have been transferred since the last trigger (delta)
            estimated?: number; // estimated time in seconds
            rate?: number; // upload speed in bytes
            upload: true; // upload sign
          }*/
        // }
        ,
      
        onDownloadProgress: function (axiosProgressEvent) {
          /*{
            loaded: number;
            total?: number;
            progress?: number;
            bytes: number; 
            estimated?: number;
            rate?: number; // download speed in bytes
            download: true; // download sign
          }*/
        },
      }); 
}
};

export { axiosInstance };
// export type { PaginatedData, ApiResponse }; 