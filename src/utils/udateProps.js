const { MongoClient } = require('mongodb')
const { v4: uuid } = require('uuid')
const dayjs = require('dayjs')

const url = ''
const mainUrl = ''

async function getData() {
    const client = await MongoClient.connect(url)
    try {
        const db = client.db('isc-us')
        const collection = db.collection('catalogs')
        const filter = {title: 'source'}
        const data = await collection.find(filter).toArray()
        return data
    } catch (error) {
        console.error('db getCollectionData error:', error)
    }
    client.close()
}

async function updateData() {
    const data = await getData()
    const client = new MongoClient(mainUrl)
    try {
        await client.connect()
        const database = client.db('isc-us')
        const collection = database.collection('source')
        const updatePromises = data[0].options.map(async (item) => {
            const filter = { title: item}
            
            const updateDoc = {
                $set: {
                    id: uuid(),
                    title: item
                },
            }

            const update = await collection.updateOne(filter, updateDoc, { upsert: true })
            console.log(`${item} updated successfully`)
            return update
        })

        await Promise.all(updatePromises)
        console.log('All data have been updated successfully')
    } catch (e) {
        console.error('Error data updating: ', e)
    } finally {
        await client.close()
    }
}

updateData()
