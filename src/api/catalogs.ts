import { apiClients } from './clients';
import axios from "axios";
import dayjs from "dayjs";

const api_uri = '/api/v1/catalogs'

// export const apiCatalogs = () => {}

    async function list() {
        // try {
            return await axios.get(api_uri).then( r => r.data.items )
        // } catch (err) {
        //     debugger;
        // }
        
    }
    
    async function getItem(id?: string | number ) {
        if(!id || id === 'new') {
            return  defaultItem
        }
        return await axios.get(`${api_uri}/${id}`).then( (r) => r.data )
    }

    async function save(item: any) {
        if(item.id) {
            return await axios.put(`${api_uri}/${item.id}`, item)
        }
        return await axios.post(api_uri, item)
    }

    async function remove(item: any) {
        return await axios.delete(`${api_uri}/${item.id}`)
    }

export const apiCatalogs = {list, getItem, save, remove}
// export default {list, getItem, save, remove}

const defaultItem = {}
