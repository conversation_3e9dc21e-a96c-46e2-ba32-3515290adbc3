import axios from "axios";

export  const apiPath = '/api/v1'

export class useAPIObject {
    loading = false;
    _path: string = ''
    constructor(path: string) {
        this.path = path
    }

    set path (p) {
        this._path = `${apiPath}/${p}`
    }

    get path () {
        return this._path;
    }

    action (action: string, params?: any) {
        let r = axios.get(`${this.path}/${action}`, {params}).then( (res) => res.data);
        return r;
    } 

    getList(params = {}) {
        let r = axios.get(this.path, {params}).then( (res) => res.data);
        return r;
    }

    getItem(id_or_obj: any, params = {}) {
        let id = id_or_obj && id_or_obj.id ? id_or_obj.id : id_or_obj;
        let r = axios.get(`this.path/${id}`, {params}).then( (res) => res.data);
        return r
    }

    isNew(item: any) {
        return !item.id  || item.isNew || item.id == 'new'
    }

    save(item: any, params = {}) {
        let res;
        if( this.isNew(item) ) {
            let itemData = {...item}
            delete itemData.id
            delete itemData.isNew
            res = axios.post(this.path, itemData) //.then(r => r.data)
        } else {
            res = axios.put(`${this.path}/${item.id}`, item) //.then(r => r.data)
        }
        
        return res.then(
            (r) => r.data
            // (error) => error
        )
    }

    delete(id_or_obj: any, params = {}) {
        let id = id_or_obj && id_or_obj.id ? id_or_obj.id : id_or_obj;
        let r = axios.delete(`${this.path}/${id}`, {params}).then( (res) => res.data);
        return r
    }

    objectToFormData(obj: any): FormData {
        let data = new FormData();
        Object.keys(obj).forEach((key) => {
            data.append(key, obj[key]);
        })
        return data; 
    }

    newElement = {}
    formConfig = []
}


export function useApi(path: string) {
    return new useAPIObject(path);
}