export const newElement: any = {
   "address_id": "c28e21e2-bd43-4ef6-b3d2-e24c6c5041ee",
   "id": "63459407-fc14-4506-b76d-6a9fc32c7b97",
   "address_type": "Mailing address",
   "phone": null,
   "note": null,
   "updated_at": null,
   "client_id": "91d05654-d8b5-48a0-a697-fa47fabb3af9",
   "renewal_date": null,
   "paid_by": null,
   "created_at": null,
   "address": {
       "street": "541 Jefferson Ave STE 100",
       "pobox": "",
       "state": "CA",
       "country": "USA",
       "updated_at": null,
       "city": "Redwood City",
       "id": "c28e21e2-bd43-4ef6-b3d2-e24c6c5041ee",
       "full_address": "541 Jefferson Ave STE 100, Redwood City, CA 94063, USA",
       "zip": "94063",
       "created_at": null
   }
}

export const formConfig: any[] = [
   { size: {xs: 4}, type: "select", field: 'address_type', catalogName: 'address_type'},
   { size: {xs: 4}, type: "select-combo", field: 'address', label: 'address', catalogName: 'addresses'},
   { size: {xs: 2}, type: "select", field: 'paid_by', catalogName: 'address_paid_by'   },
   { size: {xs: 2}, type: "date-picker", field: 'renewal_date', },

   { size: {xs: 2}, type: "text", field: 'phone',},
   { size: {xs: 10}, type: 'text', field: 'note' },
]

 export const modelClientAddress = {newElement, formConfig}