import axios from "axios"
import { useAPIObject } from "./api";

export const authInfo = (callback: Function) => {
    console.log('authInfo');
    axios.get('/api/v1/auth/info').then( (r) => {callback(r.data)})
}
export class useAPIAuthObject extends useAPIObject {
    info () {
        console.log('api', `${this.path}/info`)
        return axios.get(`${this.path}/info`).then( (res) => res.data);
    }   

    logout() {
        return axios.post(`${this.path}/logout`).then( (res) => res.data);
    }

    login(formData: any = {}) {
        return axios.post(`${this.path}/login`, {...formData}).then( (res) => res.data);
    }
}

export function useApiAuth(): useAPIAuthObject {
    return new useAPIAuthObject('auth');
}

export interface UserInterface  {
    "id":number|string,
    "name": string,
    "info":null,
    "login":string,
    "confirmed":true,
    "created_at":string,
    "updated_at":string,
    "last_seen_at":string,
    "last_login_at":string,
    "last_activity_at":string,
    "session": any, //{"id":"85eb767a-e93b-4e30-af16-a134d039079a","ttl":"2026-02-12 20:24:11","user_id":10,"extra":null},
    "org_permissions": any[] //["owner","all_chat.view","file.manage","workspace.manage","group.manage","members.manage","metrics.manage","all_chat.manage"]
  }
  