// import { apiClients } from './catalogs';
import axios from "axios";
import dayjs from "dayjs";

const api_uri = '/api/v1/clients'

// export const apiClients = () => {}

    async function list(params = {}) {
        // debugger
        return await axios.get(api_uri, {params}).then( r => r.data)
    }
    
    async function getItem(id?: string | number ) {
        if(!id || id === 'new') {
            return  defaultItem
        }
        return await axios.get(`${api_uri}/${id}`).then( (r) => r.data )
    }

    async function save(item: any) {
        if(item.id) {
            return await axios.put(`${api_uri}/${item.id}`, item);
        }
        return await axios.post(api_uri, item)
    }

    async function remove(item: any) {
        return await axios.delete(`${api_uri}/${item.id}`)
    }

    async function tabs () {
        return await axios.get(`${api_uri}/tabs_available`).then((r) => r.data);
    }


    // files 
    function files_list(item: any) {
        return  axios.get(`${api_uri}/${item.id}/files`).then((r) => r.data )
    }

    async function contacts_list(item: any) {
        return await axios.get(`${api_uri}/${item.id}/contacts`).then( (r)=> r.data )
    }

// }

export const apiClients = {list, getItem, save, remove, tabs, files_list, contacts_list}

// export default {list, getItem, save, remove}


const defaultItem = {
    // id: newUid,
    // uid: newUid,
    name: "",

    date: dayjs().format(),
    changed: null,
    client: '',
    status: '',
    description: '',
    active_since: null,
    source: '',
    manager: null,
    total_shares: 0,
    optional: 0,
    legal_ent_type: '',
    incorp_by: null,
    ein_2: '',
    naicscode: '',
    fedtaxforms: '',
    statetaxforms: null,
    since: null,
    reg_state_1: '',
    state_entity_1: '',
    last_renewal_1: null,
    last_soi_filed_1: null,
    regpayby_1: 'self',
    reg_agent_1: '',
    stock_authorized_1: 0,
    stock_issued_1: 0,
    reg_state_2: null,
    regpayby_2: null,
    last_renewal_2: null,
    state_entity_2: '',
    stock_authorized_2: 0,
    last_soi_filed_2: null,
    stock_issued_2: 0,
    reg_agent_2: null,
    agr_signed: null,
    agreement_sum: 0,
    monthly_bill: 0,
    billing_method: '',
    notes_agreement: '',
    bookkeeping: null,
    payroll: null,
    fye: 'Dec.',
    cpa: 'MB',
    accounting_method: null,
    subsidiary_to_consolidate: null,
    subsidiary_legal_entity_type: null,
    fye_for_subsidiary: 'Dec.',
    subjurisd: null,
    account: '',
    bank_name_1: null,
    aba_number_1: null,
    control_by_1: null,
    none_banks: null,
    reg_date_1: '',
    reg_date_2: null,
    bank_contact_1: '',
    date_opened_1: null,
    authorized_signer_1_1: '',
    authorized_signer_2_1: '',
    authorized_signer_3_1: '',
    account_add: '',
    control_by_2: null,
    date_opened_2: null,
    authorized_signer_1_2: '',
    authorized_signer_2_2: '',
    authorized_signer_3_2: '',
    legal_address: '',
    mailing_address: '',
    tax_return_address: null,
    foreign_address: null,
    renewal_date: null,
    paid_by: null,
    dissolution_date: null,
    withdrawal_date: null,
    physical_address: null,
    renewal_date_mail: null,
    paid_by_mail: null,
    disable_secreg: false,
    notes_main: '',
    notes_registration: '',
    notes_address: '',
    notes_accounting: '',
    notes_contacts: '',
    notes_shareholders: '',
    bank_name_2: null,
    bank_contact_2: '',
    sec_account_main_2: '',
    sec_controlled_by_2: null,
    sec_main_date_opened_2: null,
    sec_authorized_signer_1: '',
    sec_account_add: '',
    sec_control_by_add: null,
    sec_date_opened_add: null,
    sec_authorized_signer_add_1: '',
    sec_account_note: '',
    shares_authorized_preferred_1: 0,
    shares_issued_preferred_1: 0,
    company_phone: '',
    aba_number_2: null,
    addresses: [],
    tax_reporting: [],
    debit_cards: [],
    payment_systems: [],
    contacts: [],
    shareholders: [],
    services: [],
    tasks: [],
    files: [],
 }
