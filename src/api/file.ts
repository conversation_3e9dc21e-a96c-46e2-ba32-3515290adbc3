import axios from "axios"
import { useAPIObject } from "./api";

export class useAPIFilesObject extends useAPIObject {
    async upload( file: any, data: any, cb : Function = () => {}) {
        let formData = this.objectToFormData({file, ...data})
        // debugger
        return  await axios.post(`${this.path}`, formData, {
            headers: { "Content-Type": "multipart/form-data" },
            onUploadProgress: (axiosProgressEvent) => {cb(axiosProgressEvent)}
            // function (axiosProgressEvent) {
              /*{
                loaded: number;
                total?: number;
                progress?: number; // in range [0..1]
                bytes: number; // how many bytes have been transferred since the last trigger (delta)
                estimated?: number; // estimated time in seconds
                rate?: number; // upload speed in bytes
                upload: true; // upload sign
              }*/
            // }
            ,
          
            onDownloadProgress: function (axiosProgressEvent) {
              /*{
                loaded: number;
                total?: number;
                progress?: number;
                bytes: number; 
                estimated?: number;
                rate?: number; // download speed in bytes
                download: true; // download sign
              }*/
            },
          }); 
    }

    link(id: string | number) {
      return `${this.path}/${id}/download`
    }
}

export function useApiFiles(path?: string ): useAPIFilesObject {
    return new useAPIFilesObject( path ? `${path}/files` : `files`);
}

export interface FileModel {
    file: any,
    description?: string ,
    doc_type?: string | null,
    name?: string | null,
    progress?: number,
    isLoading?: boolean,
    error?: any,
    uid?: any
}

export const newElement: FileModel = {
    file: null,
    description: '',
    doc_type: null,
    name: null,
}
 
 export const formConfig: any[] = [
    // { size: {xs: 4}, type: "text", field: 'name'},
    { size: {xs: 3}, type: "select", field: 'doc_type', catalogName:'doc_type'},
    { size: {xs: 9}, type: "text", field: 'description'},
 ]
 

export const ModelApiFile = {newElement, formConfig, useApiFiles}
