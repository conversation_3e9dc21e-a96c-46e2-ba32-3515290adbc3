export const newElement: any = {
   "client_person_id": null,
   "bank_contact": "", 
   "last_renewal": null,
   "client_id": "6d2d61de-d7f5-49ec-ac44-0d294227eee8",
   "id": "b4058669-a92f-4047-886f-f93beca765e2",
   "aba_number": "*********",
   "bank_name": "BREX/Radius Bank", //
   "date_opened": null,
   "notes": null
}

export const formConfig: any[] = [
   { size: {xs: 4}, type: "select", field: 'bank_name', catalogName: 'bank'},
   { size: {xs: 4}, type: "text", field: 'account'},
   { size: {xs: 4}, type: 'text', field: 'account_add' },
   { size: {xs: 4}, type: 'select-combo', field: 'aba_number', catalogName: 'aba_number' },
   { size: {xs: 4}, type: 'select', field: 'control_by', catalogName: 'control_by' },
   { size: {xs: 4}, type: 'date-picker', field: 'date_opened',},
   { size: {xs: 4}, type: 'select-combo', field: 'person', catalogName: 'client_persons' },
   { size: {xs: 12}, type: 'text', field: 'note' },
]

 export const modelClientBank = {newElement, formConfig}