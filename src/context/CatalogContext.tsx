import React, { useContext } from 'react';
import { createContext, useEffect, useState } from 'react';
import { useAuth } from './AuthContext';
import { apiClient } from '@/utils/api';

interface interfaceCatalogContext {
    catalogItems: any[],
    getCatalogItem: Function,
    updateCatalogItems: Function,
    changeCatalog: Function
}

export const CatalogContext =  createContext <interfaceCatalogContext> ({
    catalogItems: [],
    getCatalogItem: (name: string) => {},
    updateCatalogItems: () => {},
    changeCatalog: (list: any[]) => {}
});

export const useCatalog = () => {
    const context = useContext(CatalogContext);
    if (!context) {
        debugger
      throw new Error('useCatalog must be used within an CatalogProvider');
    }
    return context;
  };
  

export const CatalogProvider = ({ children }: any) => {

    const [catalogItems, setCatalogItems] = useState<any[]>([]);
    const {user, isAuth} = useAuth()

    const updateCatalogItems = async () => {
        try{
            let catalos: any = await apiClient.get('/catalogs')
            setCatalogItems(catalos.items)
        } catch(error) {
            setCatalogItems([])
        }
    }

    const changeCatalog = async (list: any[]) => {
        debugger
        try{
            let catalos: any = await apiClient.post('/catalogs', list)
            debugger
            setCatalogItems(list)
        } catch(err) {
            debugger
        }
    }

    useEffect(() => {
        if(isAuth) {
            updateCatalogItems()
        } else {
            setCatalogItems([])
        }
    }, [user])  
   
    const getCatalogItem =(name: string) => {
        let c: any = catalogItems.find( (i: any) => i.name == name)
        return c ? c.options : []
    }

    return (
      <CatalogContext.Provider value={{ catalogItems, getCatalogItem , updateCatalogItems, changeCatalog}}>
        {children}
      </CatalogContext.Provider>
    );
  };
  