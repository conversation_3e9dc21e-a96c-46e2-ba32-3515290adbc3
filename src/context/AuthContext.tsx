// https://github.com/Sam-Meech-Ward/react-router-auth-context/tree/main
import { useApiAuth, UserInterface } from "@/api/user";
import { SettingsBackupRestore, Tune } from "@mui/icons-material";
import axios from "axios";
import { createContext, useContext, useState, useEffect, useRef } from "react";

// const AuthContext = createContext();

export interface Session {
  user: {
    name?: string;
    email?: string;
    image?: string;
  };
}

interface AuthContextInterface {
  user: null | UserInterface,
  session: null | Session,
  userLoading: boolean,
  isAuth: boolean,
  authError: any,
  getUserInfo?: Function,
  logIn: Function,
  logout: Function,
}

export const AuthContext = createContext <AuthContextInterface>  ({
  user: null,
  userLoading: false,
  isAuth: false,
  session: null,
  authError: null,
  getUserInfo: () => {},
  logIn: (data: any) => {},
  logout: () => {},
});

export const useAuth = () => {
  const context = useContext(AuthContext);
  if (!context) {
    throw new Error('useAuth must be used within an AuthProvider');
  }
  return context;
};

export const AuthProvider = ({children}: any) => {

  const [user, setUser] = useState<any>()
  const [isAuth, setIsAuth] = useState<boolean>(false)
  const [userLoading, setUserLoading] = useState(false)
  const [session, setSession] = useState<Session | null>(null);
  const [authError, setAuthError] = useState<any>(null);
  const authApi = useApiAuth()

  const  getUserInfo = async () => {
    setUserLoading(true)
    setAuthError(null)
    try {
      let user = await authApi.info();
      setUser(user);

      if(user && user.login)  {
        setIsAuth(true)
        setSession({user: {name: user.name, email: user.login}})
      } else {
        setIsAuth(false)
        setSession(null)
      }
    } catch (err) {
      setIsAuth(false)
      setUser(null)
      setSession(null)
      setAuthError(err);
    }
    setUserLoading(false)
    console.log("session", session)
  }

  // useEffect(() => {
  //   debugger
  //   if(user && user.login)  {
  //     setIsAuth(true)
  //     setSession({user: {name: user.name, email: user.login}})
  //   } else {
  //     setIsAuth(false)
  //     setSession(null)
  //   }
  // }, [user])

  useEffect(() => {
    getUserInfo()
  }, [])

  const logIn = async (formData: any) => {
    await authApi.login(formData)
    getUserInfo()
  } 

  const logout =  async () => {
    try {
      await authApi.logout()
      getUserInfo()
    } catch(err) {
      setAuthError(err)
    }
  }

  return (
    <AuthContext.Provider value={{ user, userLoading, isAuth, session, authError, logIn, logout }}>
      {children}
    </AuthContext.Provider>
  );
}