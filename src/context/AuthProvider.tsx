// import axios from "axios";
// import { useContext, createContext, useState } from "react";
// import { useNavigate } from "react-router-dom";

// const AuthContext = createContext( {});

// export const AuthProvider = ({ children }: any) => {

//   debugger
//   const [user, setUser] = useState(null);
//   // const [user, setUser] = useState(
//   //   () => {
//   //   debugger
//   //   return axios.get('/api/v1/auth/info')
//   //   // return {}
//   // }
// // );
//   const [token, setToken] = useState(localStorage.getItem("site") || "");
//   const navigate = useNavigate();

//   const chackUser = async () => {
//     debugger
//     const r = await axios.get('/api/v1/auth/info');
//     setUser(r.data);
//   }
//   const loginAction = async (data: any) => {
//     try {
//       const response = await fetch("your-api-endpoint/auth/login", {
//         method: "POST",
//         headers: {
//           "Content-Type": "application/json",
//         },
//         body: JSON.stringify(data),
//       });
//       const res = await response.json();
//       if (res.data) {
//         setUser(res.data.user);
//         setToken(res.token);
//         localStorage.setItem("site", res.token);
//         navigate("/dashboard");
//         return;
//       }
//       throw new Error(res.message);
//     } catch (err) {
//       console.error(err);
//     }
//   };

//   const logOut = () => {
//     debugger
//     // setUser(null);
//     setToken("");
//     localStorage.removeItem("site");
//     navigate("/login");
//   };
//   debugger
//   chackUser();

//   return (
//     <AuthContext.Provider value={{ token, user, loginAction, logOut }}>
//       {children}
//       {user}
//     </AuthContext.Provider>
//   );

// };

// // export default AuthProvider;

// export const useAuth = () => {
//   return useContext(AuthContext);
// };