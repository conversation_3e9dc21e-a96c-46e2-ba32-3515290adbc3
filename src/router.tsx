import { createBrowserRouter, Navigate } from "react-router";
import App from "./App";
import Layout from "./layouts/dashboard";
import SignIn from "./pages/user/signIn";
import SignUp from "./pages/user/SignUp";
import SignInDemo from "./pages/demo/SignInDemo";
import { page404 } from "./pages/404";
import {PageSettingsManagers} from "./pages/settings/managers";
import { ClientsListNew } from "./pages/clients_new";
import { ClientsItem } from "./pages/clients_new/item";
import PageSettingsCatalog from "./pages/settings/catalog";
import PageSettingsCatalogs from "./pages/settings/catalogs";
import { PageSettingsTableApi } from "./pages/settings/settings_table_api";
import { Service, serviceSchema } from "./components_lib/models/service";
import { RegAgent, regAgentSchema } from "./components_lib/models/reg-agetn";
import { Source, sourceSchema } from "./components_lib/models/source";
import { Person, personSchema } from "./components_lib/models/person";
import { Address, addressSchema } from "./components_lib/models/address";
import { ArrayExamplePage } from "./pages/array_example";
import { Subscription, subscriptionSchema } from "./components_lib/models/subscription";
import { SchemaExamplePage } from "./pages/schema_example";
import type { Navigation } from '@toolpad/core/AppProvider';

import Diversity2Icon from '@mui/icons-material/Diversity2';
import SupervisorAccountSharpIcon from '@mui/icons-material/SupervisorAccountSharp';
import BusinessSharpIcon from '@mui/icons-material/BusinessSharp';
import ManageAccountsIcon from '@mui/icons-material/ManageAccounts';
import RecentActorsIcon from '@mui/icons-material/RecentActors';
import SettingsSuggestIcon from '@mui/icons-material/SettingsSuggest';
import SourceIcon from '@mui/icons-material/Source';
import CategoryIcon from '@mui/icons-material/Category';
import { ListTableSchemaExamplePage } from "./pages/list_table_schema_example";
import { ListTableSchemaExamplePageAPI } from "./pages/list_table_schema_example_api";
import TestAutocompletePage from "./pages/test-autocomplete";
import TestListTableViewPage from "./pages/test-list-table-view";
import { ClientUploadPage } from "./pages/clients_new/client_upload";


  const router = createBrowserRouter([
    {
        Component: App,
        children: [
          {
            path: '/',
            Component: Layout,
            children: [
              {
                path: '/',
                element: <Navigate to="/clients" replace />,
                // Component: DashboardPage,
              },
              
              // {
              //   path: '/clients',
              //   Component: ClientsList,
              // },
              // {
              //   path: '/clients/:clientID',
              //   Component: PageClientItem
              // },
              {
                path: '/clients',
                Component: ClientsListNew,
              },
              {
                path: '/clients/:clientId',
                Component: ClientsItem
              },
               {
                path: '/client_upload',
                Component: ClientUploadPage
              },
              {
                path: '/settings/addresses',
                // Component: PageSettingsAddresses
                element: <PageSettingsTableApi<Address> schema={addressSchema} apiUrl='/addresses' />
              },
              {
                path: '/settings/peoples',
                // Component: PageSettingsPersons
                element: <PageSettingsTableApi<Person> schema={personSchema} apiUrl='/client_persons' />
              },
              {
                path: '/settings/managers',
                Component: PageSettingsManagers
              },
              {
                path: '/settings/reg_agents',
                // Component: PageSettingsRegAgents
                element: <PageSettingsTableApi<RegAgent> schema={regAgentSchema} apiUrl='/reg_agents' />
              },
              {
                path: '/settings/services',
                // Component: PageSettingsServices,
                element: <PageSettingsTableApi<Service> schema={serviceSchema} apiUrl='/services' />
              },

              {
                path: '/settings/subscriptions',
                element: <PageSettingsTableApi<Subscription> schema={subscriptionSchema} apiUrl='/subscriptions' />
              },
              {
                path: '/settings/sources',
                element: <PageSettingsTableApi<Source> schema={sourceSchema} apiUrl='/sources' />
              },
              {
                path: '/settings/catalog',
                Component: PageSettingsCatalog
              },
              {
                path: '/settings/catalogs',
                Component: PageSettingsCatalogs
              },
              {
                path: '/example/ArrayExamplePage',
                Component: ArrayExamplePage
              },
              {
                path: '/example/SchemaExamplePage',
                Component: SchemaExamplePage
              },
              {
                path: '/example/ListTableSchemaExamplePage',
                Component: ListTableSchemaExamplePage
              }
              ,
              {
                path: '/example/ListTableSchemaExamplePageAPI',
                Component: ListTableSchemaExamplePageAPI
              },
              {
                path: '/test/autocomplete',
                Component: TestAutocompletePage
              },
              {
                path: '/test/list-table-view',
                Component: TestListTableViewPage
              }
              // {
              //   path: '/orders',
              //   Component: OrdersPage,
              // },
            ],
          },
          {
            path: '/sign-in',
            Component: SignIn,
          },
          {
            path: '/sign-up',
            Component: SignUp,
          },
          {
            path: '/demo-sign-in',
            Component: SignInDemo,
            // children: [
            //   {
            //     path: '/sign-in',
            //     Component: SignInDemo,
            //   }
            // ]
            
          },
          {
            path: '*',
            Component: page404
          }
        ],
      },
      
  ]);


export default router;

export const NAVIGATION: Navigation = [
  {
    kind: 'header',
    title: 'Main items',
  },
  {
    segment: 'clients',
    title: 'Clients',
    icon: <Diversity2Icon />,
  },
  {
    segment: 'client_upload',
    title: 'Client upload',
    icon: <Diversity2Icon />,
  },
  {
    segment: 'settings/addresses',
    title: 'Addresses',
    icon: <BusinessSharpIcon />,
  },
  {
    segment: 'settings/peoples',
    title: 'People',
    icon: <SupervisorAccountSharpIcon />,
  },
  {
    segment: 'settings/managers',
    title: 'Managers',
    icon: <ManageAccountsIcon />,
  },
  {
    segment: 'settings/reg_agents',
    title: 'Reg Aggents',
    icon: <RecentActorsIcon/>
  },
  {
    segment: 'settings/services',
    title: 'Services',
    icon: <SettingsSuggestIcon/>
  },
  {
    segment: 'settings/subscriptions',
    title: 'Subscriptions',
    icon: <SettingsSuggestIcon/>
  },
  {
    segment: 'settings/sources',
    title: 'Sources',
    icon: <SourceIcon />
  },
  {
    segment: 'settings/catalogs',
    title: 'Catalogs',
    icon: <CategoryIcon />
  }
  // {
  //   segment: 'orders',
  //   title: 'Orders',
  //   // icon: <ShoppingCart />,
  //   icon: <ShoppingCartIcon />,
  // },
];
