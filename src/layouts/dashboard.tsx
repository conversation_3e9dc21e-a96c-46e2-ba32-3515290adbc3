import * as React from 'react';
import { Outlet, Navigate, useLocation } from 'react-router';
import { DashboardLayout } from '@toolpad/core/DashboardLayout';
import { PageContainer } from '@toolpad/core/PageContainer';
import { useSession } from '../context/SessionContext';
import { AuthContext } from '@/context/AuthContext';
import { LinearProgress } from '@mui/material';



export default function Layout() {
  const { session } = useSession();
  const location = useLocation();

  const {user, userLoading, isAuth} = React.useContext(AuthContext)


  if (user && !userLoading && !isAuth) {
    debugger
    const redirectTo = `/sign-in?callbackUrl=${encodeURIComponent(location.pathname)}`;
    return <Navigate to={redirectTo} replace />;
  }
  else if (user && isAuth) {
    return (
      <DashboardLayout defaultSidebarCollapsed>
        <PageContainer maxWidth={false}>
          <Outlet />
        </PageContainer>
      </DashboardLayout>
    );
  } else {
    return (<><LinearProgress /></>)
  }

  
}