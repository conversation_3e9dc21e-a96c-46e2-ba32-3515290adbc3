import { Address } from "../address";
import { Person } from "../person";
import { SchemaData } from '../../core/schema/Schema';

export interface ClientPaymentCards {
    id?: string;
    uid?: string;
    
    debit_card?: string;
    last_4_digits?: string;
    expired_at?: string;
    cid?: string;
    linked_to?:string;
    card_holder?: string;
    exp?: string;

    created_at?: string | null;
    updated_at?: string | null;
}

export const clientPaymentCardsSchema: SchemaData = {

  debit_card: {
    type: 'text',
    label: 'Debit Card',
    grid: { size: { xs: 8 }}
  },
  last_4_digits: {
    type: 'text',
    label: 'Debit Card',
    grid: { size: { xs: 4 }}
  },
  card_holder: {
    type: 'text',
    label: 'Card Holder',
    grid: { size: { xs: 4 }}
  },
  expired_at:{
    type: 'date',
    label: 'Expired at',
    grid: { size: { xs: 4 }}
  } ,

  cid: {
    type: 'text',
    label: 'CID',
    grid: { size: { xs: 4 }}
  },

  linked_to: {
    type: 'text',
    label: 'Linked to',
    grid: { size: { xs: 4 }}
  },
  
  exp: {
    type: 'text',
    label: 'exp',
    grid: { size: { xs: 4 }}
  },
};