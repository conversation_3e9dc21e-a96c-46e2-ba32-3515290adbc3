import { SchemaData } from "../../core/schema/Schema";

export const clientSummarySchema: SchemaData = {
    
    name: {
      type: 'text',
      label: 'Company Name',
      required: true,
      validation: {
        pattern: /^[a-zA-Z0-9\s.,&-]+$/,
        message: 'Company name can contain letters, numbers, spaces, and basic punctuation',
      },
      grid: {size: 4}
    },
    legal_ent_type: {
        type: 'select',
        label: 'Legal Entity Type',
        catalog: 'legal_ent_type',
        grid: {
          size: { xs: 2 }
        }
    },
    ein: {
        type: 'text',
        label: 'EIN',
        grid: {
          size: { xs: 2 }
        }
    },
    active_since: {
        type: 'date',
        label: 'Active Since',
        grid: {size: 2}
    },
    status: {
      type: 'select',
      label: 'Status',
    //   required: true,
    //   options: ['hourly', 'active', 'inactive', 'suspended'],
      catalog: 'status',
      grid: {size: 2}
    },
    main_contact:{
      type:'text',
      label: 'Main contact  ',
      // disabled: true,
      readOnly: true,
      getValue: (value) => {
        let m = value.contacts.find( (v:any) => v.is_main)
        if(m) {
          return `${m.person?.full_title} | ${m.position} | phone: ${ m.phone ? m.phone : '-' } | email: ${m.email ? m.email : '-'} | pcm: ${m.pcm ?  m.pcm : '-' } `
        } else {
          return 'main contact not selected'
        }
      }

      
    }
}

export const clientSummaryAdvancedSchema: SchemaData = {
  incorp_by: {
      type: 'select',
      label: 'Incorporated By',
      catalog: 'incorp_by',
      grid: {size: { xs: 3 }}
  },
  naicscode: {
      type: 'text',
      label: 'NAICS Code',
      grid: {size: { xs: 3 }}
  },
  business_model: {
    type: 'select',
    label: 'Business Model',
    catalog: 'business_model',
    grid: { size: { xs: 3 } }
  },
  source: {
    type: 'autocomplete',
      label: 'Source',
      autocomplete: {
        apiUrl: '/sources',
        labelKey: 'title',
        valueKey: 'title',
        formatOptionLabel: (option: any) =>  typeof option === 'object' ? option.title : option ,
        saveFullObject: true,
        loadOptionsOnMount: true,
      },
    grid: {size: 3}
  },
  // manager: {
  //     type: 'autocomplete',
  //     label: 'Manager',
  //     autocomplete: {
  //       apiUrl: '/managers',
  //       labelKey: 'name',
  //       valueKey: 'id',
  //       formatOptionLabel: (option: any) =>  option.title ? option.title : '-',
  //       saveFullObject: true,
  //       loadOptionsOnMount: true,
  //     },
  //     grid: {size: 2}
  // },
  description: {
    type: 'text',
    label: 'Description',
    grid: {size: 6},
    multiline:true
  },
  notes_main: {
    type: 'text',
    label: 'Notes',
    grid: {size: 6},
    multiline:true
  },
}