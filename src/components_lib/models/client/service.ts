import { Address } from "../address";
import { Person } from "../person";
import { SchemaData } from '../../core/schema/Schema';
import { Service } from "../service";
export interface ClientService {
  id?: string;
  uid?: string;
  service: Service

  active_since: string | null;
  active_until: string | null;
  note: string | null;
  discount_percent: number | null;
  discount_amount: string | null;
  total: string | null;
}

export const clientServiceSchema: SchemaData = {
  
  service: {
    type: 'autocomplete',
    label: 'Service',
    autocomplete: {
      apiUrl: '/services',
      labelKey: 'name',
      valueKey: 'id',
      formatOptionLabel: (option: any) => {return option ? option.title : ''},
      saveFullObject: true,
      loadOptionsOnMount: true,
    },
    grid: {
      size: { xs: 3 }
    }
  },
  price: {
    type: 'text',
    label: 'Price',
    getValue: (row: any) => row.service?.price,
    disabled: true,
    grid: {
      size: { xs: 2 }
    }
  },
  price_type: {
    type: 'text',
    label: 'Price type',
    getValue: (row: any) => row.service?.price_type,
    disabled: true,
    grid: {
      size: { xs: 2 }
    }
  },
  active_since: {
    type: 'date',
    label: 'Active since',
    grid: {
      size: { xs: 2 }
    }
  },
  active_until: {
    type: 'date',
    label: 'Active until',
    grid: {
      size: { xs: 2 }
    }
  },
  discount_percent: {
    type: 'number',
    label: 'Discount percent',
    grid: {
      size: { xs: 2 }
    }
  },
  discount_amount: {
    type: 'text',
    label: 'Discount amount',
    grid: {
      size: { xs: 2 }
    }
  },
  total: {
    type: 'text',
    label: 'Total',
    grid: {
      size: { xs: 2 }
    }
  },
  note: {
    type: 'text',
    label: 'Note',
    multiline: true,
    grid: {
      size: { xs: 12}
    }
  }
  
};

export interface ClientSubscription{
  subscription_id: number,
  start_date: string | null,
  stop_date: string | null,
  status: string,
  total_price: number | null,
  discount_amount: number | null,
  discount_percent: number | null,
  notes: string | null,
  services: any[] | null,
}
