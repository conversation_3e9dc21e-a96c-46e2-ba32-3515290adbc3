import { Address } from "../address";
import { Person } from "../person";
import { SchemaData } from '../../core/schema/Schema';
import { ClientShareholders } from "./shareholders";
import { ClientShares } from "./shares";

export interface ClientCorpCapitalizationTable {
    id?: string;
    uid?: string;

    share: any;
    person: Person;
    share_amount: number;

    date: string;
    vesting: number;
    issued_percentage: number;
    authorized_percentage: number;
    note: string;

    created_at?: string | null;
    updated_at?: string | null;
}

export const clientCorpCapitalizationTableSchema: SchemaData = {

  // share: {
  //   type: 'select',
  //   label: 'Share',
  //   options: []
  // },

  share: {
    type: 'autocomplete',
    label: 'Share',
    autocomplete: {
      // apiUrl: '/client_persons',
      labelKey: 'name',
      valueKey: 'id',
      formatOptionLabel: (option: ClientShares) =>  option && option.type ? `${option.type} ${option.date}` : '-',
      saveFullObject: true
    },
  },
  person: {
    type: 'autocomplete',
    label: 'Person',
    autocomplete: {
      apiUrl: '/client_persons',
      labelKey: 'name',
      valueKey: 'id',
      formatOptionLabel: (option: Address) =>  option && option.full_title ? option.full_title : '-',
      saveFullObject: true
    },
    grid: {
      size: { xs: 4 }
    }
  },
  
  share_amount: {
    type: 'number',
    label: 'share amount',
    grid: { size: { xs: 2} }
  },

  date: {
    type: 'date',
    label: 'Date',
    grid: { size: { xs: 2} }
  },

  // vesting: {
  //   type: 'number',
  //   label: 'vesting',
  //   grid: { size: { xs: 2 } }
  // },

  issued_percentage: {
    type: 'number',
    label: '% issued',
    grid: { size: { xs: 1 } },
    
  },
  authorized_percentage: {
    type: 'number',
    label: '% authorized',
    grid: { size: { xs: 1 } }
  },

  notes: {
    type: 'text',
    label: 'Notes',
    grid: { size: { xs: 12 } }
  }
};