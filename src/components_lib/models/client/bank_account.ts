import { Person } from "../person";
import { SchemaData } from "../../core/schema/Schema";
import { ClientAuthorizedSigners, clientAuthorizedSignersSchema } from "./authorized_signers";
import { ClientBankCard, clientBankCardSchema } from "./bank_card";

export interface ClientBankAccount {
    id?: string;
    uid?: string;

    // client_id?: string;
    // client_person_id?: string | null;
    bank_name?: string;
    aba_number?: string;
    account_number: string | null;
    controlled_by: string | null;
    bank_contact?: string;
    date_opened?: string | null;
    last_renewal?: string | null;
    notes?: string | null;
    authorized_signers: ClientAuthorizedSigners[];
    bank_cards: ClientBankCard[]

    
    
    // authorized_signer_id: string | null; // id того signer, кто может делать операции
  }

export const clientBankAccountSchema: SchemaData = {
  bank_name: {
    type: 'select',
    label: 'Bank Name',
    grid: {
      size: { xs: 4 }
    },
    catalog: 'bank'
  },
  
  aba_number: {
    type: 'text',
    label: 'ABA Number',
    grid: {
      size: { xs: 4 }
    },
    catalog: 'aba_number'
  },
  // aba_number: {
  //   type: 'autocomplete',
  //       label: 'ABA Number',
  //       autocomplete: {
  //         apiUrl: '/aba_number',
  //         labelKey: 'name',
  //         valueKey: 'id',
  //         formatOptionLabel: (option: any) =>  option.user ? option.user?.name : '-',
  //         saveFullObject: true,
  //         loadOptionsOnMount: true,
  //       },

  //   grid: {
  //     size: { xs: 4 }
  //   },
  //   catalog: 'aba_number'
  // },
  controlled_by: {
    type: 'select',
    label: 'Control By',
    grid: {
      size: { xs: 4 }
    },
    catalog: 'control_by'
  },
  bank_contact: {
    type: 'text',
    label: 'Bank Contact',
    grid: {
      size: { xs: 4 }
    },
    multiline: true
  },
  // authorized_signer_person: {
  //   type: 'autocomplete',
  //       label: 'Authorized Signer',
  //       autocomplete: {
  //         apiUrl: '/client_persons',
  //         labelKey: 'name', 
  //         valueKey: 'id',
  //         formatOptionLabel: (option: Person) =>  option && option.full_title ? option.full_title : '-',
  //         saveFullObject: true
  //       },
  //       grid: {
  //         size: { xs: 4 }
  //       }
  // },
  date_opened: {
    type: 'date',
    label: 'Date Opened',
    grid: {
      size: { xs: 4 }
    },
  },
  last_renewal: {
    type: 'date',
    label: 'Last Renewal',
    grid: {
      size: { xs: 4 }
    },
  },
 
  notes: {
    type: 'text',
    label: 'Note',
    grid: {
      size: { xs: 12 }
    },
    multiline: true
  },
  authorized_signers: {
    label: 'Authorized signers',
    type: 'list_schema',
    schema: clientAuthorizedSignersSchema,
    grid: {
      size: { xs: 12 }
    }
  },

  bank_cards: {
    label: 'Cards',
    type: 'list_schema',
    schema: clientBankCardSchema,
    grid: {
      size: { xs: 12 }
    }
  }

  
};