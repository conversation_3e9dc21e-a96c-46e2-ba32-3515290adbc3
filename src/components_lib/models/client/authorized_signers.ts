import { Address } from "../address";
import { Person } from "../person";
import { SchemaData } from '../../core/schema/Schema';
export interface ClientAuthorizedSigners {
    id?: string;
    uid?: string;

    person: Person
}

export const clientAuthorizedSignersSchema: SchemaData = {

  person: {
    type: 'autocomplete',
    label: 'Персона',
    autocomplete: {
      apiUrl: '/client_persons',
      labelKey: 'name',
      valueKey: 'id',
      formatOptionLabel: (option: any) => {return option ? option.full_title : ''},
      saveFullObject: true,
      loadOptionsOnMount: true,
    },
    editable:true
  },
};