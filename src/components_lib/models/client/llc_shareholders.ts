import { Address } from "../address";
import { Person } from "../person";
import { SchemaData } from '../../core/schema/Schema';

export interface ClientLLCShareholders {
    id?: string;
    uid?: string;
    person: Person;
    position: string;
    ownership: string;
    is_managing_member: boolean;
    note: string;

    created_at?: string | null;
    updated_at?: string | null;
}

export const clientLLCShareholdersSchema: SchemaData = {
  person: {
    type: 'autocomplete',
    label: 'Person',
    autocomplete: {
      apiUrl: '/client_persons',
      labelKey: 'name',
      valueKey: 'id',
      formatOptionLabel: (option: Address) =>  option && option.full_title ? option.full_title : '-',
      saveFullObject: true
    },
    grid: {
      size: { xs: 4 }
    }
  },
  
  ownership: {
    type: 'number',
    label: 'Ownership',
    grid: { size: { xs: 4 } }
  },

  is_managing_member: {
    type: 'boolean',
    label: 'Is managing member',
    grid: { size: { xs: 4 } }
  }
};