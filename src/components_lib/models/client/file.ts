import { ListItem } from './../../ListFormBySchema copy';
import { SchemaData } from '@/components_lib/core/schema/Schema';
// export interface File {
//     id: string;
//     date: string;
//     name: string;
//     description: string | null;
//     hash: string | null;
//     file_type: string;
//     doc_type: string;
//     client_id: string;
//     client_person_id: string | null;
//     manager_id: string | null;
//     created_at: string;
//     updated_at: string;
//   }
  


export interface ClientFile {

    id?: string;
    uid?: string;
    created_at?: string | null;
    updated_at?: string | null;

    client_id?: string | null;
    client_person_id? : string | null;
    date? : string | null;
    description? : string | null; 
    doc_type? : string | null;
    file_type? : string | null;
    hash? : string | null;
    manager_id? : string | null;
    name? : string | null;
    file?: File,
    progress?: number | null,
    error?: any | null
}

export const clientFileSchema: SchemaData = {
    name: {
        type: 'text',
        label: 'Name',
        // disabled: true,
        // editable: false,
        
    },
    file_type: {
        type: 'text',
        label: 'File type',
        editable: false,
        
    },
    doc_type: {
        type: 'select',
        label: 'DOC Type',
        catalog: 'doc_type'
    },
    status: {
        type: 'text',
        label: 'Status',
        editable: false,
        getValue: (values, key) => {
            return values.status ? values.status.status : '-' 
        },
    },
    manager: {
        type: 'text',
        label: "Manager",
        editable: false,
        getValue: (values, key) => {
            return values.manager ? values.manager.title: '-' 
        },
    },
    created_at: {
        type: 'date',
        label: 'Created at',
        editable: false,
    },
    file: {
        type: 'text',
        label: 'File name',
        visible: false,
        editable: false
    },
    progress: {
        type: 'progress',
        label: 'Progress'
    }
}

export const clientFileCreateSchema: SchemaData = {
    name: {
        type: 'text',
        label: 'Name',
        disabled: true,
        grid: {size: { xs: 4 }},
    },
    doc_type: {
        type: 'select',
        label: 'DOC Type',
        catalog: 'doc_type',
        grid: {size: { xs: 4 }},
    },
    file: {
        type: 'text',
        label: 'File name',
        visible: false,
        editable: false
    },
    progress: {
        type: 'progress',
        label: 'Progress',
        // grid: {size: { xs: 4 }},
    },
    error: {
        type: 'error',
        label: 'error'
    }
}

// { field: 'name', headerName: 'Title', width: 300, editable: false, type: 'string' },
//         { field: 'doc_type', headerName: 'Doc', editable: false,   type: 'string' },
//         { field: 'status', headerName: 'Status', editable: false,   type: 'string' ,
//             valueGetter: (value: any, row: any, col: any) => {
//                 return row.status ? row.status.status : '-'
//             }, 
//         },
//         { field: 'manager', headerName: 'Manager', editable: false,   type: 'string' ,
//             valueGetter: (value: any, row: any, col: any) => {
//                 return row.manager ? row.manager.title : '-'
//             }, 
//         },
//         // { field: 'client', headerName: 'Client', width: 350, editable: false, type: 'string' },
//         { field: 'created_at', headerName: 'Date', renderCell: (props: any) => <>{dayjs(props.value).format('MM/DD/YYYY')}</> },