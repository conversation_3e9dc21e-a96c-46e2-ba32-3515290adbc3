import { RegAgent } from '../reg-agetn';
import { SchemaData } from '../../core/schema/Schema';
import { ClientRegistrationItem } from './registration_item';

export interface ClientPrimaryRegistration extends ClientRegistrationItem {
}



export const clientPrimaryRegistrationSchema: SchemaData = {
  state_of_incorporation: {
    type: 'select',
    label: 'State of Incorporation',
    catalog: 'state',
    grid: {
      size: { xs:2}
    }
  },
  state_entity: {
    type: 'text',
    label: 'State Entity',
    grid: {
      size: { xs:4 }
    }
  },
  registered_date: {
    type: 'date',
    label: 'Date Registered',
    grid: {
      size: { xs:2 }
    }
  },
  annual_compliance_due_date: {
    type: 'date',
    label: 'Annual Compliance Due Date',
    grid: {
      size: { xs:2 }
    }
  },
  terminated_date: {
    type: 'date',
    label: 'Terminated Date',
    grid: {
      size: { xs:2 }
    }
  },

  registered_agent: {
    type: 'autocomplete',
    label: 'Registration Agent',
    autocomplete: {
      apiUrl: '/reg_agents',
      labelKey: 'name',
      valueKey: 'id',
      formatOptionLabel: (option: RegAgent) =>  option && option.title ? option.title : '-',
      saveFullObject: true,
      loadOptionsOnMount: true
    },
    grid: {
      size: { xs:4 }
    }
  },

  registered_agent_address: {
    type: 'text',
    label: 'Agent address',
    // disabled:true,
    readOnly:true,
    getValue(values, key) {
      return values.registered_agent? values.registered_agent.address.full_address : '' 
    },
    grid: {
      size: { xs:6 }
    }

  },

  billed_to: {
    type: 'select',
    label: 'Registration Payment By',
    catalog: 'regpayby',
    grid: {
      size: { xs:2 }
    }
  },

  notes: {
    type: 'text',
    label: 'Notes',
    multiline: true,
    grid: {
      size: { xs: 12 }
    }
  }
}
