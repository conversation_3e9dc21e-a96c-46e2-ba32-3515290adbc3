import { Address } from "../address";
import { Person } from "../person";
import { SchemaData } from '../../core/schema/Schema';

export interface ClientShares {
    id?: string;
    uid?: string;

    type: string
    issued_amount: number
    authorized_amount: number
    note: string;
    date: string;

    created_at?: string | null;
    updated_at?: string | null;
}

export const clientSharesSchema: SchemaData = {
  type: {
    type: 'select',
    label: 'Type',
    multiline:true,
    grid: {
      size: { xs: 2 }
    },
    catalog: 'shares_type'
  },
  date: {
    type: 'date',
    label: 'Date',
    grid: {
      size: { xs: 2 }
    },
  },
  
  authorized_amount: {
    type: 'number',
    label: 'authorized',
    grid: { size: { xs: 2} }
  },

  issued_amount: {
    type: 'number',
    label: 'issued',
    grid: { size: { xs: 2 } }
  },

  notes: {
    type: 'text',
    label: 'Notes',
    grid: { size: { xs: 4 } }
  }
};