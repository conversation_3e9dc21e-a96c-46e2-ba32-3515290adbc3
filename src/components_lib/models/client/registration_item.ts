import { RegAgent } from '../reg-agetn';
import { SchemaData } from '../../core/schema/Schema';


export interface ClientRegistrationItem {
  id?: number|string;
  uid?: number|string;

  
  state_of_incorporation: string | null;
  state_entity: string | null;
  registered_date: string | null;
  annual_compliance_due_date: string | null;
  terminated_date: string | null;

  last_renewal_date: string | null;
  
  billed_to: string | null;
  last_soi_filed: string | null;
  
  notes: string | null;

  registered_agent_id: string | null;
  registered_agent: RegAgent | null;
}

// State of Incorporation: ___State Entity #_____ Date Registered______ Annual Compliance Due Date_____ Terminated Date:___
// Registered Agent_____ Agent address_______ Billed to____(Client or ISC)



export const clientRegistrationItemSchema: SchemaData = {
  state_of_incorporation: {
    type: 'select',
    label: 'Qualified in State',
    catalog: 'state',
    grid: {
      size: { xs:2}
    }
  },
  state_entity: {
    type: 'text',
    label: 'State Entity',
    grid: {
      size: { xs:4 }
    }
  },
  registered_date: {
    type: 'date',
    label: 'Date Registered',
    grid: {
      size: { xs:2 }
    }
  },
  annual_compliance_due_date: {
    type: 'date',
    label: 'Annual Compliance Due Date',
    grid: {
      size: { xs:2 }
    }
  },
  terminated_date: {
    type: 'date',
    label: 'Terminated Date',
    grid: {
      size: { xs:2 }
    }
  },

  registered_agent: {
    type: 'autocomplete',
    label: 'Registration Agent',
    autocomplete: {
      apiUrl: '/reg_agents',
      labelKey: 'name',
      valueKey: 'id',
      formatOptionLabel: (option: RegAgent) =>  option && option.title ? option.title : '-',
      saveFullObject: true,
      loadOptionsOnMount: true
    },
    grid: {
      size: { xs:4 }
    }
  },

  registered_agent_address: {
    type: 'text',
    label: 'Agent address',
    // disabled:true,
    readOnly:true,
    getValue(values, key) {
      return values.registered_agent && values.registered_agent.address ? values.registered_agent.address.full_address : '' 
    },
    grid: {
      size: { xs:6 }
    }

  },

  billed_to: {
    type: 'select',
    label: 'Registration Payment By',
    catalog: 'regpayby',
    grid: {
      size: { xs:2 }
    }
  },

  notes: {
    type: 'text',
    label: 'Notes',
    multiline: true,
    grid: {
      size: { xs: 12 }
    }
  }
}
