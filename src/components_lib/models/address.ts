// import { SchemaData } from '@/components_lib/core/schemaDate';
import { SchemaData } from '../core/schema/Schema';
export interface Address {
  id: string;
  city: string;
  country: string;
  created_at: string | null;
  full_title: string;
  full_address?: string;
  pobox: string | null;
  state: string;
  street: string;
  updated_at: string | null;
  zip: string;
}



export const addressSchema: SchemaData = {
  street: {
    label: 'Улица',
    type: 'text',
    required: true,
    visible: true,
    editable: true,
    // validation: (value: string) => {
    //   if (!value) return 'Улица обязательна для заполнения';
    //   return null;
    // }
  },
  pobox: {
    label: 'Почтовый ящик',
    type: 'text',
    required: false,
    visible: true,
    editable: true,
    // validation: (value: string | null) => {
    //   if (value && value.length > 10) return 'Максимальная длина 10 символов';
    //   return null;
    // }
  },
  state: {
    label: 'Штат',
    type: 'text',
    required: true,
    visible: true,
    editable: true,
    // validation: (value: string) => {
    //   if (!value) return 'Штат обязателен для заполнения';
    //   if (value.length !== 2) return 'Штат должен содержать 2 символа';
    //   return null;
    // }
  },
  country: {
    label: 'Страна',
    type: 'text',
    required: true,
    visible: true,
    editable: true,
    // validation: (value: string) => {
    //   if (!value) return 'Страна обязательна для заполнения';
    //   return null;
    // }
  },
  city: {
    label: 'Город',
    type: 'text',
    required: true,
    visible: true,
    editable: true,
    // validation: (value: string) => {
    //   if (!value) return 'Город обязателен для заполнения';
    //   return null;
    // }
  },
  zip: {
    label: 'Почтовый индекс',
    type: 'text',
    required: true,
    visible: true,
    editable: true,
    // validation: (value: string) => {
    //   if (!value) return 'Почтовый индекс обязателен для заполнения';
    //   if (!/^\d{5}(-\d{4})?$/.test(value)) return 'Неверный формат почтового индекса';
    //   return null;
    // }
  },
  full_title: {
    label: 'Полный адрес',
    type: 'textarea',
    // required: true,
    visible: true,
    editable: false,
    // getValue: (item: any) => {
    //   const parts = [
    //     item.street,
    //     item.pobox,
    //     `${item.city}, ${item.state} ${item.zip}`,
    //     item.country
    //   ].filter(Boolean);
    //   return parts.join(', ');
    // }
  },
  // id: {
  //   label: 'ID',
  //   type: 'text',
  //   required: true,
  //   visible: false,
  //   editable: false
  // },
  // created_at: {
  //   label: 'Дата создания',
  //   type: 'date',
  //   required: false,
  //   visible: true,
  //   editable: false,
  //   getValue: (item: any) => item.created_at ? new Date(item.created_at) : null
  // },
  // updated_at: {
  //   label: 'Дата обновления',
  //   type: 'date',
  //   required: false,
  //   visible: true,
  //   editable: false,
  //   getValue: (item: any) => item.updated_at ? new Date(item.updated_at) : null
  // }
};