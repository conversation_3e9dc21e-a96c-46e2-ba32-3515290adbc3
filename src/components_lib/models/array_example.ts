import { SchemaData } from "../core/schema/Schema";

/**
 * Пример схемы с использованием нового типа поля 'array'
 */
export const arrayExampleSchema: SchemaData = {
  // Массив строк с отображением в виде списка
  tags: {
    type: 'array',
    label: 'Теги',
    array: {
      schema_value: {
        type: 'text',
        label: 'Тег',
        defaultValue: ''
      },
      maxItems: 10,
      allowDuplicates: false,
      variant: 'list'
    },
    grid: { size: 6 },
    defaultValue: []
  },

  // Массив строк с отображением в виде чипов
  keywords: {
    type: 'array',
    label: 'Ключевые слова',
    array: {
      schema_value: {
        type: 'text',
        label: 'Ключевое слово',
        defaultValue: ''
      },
      maxItems: 5,
      allowDuplicates: true,
      variant: 'chips'
    },
    grid: { size: 6 },
    defaultValue: []
  },

  // Массив с выбором из списка
  categories: {
    type: 'array',
    label: 'Категории',
    array: {
      schema_value: {
        type: 'select',
        label: 'Категория',
        options: [
          { value: 'tech', label: 'Технологии' },
          { value: 'business', label: 'Бизнес' },
          { value: 'education', label: 'Образование' },
          { value: 'health', label: 'Здоровье' },
          { value: 'entertainment', label: 'Развлечения' }
        ],
        defaultValue: ''
      },
      maxItems: 3,
      allowDuplicates: false,
      variant: 'table'
    },
    grid: { size: 6 },
    defaultValue: []
  },

  // Массив с автоподстановкой
  skills: {
    type: 'array',
    label: 'Навыки',
    array: {
      schema_value: {
        type: 'autocomplete',
        label: 'Навык',
        autocomplete: {
          labelKey: 'name',
          valueKey: 'id',
          saveFullObject: false,
          options: [
            { id: 'js', name: 'JavaScript' },
            { id: 'ts', name: 'TypeScript' },
            { id: 'react', name: 'React' },
            { id: 'vue', name: 'Vue.js' },
            { id: 'angular', name: 'Angular' },
            { id: 'node', name: 'Node.js' },
            { id: 'python', name: 'Python' },
            { id: 'java', name: 'Java' },
            { id: 'csharp', name: 'C#' },
            { id: 'php', name: 'PHP' }
          ]
        },
        defaultValue: null
      },
      maxItems: 8,
      allowDuplicates: false,
      variant: 'grid'
    },
    grid: { size: 6 },
    defaultValue: []
  },

  // Обычное текстовое поле для сравнения
  name: {
    type: 'text',
    label: 'Название',
    required: true,
    grid: { size: 12 }
  },

  // Обычное поле описания
  description: {
    type: 'text',
    label: 'Описание',
    multiline: true,
    grid: { size: 12 }
  }
};

/**
 * Интерфейс для данных примера
 */
export interface ArrayExampleData {
  tags: string[];
  keywords: string[];
  categories: string[];
  skills: string[];
  name: string;
  description?: string;
}

/**
 * Значения по умолчанию для примера
 */
export const defaultArrayExampleData: ArrayExampleData = {
  tags: ['пример', 'тест'],
  keywords: ['ключевое слово 1', 'ключевое слово 2'],
  categories: ['tech'],
  skills: ['js', 'react'],
  name: 'Пример использования массивов',
  description: 'Это пример демонстрирует различные способы работы с массивами в схемах форм'
};
