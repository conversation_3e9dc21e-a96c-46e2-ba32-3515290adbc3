import { SchemaData } from "../core/schema/Schema";

/**
 * Пример схемы с использованием нового типа поля 'array'
 */
export const arrayExampleSchema: SchemaData = {
  // Массив через текстовое поле с разделителем запятая
  tags: {
    type: 'array',
    label: 'Теги',
    array: {
      inputType: 'text',
      separator: ',',
      maxItems: 10,
      allowDuplicates: false
    },
    grid: { size: 6 },
    defaultValue: []
  },

  // Массив через текстовое поле с разделителем точка с запятой
  keywords: {
    type: 'array',
    label: 'Ключевые слова',
    array: {
      inputType: 'text',
      separator: ';',
      maxItems: 5,
      allowDuplicates: true
    },
    grid: { size: 6 },
    defaultValue: []
  },

  // Массив через select с множественным выбором
  categories: {
    type: 'array',
    label: 'Категории',
    array: {
      inputType: 'select',
      maxItems: 3,
      allowDuplicates: false
    },
    options: [
      { value: 'tech', label: 'Технологии' },
      { value: 'business', label: 'Бизнес' },
      { value: 'education', label: 'Образование' },
      { value: 'health', label: 'Здоровье' },
      { value: 'entertainment', label: 'Развлечения' }
    ],
    grid: { size: 6 },
    defaultValue: []
  },

  // Массив через autocomplete
  skills: {
    type: 'array',
    label: 'Навыки',
    array: {
      inputType: 'autocomplete',
      maxItems: 8,
      allowDuplicates: false
    },
    autocomplete: {
      labelKey: 'name',
      valueKey: 'id',
      saveFullObject: false,
      options: [
        { id: 'js', name: 'JavaScript' },
        { id: 'ts', name: 'TypeScript' },
        { id: 'react', name: 'React' },
        { id: 'vue', name: 'Vue.js' },
        { id: 'angular', name: 'Angular' },
        { id: 'node', name: 'Node.js' },
        { id: 'python', name: 'Python' },
        { id: 'java', name: 'Java' },
        { id: 'csharp', name: 'C#' },
        { id: 'php', name: 'PHP' }
      ]
    },
    grid: { size: 6 },
    defaultValue: []
  },

  // Обычное текстовое поле для сравнения
  name: {
    type: 'text',
    label: 'Название',
    required: true,
    grid: { size: 12 }
  },

  // Обычное поле описания
  description: {
    type: 'text',
    label: 'Описание',
    multiline: true,
    grid: { size: 12 }
  }
};

/**
 * Интерфейс для данных примера
 */
export interface ArrayExampleData {
  tags: string[];
  keywords: string[];
  categories: string[];
  skills: string[];
  name: string;
  description?: string;
}

/**
 * Значения по умолчанию для примера
 */
export const defaultArrayExampleData: ArrayExampleData = {
  tags: ['пример', 'тест'],
  keywords: ['ключевое слово 1', 'ключевое слово 2'],
  categories: ['tech'],
  skills: ['js', 'react'],
  name: 'Пример использования массивов',
  description: 'Это пример демонстрирует различные способы работы с массивами в схемах форм'
};
