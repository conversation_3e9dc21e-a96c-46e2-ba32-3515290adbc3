import { SchemaData } from '../core/schema/Schema';
import { Service, serviceSchema } from './service';

export interface SubscriptionService {
    id?: string;
    uid?: string;
    service_id: string|number;
    service: Service,
    price: number
};

export const subscriptionServiceSchema: SchemaData = {

  service: {
    type: 'autocomplete',
    label: 'Service',
    autocomplete: {
      apiUrl: '/services',
      labelKey: 'name',
      valueKey: 'id',
      formatOptionLabel: (option: any) => {return option ? `${option.title} $(${option.price})` : ''},
      saveFullObject: true,
      loadOptionsOnMount: true,
    },
    grid: {
      size: { xs: 6}
    }
  },
  price: {
    type: 'number',
    label: 'Price',
    getValue: (row) => row.price != undefined || row.price != null  ? row.price : row.service?.price,
    grid: {
      size: { xs: 6}
    }
  }
}; 

export interface Subscription {
    id?: string;
    uid?: string;
    title: string;
    price: number;
    price_type: string;
    description: string | null;
    services: SubscriptionService[]
};


export const subscriptionSchema: SchemaData = {

  title: {
    type: 'text',
    label: 'title',
    
  },
  price: {
    type: 'number',
    label: 'Price',
  },

  price_type: {
    type: 'select',
    label: 'Price type',
    catalog: 'price_type'
  },
  // test: {
  //   type: 'autocomplete',
  //   label: 'Service',
  //   autocomplete: {
  //     apiUrl: '/services',
  //     labelKey: 'name',
  //     valueKey: 'id',
  //     formatOptionLabel: (option: any) => {return option ? `${option.title} $(${option.price})` : ''},
  //     saveFullObject: true,
  //     loadOptionsOnMount: true,
  //   },
  // },

  // test1: {
  //   type: 'autocomplete',
  //   label: 'test1',
  //   autocomplete: {
  //     labelKey: 'name',
  //     valueKey: 'id',
  //     options: [
  //       { id: 1, name: 'Option 1' },
  //       { id: 2, name: 'Option 2' },
  //       { id: 3, name: 'Option 3' },
  //     ],
  //     formatOptionLabel: (option: any) => {return option ? `${option.id} (${option.name})` : ''},
  //     saveFullObject: true,
  //     loadOptionsOnMount: true,
  //   },
  // },

  services: {
    type: 'array',
    label: 'Services',
    array:{
      schema_value: {
        type: 'autocomplete',
        label: 'Service',
        autocomplete: {
          apiUrl: '/services',
          labelKey: 'name',
          valueKey: 'id',
          formatOptionLabel: (option: any) => {return option ? `${option.title} $(${option.price})` : ''},
          saveFullObject: true,
          loadOptionsOnMount: true,
        },
        // grid: {
        //   size: { xs: 6}
        // }
      },
      variant: 'list'
    },
    // schema: subscriptionServiceSchema,

    grid: {
      size: { xs: 12 }
    }
  }

  
  
  
}; 

// {
//     "title":"title",
//     "price":"100",
//     "price_type":"Monthly",
//     "services":[
//         {
//             "uid":"db642b39-0eb1-449d-a71d-b18860a952e7",
//             "service":{"id":"d1b3002c-703a-4666-ae51-8b4ea81585e9","title":"Apostille","price":100,"price_type":"One-time","description":null,"created_at":"2025-06-17 05:38:15","updated_at":"2025-06-17 05:38:15"},
//             "price":"90"
//         },
//         {
//             "uid":"db642b39-0eb1-449d-a71d-b18860a952e7",
//             "service":{"id":"d1b3002c-703a-4666-ae51-8b4ea81585e9","title":"Apostille","price":100,"price_type":"One-time","description":null,"created_at":"2025-06-17 05:38:15","updated_at":"2025-06-17 05:38:15"},
//             "price":"90"
//         }
//         ...
//     ]
// }