import { SchemaData } from '../core/schema/Schema';

export interface Service {
    id?: string;
    uid?: string;
    title:  string;
    price?: number;
    price_type?: string;
  };


export const serviceSchema: SchemaData = {

  title: {
    type: 'text',
    label: 'title',
    
  },
  price: {
    type: 'number',
    label: 'Price',
  },

  price_type: {
    type: 'select',
    label: 'Price type',
    catalog: 'price_type'
  },

  
  
  
}; 