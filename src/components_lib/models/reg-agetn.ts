import { SchemaData } from '../core/schema/Schema';
import { Address } from './address';

export interface RegAgent {
    id?: string;
    uid?: string;
    created_at?: string | null;
    updated_at?: string | null;

    nickname: string | null;
    address: Address | null;
    title:  string
  };


export const regAgentSchema: SchemaData = {

  title: {
    type: 'text',
    label: 'title',
  },
  nickname: {
    type: 'text',
    label: 'nickname',
  },
  address: {
    type: 'autocomplete',
    label: 'address',
    autocomplete: {
      apiUrl: '/addresses',
      labelKey: 'name',
      valueKey: 'id',
      formatOptionLabel: (option: any) =>  option && option.full_address ? option.full_address : '-',
      saveFullObject: true
    },
    getValue: (row: any) => row && row.address && row.address.full_address ? row.address.full_address : '-'
  },
  
  
}; 