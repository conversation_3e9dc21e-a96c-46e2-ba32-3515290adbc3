import { SchemaData } from "../core/schema";
import { Client } from "./client";

export interface ClientUpload {
    id: string;
    manager_id: string;
    detection_id: number;
    detection_item_id: number;
    output: Client;
    file_id: string;
    message: string | null;
    error_message: string | null;
    created_at: string;
    updated_at: string;
    status: string;
    file: ClientUploadFile;
};

export interface ClientUploadFile {
    id: string;
    date?: string;
    name: string;
    size: number;
    description?: string | null;
    hash: string;
    file_type: string;
    doc_type?: string | null;
    client_id?: string | null;
    client_person_id?: string | null;
    manager_id: string;
    created_at: string;
    updated_at: string;
}

export const clientUploadTableSchema: SchemaData = {
    file_name: {
        type: 'text',
        label: 'File',
        getValue: (row) => row.file?.name
    },
    status: {
        type: 'text',
        label: 'Status',
    },
    client_name: {
        type: 'text',
        label: 'Client Name',
        getValue: (row) => row.output?.name
    },
    client_ein: {
        type: 'text',
        label: 'Client Name',
        getValue: (row) => row.output?.ein
    },
}