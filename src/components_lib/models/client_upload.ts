import { SchemaData } from "../core/schema";
import { Client } from "./client";
import { clientAccounting } from "./client/accounting";
import { clientAddressOtherSchema, clientAddressSchema } from "./client/address";
import { clientBankAccountSchema } from "./client/bank_account";
import { clientContactSchema } from "./client/contat";
import { clientCorpCapitalizationTableSchema } from "./client/corp_capitalization_table";
import { clientMainSchema } from "./client/main";
import { clientRegistrationItemSchema } from "./client/registration_item";
import { clientPrimaryRegistrationSchema } from "./client/registration_primary";
import { clientSharesSchema } from "./client/shares";
import { clientSummaryAdvancedSchema, clientSummarySchema } from "./client/summary";

export interface ClientUpload {
    id: string | number;
    manager_id: string;
    detection_id: number;
    detection_item_id: number;
    output: Client;
    file_id: string;
    message: string | null;
    error_message: string | null;
    created_at: string;
    updated_at: string;
    status: string;
    file: ClientUploadFile;
    exist?: boolean;
    client?: Client | null;
    client_output?: Client | null;
};

export interface ClientUploadFile {
    id: string;
    date?: string;
    name: string;
    size: number;
    description?: string | null;
    hash: string;
    file_type: string;
    doc_type?: string | null;
    client_id?: string | null;
    client_person_id?: string | null;
    manager_id: string;
    created_at: string;
    updated_at: string;
}

export const clientUploadTableSchema: SchemaData = {
    file_name: {
        type: 'text',
        label: 'File',
        getValue: (row) => row.file?.name,
        editable: false
        
    },
    status: {
        type: 'text',
        label: 'Status',
        editable: false
    },
    client_name: {
        type: 'text',
        label: 'Client Name',
        getValue: (row) => row.output?.name,
        editable: false
    },
    client_ein: {
        type: 'text',
        label: 'EIN',
        getValue: (row) => row.output?.ein,
        editable: false
    },
    // output: {
    //     type:"object_schema",
    //     label: 'Client',
    //     schema: clientMainSchema,
    //     visible: false,
    //     readOnly: true
    // },
    // json: {
    //     type: 'json',
    //     label: 'JSON',
    //     visible: false,
    //     readOnly: true,
    //     getValue: (row) => row.output
    // }
}

export const clientUploadViewSchema: SchemaData = {
    // ...clientMainSchema,
    ...clientSummarySchema,
    ...clientSummaryAdvancedSchema,
    label1: {type: 'title', label: 'Accounting'},
    ...clientAccounting,
    primary_registration: {
        type: 'object_schema',
        label: 'Primary Registration',
        schema: clientPrimaryRegistrationSchema,
        // visible: false,
        // readOnly: true
    },
    secondary_registrations: {
        type: 'list_schema',
        label: 'Secondary registrations',
        schema: clientRegistrationItemSchema,
        // readOnly: true
    },
    addresses:{
        type: 'list_schema',
        label: 'Addesses',
        schema: clientAddressSchema,
        // readOnly: true
    },
    ...clientAddressOtherSchema,
    contacts:{
        type: 'list_schema',
        label: 'Contacts',
        schema: clientContactSchema,
        // readOnly: true
    },
    shares: {
        type: 'list_schema',
        label: 'Shares',
        schema: clientSharesSchema ,
        // readOnly: true
    },
    capitalization_table:{
        type: 'list_schema',
        label: 'Capitalization table',
        schema: clientCorpCapitalizationTableSchema ,
        // readOnly: true
    },
    llc_shareholders: {
        type: 'list_schema',
        label: 'Shareholders',
        schema: clientCorpCapitalizationTableSchema ,
        // readOnly: true
    },
    
    bank_accounts: {
        type: 'list_schema',
        label: 'Bank accounts',
        schema: clientBankAccountSchema ,
        // readOnly: true
    },
    // json: {
    //     type: 'json',
    //     label: 'JSON',
    //     // visible: false,
    //     readOnly: true,
    //     getValue: (row) => row.output
    // },

}