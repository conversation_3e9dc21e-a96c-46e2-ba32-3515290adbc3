import { SchemaData } from '../core/schema/Schema';

// export interface Manager {
//   id: number;
//   name: string;
//   email: string;
//   phone: string;
//   status: string;
//   created_at: string;
//   updated_at: string;
// }

export interface Manager {
    id: number|string
    phone: string
    person: any
    user: {
      name: string
      login: string,
      password: string
    }
  };


export const managerSchema: SchemaData = {
  // title: {
  //   type: 'text',
  //   label: 'Название',

  //   editable:false
  // },
  user_name: {
    type: 'text',
    label: 'Имя пользователя',
    required: true,
    validation: {
      pattern: /^[а-яА-Яa-zA-Z\s-]+$/,
      message: 'Имя может содержать только буквы, пробелы и дефис',
    },
    getValue: (values: Record<string, any>) => values.user?.name,
    setValue: (values: Record<string, any>, value: any) => ({
      ...values,
      user: { ...values.user, name: value },
    }),
  },
  user_login: {
    type: 'text',
    label: 'Логин',
    required: true,
    validation: {
      pattern: /^[^\s@]+@[^\s@]+\.[^\s@]+$/,
      message: 'Введите корректный email',
    },
    getValue: (values: Record<string, any>) => values.user?.login,
    setValue: (values: Record<string, any>, value: any) => ({
      ...values,
      user: { ...values.user, login: value },
    }),
    editable:true
  },
  user_password: {
    type: 'text',
    label: 'Пароль',
    validation: {
      pattern: /^(?=.*[A-Za-z])(?=.*\d)[A-Za-z\d]{8,}$/,
      message: 'Пароль должен содержать минимум 8 символов, буквы и цифры',
    },
    visible: false,
    getValue: (values: Record<string, any>) => values.user?.password,
    setValue: (values: Record<string, any>, value: any) => ({
      ...values,
      user: { ...values.user, password: value },
    }),
    editable:true
  },
  role_name: {
    type: 'select',
    label: 'Роль',
    options: [],
    editable:true
  },
  phone: {
    type: 'text',
    label: 'Телефон',
    validation: {
      pattern: /^\+?[1-9]\d{1,14}$/,
      message: 'Введите корректный номер телефона',
    },
    editable:true
  },
  person: {
    type: 'autocomplete',
    label: 'Персона',
    autocomplete: {
      apiUrl: '/client_persons',
      labelKey: 'name',
      valueKey: 'id',
      formatOptionLabel: (option: any) => {return option ? option.full_title : ''},
      saveFullObject: true,
    },
    editable:true
  },
  
}; 