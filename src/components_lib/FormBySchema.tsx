import React, { useState, useEffect, useCallback } from 'react';
import {
  Box,
  Button,
  Grid,
  Alert,
  TextField,
  MenuItem,
  Autocomplete,
  ListItem,
  CircularProgress,
} from '@mui/material';
import { useNavigate } from 'react-router-dom';

import { apiClient } from '../utils/api';
import { PaginatedData } from '../utils/api';
import { FormField } from './core/schema/FormField';
import { SchemaData, SchemaDataValue } from '@/components_lib/core/schema/Schema';


interface FormBySchemaProps<T extends Record<string, any>> {
  /** Схема формы */
  schema: SchemaData;
  /** Начальные значения */
  initialValues?: T;
  /** Базовые значения для сравнения (поля, отличающиеся от базовых, будут выделены) */
  baseValues?: T;
  /** Обработчик отправки формы */
  onSubmit?: (values: T) => void;
  /** Обработчик изменения значений */
  onChange?: (values: T) => void;
  /** Обработчик отмены */
  onCancel?: () => void;
  /** Текст кнопки отправки */
  submitText?: string;
  /** Текст кнопки отмены */
  cancelText?: string;
  /** Загрузка формы */
  loading?: boolean;
  /** Ошибки валидации */
  errors?: Record<string, string>;
  /** Ошибка */
  error?: any; //Record<string, string>;
  actions?: boolean;
}

/**
 * Компонент формы, созданной на основе схемы
 */
// export const FormBySchema = <T extends Record<string, any>>({
export const FormBySchema = <T extends Record<string, any>>({
  schema,
  initialValues = {} as T,
  baseValues,
  onSubmit,
  onChange,
  onCancel,
  submitText = 'Сохранить',
  cancelText = 'Отмена',
  loading = false,
  errors = {},
  error,
  actions = true
}: FormBySchemaProps<T>) => {
  // console.log('FormBySchema initialValues:', initialValues);
  const [values, setValues] = React.useState<T>(initialValues);
  const [validationErrors, setValidationErrors] = React.useState<Record<string, string>>({});
  const [autocompleteOptions, setAutocompleteOptions] = React.useState<Record<string, any[]>>({});
  const [autocompleteLoading, setAutocompleteLoading] = React.useState<Record<string, boolean>>({});
  const debounceTimers = React.useRef<Record<string, NodeJS.Timeout>>({});
  const navigate = useNavigate();

  // Обновляем значения только при изменении initialValues
  React.useEffect(() => {
    // console.log('FormBySchema initialValues:', initialValues);
    setValues(initialValues);
  }, [JSON.stringify(initialValues)]);

  // Обновляем ошибки только при изменении errors
  React.useEffect(() => {
    setValidationErrors(errors);
  }, [JSON.stringify(errors)]);

  React.useEffect(() => {
    // Load autocomplete options on mount for fields that have loadOptionsOnMount enabled
    Object.entries(schema).forEach(([key, field]) => {
      if (field.type === 'autocomplete' && field.autocomplete?.loadOptionsOnMount) {
        loadAutocompleteOptions(key, '');
      }

      if (field.type === 'autocomplete' && field.autocomplete?.options && field.autocomplete?.options.length) {
        setAutocompleteOptions(prev => ({ ...prev, [key]: field.autocomplete?.options || [] }));
        // setAutocompleteOptions(field.autocomplete?.options)
      }
    });
  }, [schema]);

  const handleChange = (key: string, value: any) => {
    const field = schema[key];
    if (!field) return;

    console.log(`Handling change for ${key}:`, {
      value,
      currentValues: values,
      hasSetValue: !!field.setValue
    });

    let newValues = { ...values } as T;
    
    if (field.setValue) {
      newValues = field.setValue(newValues, value) as T;
    } else {
      (newValues as any)[key] = value;
    }

    console.log(`New values after change:`, newValues);
    setValues(newValues);
    onChange?.(newValues);
    validateField(key, newValues);
  };

  const validateField = (key: string, values: any) => {
    const field = schema[key];
    if (!field) return true;

    let error = '';
    console.log(`Validating field ${key}:`, {
      values,
      required: field.required,
      pattern: field.validation?.pattern,
      message: field.validation?.message
    });

    // Получаем значение с учетом getValue для валидации
    const fieldValue = field.getValue ? field.getValue( values, key ) : values[key];

    // Проверяем required только если значение пустое
    if (field.required && (fieldValue === undefined || fieldValue === null || fieldValue === '')) {
      error = 'Это поле обязательно для заполнения';
    } else if (field.validation?.pattern && fieldValue && !field.validation.pattern.test(fieldValue)) {
      error = field.validation.message || 'Неверный формат';
    } else if (field.maxLength && fieldValue && fieldValue.length > field.maxLength) {
      error = `Максимальная длина ${field.maxLength} символов`;
    } else if (field.type === 'number') {
      const num = Number(fieldValue);
      if (field.min !== undefined && num < field.min) {
        error = `Минимальное значение ${field.min}`;
      } else if (field.max !== undefined && num > field.max) {
        error = `Максимальное значение ${field.max}`;
      }
    }

    console.log(`Validation result for ${key}:`, { error });
    setValidationErrors((prev) => ({ ...prev, [key]: error }));
    return !error;
  };

  const validateForm = () => {
    const newErrors: Record<string, string> = {};
    let isValid = true;

    Object.entries(schema).forEach(([key, field]) => {
      // const value = values[key];
      if (!validateField(key, values)) {
        isValid = false;
      }
    });

    return isValid;
  };

  const handleSubmit = (e: React.FormEvent) => {
    e.preventDefault();
    console.log('Form values before validation:', values);

    if (validateForm() && onSubmit) {
      onSubmit(values);
    }
  };

  const loadAutocompleteOptions = async (key: string, search: string) => {
    const field = schema[key];
    if (!field?.autocomplete) return;

    const { apiUrl, params = {}, minChars = 2, loadOptionsOnMount , options} = field.autocomplete;
    if (!loadOptionsOnMount && search.length < minChars) {
      setAutocompleteOptions(prev => ({ ...prev, [key]: [] }));
      return;
    }

    try {
      setAutocompleteLoading(prev => ({ ...prev, [key]: true }));
      let items = options || [];
      if(apiUrl) {
        const response =  await apiClient.get<PaginatedData<any>>(apiUrl, {
        page: 1,
        limit: 25,
        q: search,
        ...params
      });
      items = response.items
      }
      
      setAutocompleteOptions(prev => ({ ...prev, [key]: items }));
    } catch (error) {
      console.error('Ошибка загрузки опций:', error);
      setAutocompleteOptions(prev => ({ ...prev, [key]: [] }));
    } finally {
      setAutocompleteLoading(prev => ({ ...prev, [key]: false }));
    }
  };

  const handleAutocompleteInputChange = (key: string, inputValue: string) => {
    const field = schema[key];
    if (!field?.autocomplete) return;

    const { debounce = 300 } = field.autocomplete;
    
    if (debounceTimers.current[key]) {
      clearTimeout(debounceTimers.current[key]);
    }

    debounceTimers.current[key] = setTimeout(() => {
      loadAutocompleteOptions(key, inputValue);
    }, debounce);
  };

  // const handleAutocompleteChange = (key: string, value: any) => {
  //   const field = schema[key];
  //   if (!field.autocomplete) return;

  //   const newValue = field.autocomplete.saveFullObject ? value : value?.[field.autocomplete.valueKey];
  //   handleChange(key, newValue);
  // };

  const renderField = (key: string, field: SchemaDataValue) => {
    // Пропускаем поля, которые нельзя редактировать
    if (field.editable === false) {
      return null;
    }
    const displayValue = field.getValue ? field.getValue(values, key) : values[key];
    
    // Определяем, отличается ли значение от базового
    let isChanged = false;
    let baseValue = null;
    if (baseValues) {
      baseValue = field.getValue ? field.getValue(baseValues, key) : baseValues[key];
      // Для дат и сложных объектов используем JSON.stringify
      if (typeof displayValue === 'object' || typeof baseValue === 'object') {
        isChanged = JSON.stringify(displayValue) !== JSON.stringify(baseValue);
      } else {
        isChanged = displayValue !== baseValue || (displayValue && !baseValue);
      }
    }
    
    return (
      <FormField
        key={key}
        fieldKey={key}
        field={field}
        value={displayValue}
        baseValue={baseValue}
        error={validationErrors[key]}
        values={values}
        onChange={handleChange}
        autocompleteOptions={autocompleteOptions[key]}
        autocompleteLoading={autocompleteLoading[key]}
        onAutocompleteInputChange={handleAutocompleteInputChange}
        isChanged={isChanged}
      />
    );
  };

  return (
    <Box component="form" onSubmit={handleSubmit} noValidate>
      <Grid container spacing={3}>
        {Object.entries(schema).map(([key, field]) => (
          <Grid item key={key} size={field.grid?.size || 12} {...(field.grid?.props || {})}>
            {renderField(key, field)}
          </Grid>
        ))}
      </Grid>
      {/* {JSON.stringify(validationErrors)} */}
      {error && <Alert severity="error">{JSON.stringify(error)}</Alert>}
      {actions && (
      <Box sx={{ mt: 3, display: 'flex', gap: 2, justifyContent: 'flex-end' }}>
        {onCancel && (
          <Button onClick={onCancel} disabled={loading}>
            {cancelText}
          </Button>
        )}
        <Button type="submit" variant="contained" disabled={loading}>
          {loading ? <CircularProgress size={24} /> : submitText}
        </Button>
      </Box>
      )}
    </Box>
  );
}; 