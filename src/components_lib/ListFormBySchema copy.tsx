import React, { useCallback, useEffect, useMemo } from 'react';
import { Button, Box, IconButton, Paper, Stack, Typography, Tooltip } from '@mui/material';
import DeleteIcon from '@mui/icons-material/Delete';
import EditIcon from '@mui/icons-material/Edit';
import AddIcon from '@mui/icons-material/Add';
import { FormBySchema } from './FormBySchema';
import { SchemaData } from './core/schema/Schema';
import { v4 as uuidv4 } from 'uuid';
import RadioButtonCheckedIcon from '@mui/icons-material/RadioButtonChecked';
import AddCircleOutlineIcon from '@mui/icons-material/AddCircleOutline';
import CheckCircleOutlineIcon from '@mui/icons-material/CheckCircleOutline';
import ErrorOutlineIcon from '@mui/icons-material/ErrorOutline';
import RestoreIcon from '@mui/icons-material/Restore';
import DeleteForeverIcon from '@mui/icons-material/DeleteForever';

export interface ListItem {
  id?: number | string;
  uid?: string | number;
  [key: string]: any;
}

export interface ListFormBySchemaProps<T extends ListItem> {
  /** Schema for each form item */
  schema: SchemaData;
  /** List of items */
  items: T[];
  /** Base list of items for comparison (items that differ will be highlighted) */
  baseItems?: T[];
  /** Callback when items change */
  onChange: (items: T[]) => void;
  /** Default values for new items */
  defaultItemValues?: Partial<T>;
  /** Button text for adding new items */
  addButtonText?: string;
  /** Title for the list section */
  title?: string;
  /** Allow item deletion */
  allowDelete?: boolean;
  /** Show deleted items */
  showDeleted?: boolean;
  /** Additional component to render for each item */
  renderItemExtra?: (item: T, index: number) => React.ReactNode;
}

/**
 * A component that renders a list of forms based on a schema
 */
export const ListFormBySchema = <T extends ListItem>({
  schema,
  items,
  baseItems,
  onChange,
  defaultItemValues = {} as Partial<T>,
  addButtonText = 'Add Item',
  title,
  allowDelete = true,
  showDeleted = true,
  renderItemExtra
}: ListFormBySchemaProps<T>) => {
  
  // Make sure we have an array even if items is undefined
  useEffect(() => {
    if (!Array.isArray(items)) {
      onChange([] as T[]);
    }
  }, []);

  // Find a matching base item for the current item
  const findMatchingBaseItem = useCallback( (item: T, baseItems?: T[]) => {
    if (!baseItems || baseItems.length === 0) return undefined;
    return baseItems.find(baseItem => baseItem.id === item.id || baseItem.uid === item.uid)
    // // try to match by id / uid
    // if (item.id || item.uid) {
    //   const matchById = baseItems.find(baseItem => baseItem.id === item.id || baseItem.uid === item.uid);
    //   if (matchById) return matchById;
    // }
    // return undefined;
  }, [baseItems, items])

  // Find items in baseItems that are not in items (deleted items)
  const deletedItems = useMemo(() => {
    if (!baseItems || baseItems.length === 0) return [];
    if (!items || items.length === 0) return [...baseItems];
    
    return baseItems.filter(baseItem => {
      // Check if this base item exists in the current items
      const existsInItems = items.some(item => 
        (item.id && baseItem.id && item.id === baseItem.id) || 
        (item.uid && baseItem.uid && item.uid === baseItem.uid)
      );
      
      return !existsInItems;
    });
  }, [baseItems, items]);

  // Check if item has differences from base item
  const hasChanges = (item: T, baseItem?: T , itemSchema?: SchemaData ) => {
    if (!baseItem) return false; // No base item to compare with
    
    if (itemSchema) {
      for(let key of Object.keys(itemSchema)) {
        if(itemSchema[key].type != 'autocomplete' && item[key] !== baseItem[key]) {
          console.log(key, item[key], baseItem[key])
          return true
        }
      }
    }
    

    // // Compare fields, exclude technical fields like 'uid'
    // const itemKeys = Object.keys(item).filter(key => key !== 'uid');
  
    

    // for (const key of itemKeys) {
    //   const itemValue = item[key];
    //   const baseValue = baseItem[key];
      
    //   // For object values, compare stringified versions
    //   if (typeof itemValue === 'object' || typeof baseValue === 'object') {
    //     if (JSON.stringify(itemValue) !== JSON.stringify(baseValue)) {
    //       return true;
    //     }
    //   } else if (itemValue !== baseValue) {
    //     return true;
    //   }
    // }
    
    return false;
  }
  // Handle change of a specific item
  const handleItemChange = useCallback((updatedItem: T, index: number) => {
    const newItems = [...items];
    newItems[index] = updatedItem;
    onChange(newItems);
  }, [items, onChange]);

  // Add a new item to the list
  const addItem = useCallback(() => {
    const newItem = {
      ...defaultItemValues,
      uid: uuidv4() // Generate a unique id for the new item
    } as T;
    onChange([...items, newItem]);
  }, [items, defaultItemValues, onChange]);

  // Restore a deleted item
  const restoreItem = useCallback((item: T) => {
    // Add the deleted item back to the list
    onChange([...items, {...item, uid: item.uid || uuidv4()}]);
  }, [items, onChange]);

  // Remove an item from the list
  const removeItem = useCallback((index: number) => {
    const newItems = [...items];
    newItems.splice(index, 1);
    onChange(newItems);
  }, [items, onChange]);


  // const printItem = (item, index) => {

  // }


  return (
    <Box>
      {title && (
        <Typography variant="h6" gutterBottom>
          {title}
        </Typography>
      )}
      
      {/* Current items */}
      {items && items.map((item, index) => {
        // Find matching base item if we have baseItems
        const baseItem = findMatchingBaseItem(item, baseItems);
        console.log('baseItem' , baseItem)
        // Check if this item has changes
        const itemHasChanges = baseItem ? hasChanges(item, baseItem, schema) : false;
        // Check if this is a new item (doesn't exist in baseItems)
        const isNewItem = baseItems ? !baseItem : true;
        
        return (
          <Paper
            key={item.id || item.uid || index}
            elevation={1}
            sx={{ 
              p: 2, 
              mb: 2, 
              position: 'relative',
              border: isNewItem 
                ? '1px solid rgba(76, 175, 80, 0.7)' // Green border for new
                : itemHasChanges 
                  ? '1px solid rgba(255, 235, 59, 0.7)' // Yellow border for changed
                  : 'none',
              boxShadow: isNewItem 
                ? '0 0 8px rgba(76, 175, 80, 0.4)' // Green shadow for new
                : itemHasChanges 
                  ? '0 0 8px rgba(255, 235, 59, 0.4)' // Yellow shadow for changed
                  : undefined,
            }}
          >
            <Stack direction="row" spacing={2} alignItems="flex-start">
              {
                isNewItem &&
                <Tooltip title="New Item">
                  <AddCircleOutlineIcon  color="success"/>
                </Tooltip>
              }
              {itemHasChanges && 
                <Tooltip title='Has change'>
                  <ErrorOutlineIcon color="warning"  />
                </Tooltip>
              }
              {
                !isNewItem && !itemHasChanges &&
                  <CheckCircleOutlineIcon />
              }
              
              <Box flexGrow={1}>
                <FormBySchema
                  schema={schema}
                  initialValues={item}
                  baseValues={baseItem || {}}
                  onChange={(values) => handleItemChange(values as T, index)}
                  actions={false}
                />
                <div>{baseItem?.id}</div>
                <div>{item?.id}</div>
                {renderItemExtra && renderItemExtra(item, index)}
              </Box>
              
              {allowDelete && (
                <IconButton 
                  disabled={items.length <= 1}
                  color="error" 
                  onClick={() => removeItem(index)}
                  sx={{ mt: 1 }}
                >
                  <DeleteIcon />
                </IconButton>
              )}
            </Stack>
          </Paper>
        );
      })}
      
      {/* Deleted items */}
      {showDeleted && deletedItems.length > 0 && (
        <Box mt={4}>
          <Typography variant="h6" gutterBottom sx={{ color: 'text.secondary' }}>
            Удаленные элементы
          </Typography>
          
          {deletedItems.map((deletedItem, index) => (
            <Paper
              key={`deleted-${deletedItem.id || deletedItem.uid || index}`}
              elevation={1}
              sx={{ 
                p: 2, 
                mb: 2, 
                position: 'relative',
                border: '1px solid rgba(211, 47, 47, 0.3)',
                opacity: 0.8,
                backgroundColor: 'rgba(211, 47, 47, 0.05)',
              }}
            >
              <Stack direction="row" spacing={2} alignItems="flex-start">
                <Tooltip title="Deleted Item">
                  <DeleteForeverIcon color="error" />
                </Tooltip>
                
                <Box flexGrow={1}>
                  <FormBySchema
                    schema={schema}
                    initialValues={deletedItem}
                    actions={false}
                  />
                </Box>
                
                <IconButton 
                  color="primary" 
                  onClick={() => restoreItem(deletedItem)}
                  sx={{ mt: 1 }}
                  title="Restore item"
                >
                  <RestoreIcon />
                </IconButton>
              </Stack>
            </Paper>
          ))}
        </Box>
      )}
      
      <Button 
        variant="outlined" 
        startIcon={<span>+</span>}
        onClick={addItem}
        sx={{ mt: 1 }}
      >
        {addButtonText}
      </Button>
    </Box>
  );
};
