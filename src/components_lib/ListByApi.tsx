import React, { useState, useEffect } from 'react';
import {
  Box,
  Paper,
  Table,
  TableBody,
  TableCell,
  TableContainer,
  TableHead,
  TableRow,
  IconButton,
  TextField,
  Dialog,
  DialogTitle,
  DialogContent,
  DialogActions,
  InputAdornment,
  Pa<PERSON>ation,
  Stack,
  Alert,
  CircularProgress,
  Typography,
  Button,
} from '@mui/material';
import {
  Edit as EditIcon,
  Delete as DeleteIcon,
  Search as SearchIcon,
  Add as AddIcon,
} from '@mui/icons-material';
import { apiClient } from '../utils/api';

const ITEMS_PER_PAGE = 10;

interface SchemaValue {
  type: 'text' | 'number' | 'date' | 'select';
  label: string;
  options?: { value: string; label: string }[];
}

interface Schema {
  [key: string]: SchemaValue;
}

interface ListByApiProps {
  apiPath: string;
  schema: Schema;
  title?: string;
  onEdit?: (item: any) => void;
  onDelete?: (id: number) => void;
  onAdd?: () => void;
  renderActions?: (item: any) => React.ReactNode;
  searchFields?: string[];
}

interface DataItem {
  id: number;
  [key: string]: any;
}

interface ApiResponse {
  data: DataItem[];
  last_page: number;
  total: number;
  current_page: number;
  count?: number
  items?: any[]
}

const ListByApi: React.FC<ListByApiProps> = ({
  apiPath,
  schema,
  title,
  onEdit,
  onDelete,
  onAdd,
  renderActions,
  searchFields = [],
}) => {
  const [data, setData] = useState<DataItem[]>([]);
  const [open, setOpen] = useState<boolean>(false);
  const [editingId, setEditingId] = useState<number | null>(null);
  const [searchQuery, setSearchQuery] = useState<string>('');
  const [page, setPage] = useState<number>(1);
  const [totalPages, setTotalPages] = useState<number>(1);
  const [loading, setLoading] = useState<boolean>(false);
  const [error, setError] = useState<string>('');
  const [formData, setFormData] = useState<{ [key: string]: any }>({});

  const loadData = async (): Promise<void> => {
    try {
      setLoading(true);
      setError('');
      const params = new URLSearchParams({
        page: page.toString(),
        per_page: ITEMS_PER_PAGE.toString(),
        search: searchQuery,
      });
      const response = await apiClient.get<ApiResponse>(`${apiPath}?${params}`);
      setData(response.items || []);
      // setTotalPages(response.count || 1);
    } catch (err) {
      setError('Ошибка при загрузке данных');
      console.error('Ошибка:', err);
    } finally {
      setLoading(false);
    }
  };

  useEffect(() => {
    loadData();
  }, [page, searchQuery]);

  const handleOpen = (item?: DataItem): void => {
    if (item) {
      setFormData(item);
      setEditingId(item.id);
    } else {
      setFormData({});
      setEditingId(null);
    }
    setOpen(true);
  };

  const handleClose = (): void => {
    setOpen(false);
    setFormData({});
    setEditingId(null);
  };

  const handleSubmit = async (): Promise<void> => {
    try {
      setError('');
      if (editingId) {
        await apiClient.put(`${apiPath}/${editingId}`, formData);
      } else {
        await apiClient.post(`${apiPath}`, formData);
      }
      handleClose();
      loadData();
    } catch (err) {
      setError('Произошла ошибка при сохранении');
      console.error('Ошибка:', err);
    }
  };

  const handleDelete = async (id: number): Promise<void> => {
    if (window.confirm('Вы уверены, что хотите удалить эту запись?')) {
      try {
        setError('');
        await apiClient.delete(`${apiPath}/${id}`);
        loadData();
        if (onDelete) onDelete(id);
      } catch (err) {
        setError('Ошибка при удалении');
        console.error('Ошибка:', err);
      }
    }
  };

  const handlePageChange = (event: React.ChangeEvent<unknown>, value: number): void => {
    setPage(value);
  };

  const renderSearchField = (): React.ReactNode => {
    if (searchFields.length === 0) return null;

    return (
      <Box sx={{ mb: 3 }}>
        <TextField
          fullWidth
          variant="outlined"
          placeholder="Поиск..."
          value={searchQuery}
          onChange={(e) => setSearchQuery(e.target.value)}
          InputProps={{
            startAdornment: (
              <InputAdornment position="start">
                <SearchIcon />
              </InputAdornment>
            ),
          }}
        />
      </Box>
    );
  };

  const renderFormFields = (): React.ReactNode => {
    return Object.entries(schema).map(([key, field]) => {
      if (typeof field === 'object' && field.type) {
        switch (field.type) {
          case 'select':
            return (
              <TextField
                key={key}
                select
                fullWidth
                label={field.label}
                value={formData[key] || ''}
                onChange={(e) => {
                  setFormData(prev => ({ ...prev, [key]: e.target.value }));
                }}
                margin="normal"
                SelectProps={{
                  native: true,
                }}
              >
                <option value="">Выберите...</option>
                {field.options?.map((option) => (
                  <option key={option.value} value={option.value}>
                    {option.label}
                  </option>
                ))}
              </TextField>
            );
          case 'date':
            return (
              <TextField
                key={key}
                type="date"
                fullWidth
                label={field.label}
                value={formData[key] || ''}
                onChange={(e) => {
                  setFormData(prev => ({ ...prev, [key]: e.target.value }));
                }}
                margin="normal"
                InputLabelProps={{
                  shrink: true,
                }}
              />
            );
          default:
            return (
              <TextField
                key={key}
                fullWidth
                label={field.label}
                value={formData[key] || ''}
                onChange={(e) => {
                  setFormData(prev => ({ ...prev, [key]: e.target.value }));
                }}
                margin="normal"
              />
            );
        }
      }
      return null;
    });
  };

  const renderTableHeaders = (): string[] => {
    const headers: string[] = [];
    Object.entries(schema).forEach(([key, field]) => {
      if (typeof field === 'object' && field.type) {
        headers.push(field.label);
      }
    });
    if (onEdit || onDelete || renderActions) {
      headers.push('Действия');
    }
    return headers;
  };

  const renderTableRow = (item: DataItem): (string | number)[] => {
    const row: (string | number)[] = [];
    Object.entries(schema).forEach(([key, field]) => {
      if (typeof field === 'object' && field.type) {
        row.push(item[key] || '');
      }
    });
    return row;
  };

  return (
    <Box>
      {title && (
        <Typography variant="h5" gutterBottom>
          {title}
        </Typography>
      )}
      {renderSearchField()}
      {error && (
        <Alert severity="error" sx={{ mb: 2 }}>
          {error}
        </Alert>
      )}
      <Box sx={{ mb: 2, display: 'flex', justifyContent: 'flex-end' }}>
        {onAdd && (
          <Button
            variant="contained"
            startIcon={<AddIcon />}
            onClick={() => handleOpen()}
          >
            Добавить
          </Button>
        )}
      </Box>
      <TableContainer component={Paper}>
        <Table>
          <TableHead>
            <TableRow>
              {renderTableHeaders().map((header, index) => (
                <TableCell key={index}>{header}</TableCell>
              ))}
            </TableRow>
          </TableHead>
          <TableBody>
            {loading ? (
              <TableRow>
                <TableCell colSpan={renderTableHeaders().length} align="center">
                  <CircularProgress />
                </TableCell>
              </TableRow>
            ) : data.length === 0 ? (
              <TableRow>
                <TableCell colSpan={renderTableHeaders().length} align="center">
                  Нет данных
                </TableCell>
              </TableRow>
            ) : (
              data.map((item) => (
                <TableRow key={item.id}>
                  {renderTableRow(item).map((cell, index) => (
                    <TableCell key={index}>{cell}</TableCell>
                  ))}
                  {(onEdit || onDelete || renderActions) && (
                    <TableCell>
                      {renderActions ? (
                        renderActions(item)
                      ) : (
                        <Box>
                          {onEdit && (
                            <IconButton
                              size="small"
                              onClick={() => handleOpen(item)}
                            >
                              <EditIcon />
                            </IconButton>
                          )}
                          {onDelete && (
                            <IconButton
                              size="small"
                              onClick={() => handleDelete(item.id)}
                            >
                              <DeleteIcon />
                            </IconButton>
                          )}
                        </Box>
                      )}
                    </TableCell>
                  )}
                </TableRow>
              ))
            )}
          </TableBody>
        </Table>
      </TableContainer>
      <Stack spacing={2} alignItems="center" sx={{ mt: 2 }}>
        <Pagination
          count={totalPages}
          page={page}
          onChange={handlePageChange}
          color="primary"
        />
      </Stack>
      <Dialog open={open} onClose={handleClose} maxWidth="sm" fullWidth>
        <DialogTitle>
          {editingId ? 'Редактировать' : 'Добавить'}
        </DialogTitle>
        <DialogContent>
          {renderFormFields()}
        </DialogContent>
        <DialogActions>
          <Button onClick={handleClose}>Отмена</Button>
          <Button onClick={handleSubmit} variant="contained">
            Сохранить
          </Button>
        </DialogActions>
      </Dialog>
    </Box>
  );
};

export default ListByApi; 