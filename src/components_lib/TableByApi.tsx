import React, { useState, useEffect, useMemo } from 'react';
import {
  Table,
  TableBody,
  TableCell,
  TableContainer,
  TableHead,
  TableRow,
  Paper,
  TablePagination,
  Box,
  TextField,
  IconButton,
  Dialog,
  DialogTitle,
  DialogContent,
  DialogActions,
  Button,
  Fab,
  Tooltip,
  CircularProgress,
  LinearProgress,
  Stack,
} from '@mui/material';
import EditIcon from '@mui/icons-material/Edit';
import DeleteIcon from '@mui/icons-material/Delete';
import AddIcon from '@mui/icons-material/Add';
import { apiClient, PaginatedData } from '@/utils/api';
import { SchemaData, SchemaDataValue } from '@/components_lib/core/schema/Schema';
import { FormBySchema } from './FormBySchema';
import { add } from 'date-fns';

interface Column {
  field: string;
  headerName: string;
  flex: number;
  renderCell?: (params: { row: any }) => React.ReactNode;
}

export interface TableByApiProps<T> {
  /** Схема таблицы */
  schema: SchemaData;
  /** URL API для получения данных */
  apiUrl: string;
  /** Параметры запроса */
  params?: Record<string, any>;
  /** Функция для дополнительных действий в колонке действий */
  renderActions?: (row: T) => React.ReactNode;
  /** Флаг отображения кнопки редактирования */
  enableEdit?: boolean;
  /** Флаг отображения кнопки удаления */
  enableDelete?: boolean;
  /** Флаг отображения кнопки нщвый */
  enableAdd?: Boolean;
  /** Колбэк при удалении записи */
  onDelete?: (id: number) => Promise<void>;
  /** Колбэк при создании записи */
  onCreate?: (item: T) => Promise<void>;
  /** Заголовок диалога подтверждения удаления */
  deleteConfirmTitle?: string;
  /** Сообщение диалога подтверждения удаления */
  deleteConfirmMessage?: string;
  /** Максимальная высота таблицы */
  maxHeight?: number | string;
  /** Функция для обработки клика по строке */
  onRowClick?: (item: T) => void;
  onCreateClick?: () => void;
  onChangeParams?: (p: any) => void;
  addButton?: any
}



/**
 * Компонент для отображения таблицы с данными из API
 */
export const TableByApi = <T extends { id?: number|string }>({
  schema,
  apiUrl,
  params = {},
  renderActions,
  enableEdit = true,
  enableDelete = true,
  enableAdd = true,
  onDelete,
  deleteConfirmTitle = 'Подтверждение удаления',
  deleteConfirmMessage = 'Вы уверены, что хотите удалить эту запись?',
  maxHeight = 'calc(100vh - 300px)',
  onRowClick,
  onCreateClick,
  onChangeParams,
  addButton
}: TableByApiProps<T>) => {
  const [data, setData] = useState<T[]>([]);
  const [page, setPage] = useState(0);
  const [rowsPerPage, setRowsPerPage] = useState(100);
  const [total, setTotal] = useState(0);
  const [searchTerm, setSearchTerm] = useState('');
  const [loading, setLoading] = useState(false);
  const [deleteDialogOpen, setDeleteDialogOpen] = useState(false);
  const [formDialogOpen, setFormDialogOpen] = useState(false);
  const [selectedItem, setSelectedItem] = useState<T | null>(null);
  const [formValues, setFormValues] = useState<T | null>(null);
  const [formError, setFormError] = useState<Record<string, string> | null>(null);

  const fetchData = async () => {
    try {
      setLoading(true);
      const p = {
        ...params,
        page: page + 1,
        limit: rowsPerPage,
        q: searchTerm,
      }
      const response = await apiClient.get<PaginatedData<T>>(apiUrl, p);
      if(onChangeParams) {onChangeParams(p)}
      if (response && Array.isArray(response.items)) {
        setData(response.items);
        setTotal(response.count || 0);
      } else {
        setData([]);
        setTotal(0);
      }
    } catch (error) {
      console.error('Error fetching data:', error);
      setData([]);
      setTotal(0);
    } finally {
      setLoading(false);
    }
  };

  useEffect(()=> {
    const timeoutId = setTimeout(() => {
      console.log('Search term:', searchTerm);
      setPage(0);
      fetchData();
    }, 500);
    return () => clearTimeout(timeoutId);
  }, [searchTerm])

  useEffect(() => {
    fetchData();
  }, [page, rowsPerPage, params
    // , searchTerm
  ]);

  const handleChangePage = (_: unknown, newPage: number) => {
    setPage(newPage);
  };

  const handleChangeRowsPerPage = (event: React.ChangeEvent<HTMLInputElement>) => {
    setRowsPerPage(parseInt(event.target.value, 10));
    setPage(0);
  };

  // let toHandleSearch: NodeJS.Timeout | null = null;
  const handleSearch = (event: React.ChangeEvent<HTMLInputElement>) => {
    setSearchTerm(event.target.value);

    // if(toHandleSearch){ clearTimeout(toHandleSearch);   }
    // toHandleSearch = setTimeout(() => {
    //   console.log('Search term:', searchTerm);
    //   setPage(0);
    //   fetchData();
    // }, 1000);
  };

  const handleEdit = (item: T) => {
    setSelectedItem(item);
    setFormValues(item);
    setFormDialogOpen(true);
  };

  const handleCreate = () => {
    if(onCreateClick) {onCreateClick()}
    else
    {
      setSelectedItem(null);
      setFormValues(null);
      setFormDialogOpen(true);
    }

  };

  const handleFormClose = () => {
    setFormDialogOpen(false);
    setSelectedItem(null);
    setFormValues(null);
  };

  const handleFormChange = (values: T) => {
    setFormValues(values);
  };

  const handleFormSubmit = async () => {
    if (!formValues) return;

    try {
      await apiClient.save<T>(`${apiUrl}`, formValues);
      handleFormClose();
      fetchData();
    } catch (error: any) {
      setFormError(error.response.data.errors);
      console.error('Error saving item:', error);
    }
  };

  const handleDeleteClick = (item: T) => {
    setSelectedItem(item);
    setDeleteDialogOpen(true);
  };

  const handleDeleteConfirm = async () => {
    if (!selectedItem) return;

    try {
      if (onDelete) {
        await onDelete(Number(selectedItem.id));
      } else {
        await apiClient.delete(`${apiUrl}/${selectedItem.id}`);
      }
      fetchData();
    } catch (error) {
      console.error('Error deleting item:', error);
    } finally {
      setDeleteDialogOpen(false);
      setSelectedItem(null);
    }
  };

  const handleDeleteCancel = () => {
    setDeleteDialogOpen(false);
    setSelectedItem(null);
  };

  const getCellValue = (item: T, key: string, field: SchemaDataValue | undefined) => {
    if (!field) return '-';
    if (field.getValue) {
      return field.getValue(item);
    }
    if (field.type === 'autocomplete' && field.autocomplete?.saveFullObject && field.autocomplete?.formatOptionLabel) {
      return field.autocomplete.formatOptionLabel(item);
    }
    return (item as any)[key];
  };

  const formatCellValue = (value: any, field: SchemaDataValue | undefined) => {
    if (!field) return '-';
    if (value === undefined || value === null) {
      return '-';
    }

    switch (field.type) {
      case 'date':
        return new Date(value).toLocaleDateString();
      case 'textarea':
        return value;
      case 'checkbox':
        return value ? '*' : '';
      case 'select':
        if (field.options) {
          const option = field.options.find(opt =>
            typeof opt === 'object' ? opt.value === value : opt === value
          );
          return typeof option === 'object' ? option.label : option;
        }
        return value;
      case 'autocomplete':
        if (field.autocomplete?.formatOptionLabel && typeof value === 'object') {
          return field.autocomplete.formatOptionLabel(value);
        }
        return value;
      default:
        return value;
    }
  };

  const columns = useMemo(() => {
    const cols: Column[] = Object.entries(schema)
      .filter(([_, field]) => field.visible !== false)
      .map(([key, field]) => ({
        field: key,
        headerName: field.label,
        flex: 1,
      }));

    if (enableEdit || enableDelete || renderActions) {
      cols.push({
        field: 'actions',
        headerName: 'Действия',
        flex: 1,
        renderCell: (params) => (
          <Box sx={{ display: 'flex', gap: 1 }}>
            {enableEdit && (
              <Tooltip title="Редактировать">
                <IconButton
                  size="small"
                  onClick={() => handleEdit(params.row)}
                  color="primary"
                >
                  <EditIcon />
                </IconButton>
              </Tooltip>
            )}
            {enableDelete && (
              <Tooltip title="Удалить">
                <IconButton
                  size="small"
                  onClick={() => handleDeleteClick(params.row)}
                  color="error"
                >
                  <DeleteIcon />
                </IconButton>
              </Tooltip>
            )}
            {renderActions?.(params.row)}
          </Box>
        ),
      });
    }

    return cols;
  }, [schema, renderActions, enableEdit, enableDelete]);

  return (
    <Box>
      <Stack
        direction="row"
        spacing={2}
        >
        <Box sx={{ mb: 2 }}>
          <TextField
            fullWidth
            variant="outlined"
            placeholder="Search..."
            value={searchTerm}
            onChange={handleSearch}
          />
        </Box>
        {addButton && addButton}

        {!addButton && enableAdd && (
            <Fab
              color="primary"
              // sx={{ position: 'fixed', bottom: 16, right: 16 }}
              onClick={handleCreate}
            >
              <AddIcon />
            </Fab>
          )}
      </Stack>
      <TableContainer
        component={Paper}
        sx={{
          maxHeight,
          overflow: 'auto',
          '& .MuiTable-root': {
            minWidth: 650,
          },
          position: 'relative'
        }}
      >
        {loading && (
          <Box
            sx={{
              position: 'absolute',
              top: 0,
              left: 0,
              right: 0,
              zIndex: 1
            }}
          >
            <LinearProgress />
          </Box>
        )}
        <Table stickyHeader>
          <TableHead>
            <TableRow>
              {columns.map((column) => (
                <TableCell
                  key={column.field}
                  sx={{
                    backgroundColor: 'background.paper',
                    fontWeight: 'bold',
                    whiteSpace: 'nowrap'
                  }}
                >
                  {column.headerName}
                </TableCell>
              ))}
            </TableRow>
          </TableHead>
          <TableBody>
            {loading ? (
              <TableRow>
                <TableCell
                  colSpan={columns.length}
                  align="center"
                  sx={{ py: 8 }}
                >
                  <CircularProgress />
                </TableCell>
              </TableRow>
            ) : data.length === 0 ? (
              <TableRow>
                <TableCell
                  colSpan={columns.length}
                  align="center"
                  sx={{ py: 4 }}
                >
                  Нет данных
                </TableCell>
              </TableRow>
            ) : (
              data.map((item) => (
                <TableRow
                  key={item.id}
                  onClick={() => onRowClick?.(item)}
                  sx={{
                    cursor: onRowClick ? 'pointer' : 'default',
                    '&:hover': onRowClick ? {
                      backgroundColor: 'action.hover',
                    } : {}
                  }}
                >
                  {columns.map((column) => (
                    <TableCell
                      key={column.field}
                      sx={{ whiteSpace: 'nowrap' }}
                    >
                      {column.renderCell
                        ? column.renderCell({ row: item })
                        : formatCellValue(getCellValue(item, column.field, schema[column.field]), schema[column.field])}
                    </TableCell>
                  ))}
                </TableRow>
              ))
            )}
          </TableBody>
        </Table>
      </TableContainer>

      <TablePagination
        component="div"
        count={total}
        page={page}
        onPageChange={handleChangePage}
        rowsPerPage={rowsPerPage}
        onRowsPerPageChange={handleChangeRowsPerPage}
        labelRowsPerPage="Строк на странице:"
      />

      <Dialog
        open={formDialogOpen}
        onClose={handleFormClose}
        maxWidth="md"
        fullWidth
      >
        <DialogTitle>{selectedItem ? 'Редактирование' : 'Создание'}</DialogTitle>
        <DialogContent>
          <Box sx={{ mt: 2 }}>
            <FormBySchema
              schema={schema}
              initialValues={formValues || {} as T}
              onSubmit={handleFormSubmit}
              onChange={handleFormChange}
              cancelText='Отмена'
              submitText={selectedItem ? 'Сохранить' : 'Создать'}
              loading={loading}
              onCancel={handleFormClose}
              error={formError}

            />
          </Box>
        </DialogContent>
      </Dialog>

      {/* {enableAdd && (
        <Fab
          color="primary"
          sx={{ position: 'fixed', bottom: 16, right: 16 }}
          onClick={handleCreate}
        >
          <AddIcon />
        </Fab>
      )} */}

      <Dialog
        open={deleteDialogOpen}
        onClose={handleDeleteCancel}
      >
        <DialogTitle>{deleteConfirmTitle}</DialogTitle>
        <DialogContent>
          {deleteConfirmMessage}
        </DialogContent>
        <DialogActions>
          <Button onClick={handleDeleteCancel}>Отмена</Button>
          <Button onClick={handleDeleteConfirm} color="error" variant="contained">
            Удалить
          </Button>
        </DialogActions>
      </Dialog>
    </Box>
  );
};