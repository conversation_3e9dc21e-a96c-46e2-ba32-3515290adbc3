import React, { useState, useRef, useCallback } from 'react';
import {
  Box,
  Button,
  LinearProgress,
  Typography,
  Alert,
  Paper,
  IconButton,
  Chip,
} from '@mui/material';
import {
  CloudUpload as UploadIcon,
  Delete as DeleteIcon,
  InsertDriveFile as FileIcon,
} from '@mui/icons-material';

export interface FileUploadProps {
  /** URL для загрузки файла */
  url: string;
  /** Дополнительные параметры для запроса */
  additionalParams?: Record<string, any>;
  /** Callback по окончанию загрузки */
  onComplete?: (response: any, file: File) => void;
  /** Callback при ошибке */
  onError?: (error: any, file: File) => void;
  /** Callback при изменении прогресса */
  onProgress?: (progress: number, file: File) => void;
  /** Разрешенные типы файлов */
  accept?: string;
  /** Максимальный размер файла в байтах */
  maxSize?: number;
  /** Множественная загрузка */
  multiple?: boolean;
  /** Отключить компонент */
  disabled?: boolean;
  /** Текст кнопки */
  buttonText?: string;
  /** Показать прогресс */
  showProgress?: boolean;
  /** Показать информацию о файле */
  showFileInfo?: boolean;
}

interface UploadingFile {
  file: File;
  progress: number;
  status: 'uploading' | 'completed' | 'error';
  error?: string;
  response?: any;
}

export const FileUpload: React.FC<FileUploadProps> = ({
  url,
  additionalParams = {},
  onComplete,
  onError,
  onProgress,
  accept,
  maxSize,
  multiple = false,
  disabled = false,
  buttonText = 'Upload File',
  showProgress = true,
  showFileInfo = true,
}) => {
  const [uploadingFiles, setUploadingFiles] = useState<UploadingFile[]>([]);
  const [dragOver, setDragOver] = useState(false);
  const fileInputRef = useRef<HTMLInputElement>(null);

  const formatFileSize = (bytes: number): string => {
    if (bytes === 0) return '0 Bytes';
    const k = 1024;
    const sizes = ['Bytes', 'KB', 'MB', 'GB'];
    const i = Math.floor(Math.log(bytes) / Math.log(k));
    return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i];
  };

  const validateFile = (file: File): string | null => {
    if (maxSize && file.size > maxSize) {
      return `File size exceeds ${formatFileSize(maxSize)}`;
    }
    return null;
  };

  const uploadFile = useCallback(async (file: File) => {
    const validationError = validateFile(file);
    if (validationError) {
      setUploadingFiles(prev => prev.map(f => 
        f.file === file 
          ? { ...f, status: 'error', error: validationError }
          : f
      ));
      onError?.(new Error(validationError), file);
      return;
    }

    const formData = new FormData();
    formData.append('file', file);
    
    // Добавляем дополнительные параметры
    Object.entries(additionalParams).forEach(([key, value]) => {
      formData.append(key, String(value));
    });

    try {
      const xhr = new XMLHttpRequest();

      // Обработчик прогресса
      xhr.upload.addEventListener('progress', (event) => {
        if (event.lengthComputable) {
          const progress = Math.round((event.loaded / event.total) * 100);
          setUploadingFiles(prev => prev.map(f => 
            f.file === file ? { ...f, progress } : f
          ));
          onProgress?.(progress, file);
        }
      });

      // Обработчик завершения
      xhr.addEventListener('load', () => {
        if (xhr.status >= 200 && xhr.status < 300) {
          let response;
          try {
            response = JSON.parse(xhr.responseText);
          } catch {
            response = xhr.responseText;
          }

          setUploadingFiles(prev => prev.map(f => 
            f.file === file 
              ? { ...f, status: 'completed', progress: 100, response }
              : f
          ));
          onComplete?.(response, file);
        } else {
          const error = `Upload failed: ${xhr.status} ${xhr.statusText}`;
          setUploadingFiles(prev => prev.map(f => 
            f.file === file 
              ? { ...f, status: 'error', error }
              : f
          ));
          onError?.(new Error(error), file);
        }
      });

      // Обработчик ошибки
      xhr.addEventListener('error', () => {
        const error = 'Network error occurred';
        setUploadingFiles(prev => prev.map(f => 
          f.file === file 
            ? { ...f, status: 'error', error }
            : f
        ));
        onError?.(new Error(error), file);
      });

      xhr.open('POST', url);
      xhr.send(formData);

    } catch (error) {
      const errorMessage = error instanceof Error ? error.message : 'Unknown error';
      setUploadingFiles(prev => prev.map(f => 
        f.file === file 
          ? { ...f, status: 'error', error: errorMessage }
          : f
      ));
      onError?.(error, file);
    }
  }, [url, additionalParams, onComplete, onError, onProgress, maxSize]);

  const handleFileSelect = (files: FileList | null) => {
    if (!files) return;

    const fileArray = Array.from(files);
    const newUploadingFiles = fileArray.map(file => ({
      file,
      progress: 0,
      status: 'uploading' as const,
    }));

    setUploadingFiles(prev => [...prev, ...newUploadingFiles]);

    // Начинаем загрузку каждого файла
    fileArray.forEach(uploadFile);
  };

  const handleButtonClick = () => {
    fileInputRef.current?.click();
  };

  const handleDragOver = (e: React.DragEvent) => {
    e.preventDefault();
    setDragOver(true);
  };

  const handleDragLeave = (e: React.DragEvent) => {
    e.preventDefault();
    setDragOver(false);
  };

  const handleDrop = (e: React.DragEvent) => {
    e.preventDefault();
    setDragOver(false);
    handleFileSelect(e.dataTransfer.files);
  };

  const removeFile = (fileToRemove: File) => {
    setUploadingFiles(prev => prev.filter(f => f.file !== fileToRemove));
  };

  return (
    <Box>
      {/* Область загрузки */}
      <Paper
        sx={{
          p: 3,
          border: '2px dashed',
          borderColor: dragOver ? 'primary.main' : 'grey.300',
          backgroundColor: dragOver ? 'action.hover' : 'background.paper',
          textAlign: 'center',
          cursor: disabled ? 'not-allowed' : 'pointer',
          transition: 'all 0.2s ease',
        }}
        onDragOver={handleDragOver}
        onDragLeave={handleDragLeave}
        onDrop={handleDrop}
        onClick={!disabled ? handleButtonClick : undefined}
      >
        <input
          ref={fileInputRef}
          type="file"
          accept={accept}
          multiple={multiple}
          disabled={disabled}
          style={{ display: 'none' }}
          onChange={(e) => handleFileSelect(e.target.files)}
        />
        
        <UploadIcon sx={{ fontSize: 48, color: 'grey.400', mb: 2 }} />
        <Typography variant="h6" gutterBottom>
          {buttonText}
        </Typography>
        <Typography variant="body2" color="text.secondary">
          Drag and drop files here or click to select
        </Typography>
        {maxSize && (
          <Typography variant="caption" color="text.secondary">
            Max file size: {formatFileSize(maxSize)}
          </Typography>
        )}
      </Paper>

      {/* Список загружаемых файлов */}
      {showFileInfo && uploadingFiles.length > 0 && (
        <Box sx={{ mt: 2 }}>
          {uploadingFiles.map((uploadingFile, index) => (
            <Paper key={index} sx={{ p: 2, mb: 1 }}>
              <Box sx={{ display: 'flex', alignItems: 'center', gap: 2 }}>
                <FileIcon color="action" />
                <Box sx={{ flexGrow: 1 }}>
                  <Typography variant="body2" noWrap>
                    {uploadingFile.file.name}
                  </Typography>
                  <Typography variant="caption" color="text.secondary">
                    {formatFileSize(uploadingFile.file.size)}
                  </Typography>
                </Box>
                
                <Chip
                  label={uploadingFile.status}
                  color={
                    uploadingFile.status === 'completed' ? 'success' :
                    uploadingFile.status === 'error' ? 'error' : 'default'
                  }
                  size="small"
                />
                
                <IconButton
                  size="small"
                  onClick={() => removeFile(uploadingFile.file)}
                  disabled={uploadingFile.status === 'uploading'}
                >
                  <DeleteIcon />
                </IconButton>
              </Box>
              
              {showProgress && uploadingFile.status === 'uploading' && (
                <LinearProgress
                  variant="determinate"
                  value={uploadingFile.progress}
                  sx={{ mt: 1 }}
                />
              )}
              
              {uploadingFile.status === 'error' && uploadingFile.error && (
                <Alert severity="error" sx={{ mt: 1 }}>
                  {uploadingFile.error}
                </Alert>
              )}
            </Paper>
          ))}
        </Box>
      )}
    </Box>
  );
};
