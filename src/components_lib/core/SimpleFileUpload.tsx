import React, { useRef } from 'react';
import {
  Box,
  Button,
  LinearProgress,
  Typography,
  Alert,
  Paper,
  CircularProgress,
} from '@mui/material';
import {
  CloudUpload as UploadIcon,
  CheckCircle as SuccessIcon,
  Error as ErrorIcon,
} from '@mui/icons-material';
import { useFileUpload, UseFileUploadOptions } from './hooks/useFileUpload';

export interface SimpleFileUploadProps extends UseFileUploadOptions {
  /** Разрешенные типы файлов */
  accept?: string;
  /** Отключить компонент */
  disabled?: boolean;
  /** Текст кнопки */
  buttonText?: string;
  /** Показать прогресс */
  showProgress?: boolean;
  /** Вариант отображения */
  variant?: 'button' | 'dropzone';
  /** Размер кнопки */
  size?: 'small' | 'medium' | 'large';
}

export const SimpleFileUpload: React.FC<SimpleFileUploadProps> = ({
  url,
  additionalParams,
  onComplete,
  onError,
  onProgress,
  maxSize,
  accept,
  disabled = false,
  buttonText = 'Upload File',
  showProgress = true,
  variant = 'button',
  size = 'medium',
}) => {
  const fileInputRef = useRef<HTMLInputElement>(null);
  
  const { uploadFile, isUploading, progress, error, response, reset } = useFileUpload({
    url,
    additionalParams,
    onComplete,
    onError,
    onProgress,
    maxSize,
  });

  const handleFileSelect = async (files: FileList | null) => {
    if (!files || files.length === 0) return;
    
    const file = files[0];
    try {
      await uploadFile(file);
    } catch (err) {
      // Ошибка уже обработана в хуке
    }
  };

  const handleButtonClick = () => {
    if (isUploading) return;
    reset();
    fileInputRef.current?.click();
  };

  const formatFileSize = (bytes: number): string => {
    if (bytes === 0) return '0 Bytes';
    const k = 1024;
    const sizes = ['Bytes', 'KB', 'MB', 'GB'];
    const i = Math.floor(Math.log(bytes) / Math.log(k));
    return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i];
  };

  if (variant === 'dropzone') {
    return (
      <Box>
        <Paper
          sx={{
            p: 3,
            border: '2px dashed',
            borderColor: 'grey.300',
            textAlign: 'center',
            cursor: disabled || isUploading ? 'not-allowed' : 'pointer',
            transition: 'all 0.2s ease',
            '&:hover': {
              borderColor: disabled || isUploading ? 'grey.300' : 'primary.main',
              backgroundColor: disabled || isUploading ? 'inherit' : 'action.hover',
            },
          }}
          onClick={!disabled && !isUploading ? handleButtonClick : undefined}
        >
          <input
            ref={fileInputRef}
            type="file"
            accept={accept}
            disabled={disabled || isUploading}
            style={{ display: 'none' }}
            onChange={(e) => handleFileSelect(e.target.files)}
          />
          
          {isUploading ? (
            <CircularProgress size={48} sx={{ mb: 2 }} />
          ) : response ? (
            <SuccessIcon sx={{ fontSize: 48, color: 'success.main', mb: 2 }} />
          ) : error ? (
            <ErrorIcon sx={{ fontSize: 48, color: 'error.main', mb: 2 }} />
          ) : (
            <UploadIcon sx={{ fontSize: 48, color: 'grey.400', mb: 2 }} />
          )}
          
          <Typography variant="h6" gutterBottom>
            {isUploading ? 'Uploading...' : 
             response ? 'Upload Complete' :
             error ? 'Upload Failed' : buttonText}
          </Typography>
          
          {!isUploading && !response && !error && (
            <Typography variant="body2" color="text.secondary">
              Click to select file
              {maxSize && (
                <><br />Max size: {formatFileSize(maxSize)}</>
              )}
            </Typography>
          )}
          
          {showProgress && isUploading && (
            <Box sx={{ mt: 2, width: '100%' }}>
              <LinearProgress variant="determinate" value={progress} />
              <Typography variant="caption" sx={{ mt: 1, display: 'block' }}>
                {progress}%
              </Typography>
            </Box>
          )}
        </Paper>
        
        {error && (
          <Alert severity="error" sx={{ mt: 2 }}>
            {error}
          </Alert>
        )}
        
        {response && (
          <Alert severity="success" sx={{ mt: 2 }}>
            File uploaded successfully!
          </Alert>
        )}
      </Box>
    );
  }

  return (
    <Box>
      <input
        ref={fileInputRef}
        type="file"
        accept={accept}
        disabled={disabled || isUploading}
        style={{ display: 'none' }}
        onChange={(e) => handleFileSelect(e.target.files)}
      />
      
      <Button
        variant="contained"
        size={size}
        disabled={disabled || isUploading}
        onClick={handleButtonClick}
        startIcon={
          isUploading ? (
            <CircularProgress size={20} color="inherit" />
          ) : response ? (
            <SuccessIcon />
          ) : error ? (
            <ErrorIcon />
          ) : (
            <UploadIcon />
          )
        }
        sx={{ minWidth: 140 }}
      >
        {isUploading ? 'Uploading...' : 
         response ? 'Success' :
         error ? 'Try Again' : buttonText}
      </Button>
      
      {showProgress && isUploading && (
        <Box sx={{ mt: 1, width: '100%' }}>
          <LinearProgress variant="determinate" value={progress} />
          <Typography variant="caption" color="text.secondary">
            {progress}%
          </Typography>
        </Box>
      )}
      
      {error && (
        <Alert severity="error" sx={{ mt: 1 }}>
          {error}
        </Alert>
      )}
      
      {response && (
        <Alert severity="success" sx={{ mt: 1 }}>
          File uploaded successfully!
        </Alert>
      )}
    </Box>
  );
};
