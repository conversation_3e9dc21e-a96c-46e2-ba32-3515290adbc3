import React, { memo, useMemo, useCallback } from 'react';
import { Grid } from '@mui/material';
import { SchemaFieldInputContainer } from './SchemaFieldInputContainer';
import { SchemaDataValue } from './Schema';

interface MemoizedSchemaFieldProps {
  fieldKey: string;
  field: SchemaDataValue;
  value: any;
  baseValue?: any;
  allValues: Record<string, any>;
  baseValues?: Record<string, any>;
  onChange: (key: string, value: any) => void;
  readonly?: boolean;
}

/**
 * Мемоизированный компонент поля схемы
 * Перерисовывается только при изменении своего значения или зависимостей
 */
export const MemoizedSchemaField = memo<MemoizedSchemaFieldProps>(({
  fieldKey,
  field,
  value,
  baseValue,
  allValues,
  baseValues,
  onChange,
  readonly = false
}) => {
  // Мемоизируем проверку изменений
  const isChanged = useMemo(() => {
    if (!baseValues) return false;
    
    // Для дат и сложных объектов используем JSON.stringify
    if (typeof value === 'object' || typeof baseValue === 'object') {
      return JSON.stringify(value) !== JSON.stringify(baseValue);
    }
    return value !== baseValue || (value && !baseValue);
  }, [value, baseValue]);

  // Мемоизируем обработчик изменений
  const handleChange = useCallback((key: any, newValue: any) => {
    onChange(fieldKey, newValue);
  }, [fieldKey, onChange]);

  // Пропускаем поля, которые нельзя редактировать
  if (field.editable === false) {
    return null;
  }

  return (
    <Grid size={field.grid?.size || 12} {...(field.grid?.props || {})}>
      <SchemaFieldInputContainer
        fieldKey={fieldKey}
        field={field}
        value={value}
        baseValue={baseValue}
        isChanged={isChanged}
        values={allValues}
        baseValues={baseValues}
        onChange={handleChange}
        readonly={readonly}
      />
    </Grid>
  );
}, (prevProps, nextProps) => {
  // Кастомная функция сравнения для memo
  // Перерисовываем только если изменились критичные пропы
  return (
    prevProps.fieldKey === nextProps.fieldKey &&
    prevProps.readonly === nextProps.readonly &&
    prevProps.value === nextProps.value &&
    prevProps.baseValue === nextProps.baseValue &&
    // Для сложных объектов используем глубокое сравнение
    JSON.stringify(prevProps.field) === JSON.stringify(nextProps.field)
  );
});

MemoizedSchemaField.displayName = 'MemoizedSchemaField';
