
import { SchemaGridFormProps } from './SchemaFieldProps';


/**
 * Компонент отображения, созданной на основе схемы  использует SchemaFieldViewContainer
 */
// export const FormBySchema = <T extends Record<string, any>>({
export const SchemaGridView = <T extends Record<string, any>>({
  schema,
  initialValues = {} as T,
  baseValues,
  onSubmit,
  onChange,
  onCancel,
  submitText = 'Save',
  cancelText = 'Cancel',
  loading = false,
  errors = {},
  error,
  actions = true
}: SchemaGridFormProps<T>) => {
  
}; 