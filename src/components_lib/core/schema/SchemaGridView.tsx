
import React, { useState, useEffect } from 'react';
import {
  Box,
  Button,
  Grid,
  Alert,
  CircularProgress,
} from '@mui/material';
import { SchemaGridFormProps } from './SchemaFieldProps';
import { SchemaFieldViewContainer } from './SchemaFieldViewContainer';

/**
 * Компонент для просмотра данных, созданный на основе схемы
 * Использует SchemaFieldViewContainer для каждого поля (режим только чтения)
 */
export const SchemaGridView = <T extends Record<string, any>>({
  schema,
  initialValues = {} as T,
  baseValues,
  onSubmit,
  onChange,
  onCancel,
  submitText = 'Сохранить',
  cancelText = 'Отмена',
  loading = false,
  errors = {},
  error,
  actions = false // По умолчанию для просмотра действия отключены
}: SchemaGridFormProps<T>) => {
  const [values, setValues] = useState<T>(initialValues);

  // Обновляем значения только при изменении initialValues
  useEffect(() => {
    setValues(initialValues);
  }, [JSON.stringify(initialValues)]);

  const handleChange = (key: string, value: any) => {
    const field = schema[key];
    if (!field) return;

    let newValue = value;

    // Применяем setValue если есть
    if (field.setValue) {
      const newValues = field.setValue(values, value);
      setValues(newValues as T);
      onChange?.(newValues as T);
      return;
    }

    const newValues = { ...values, [key]: newValue };
    setValues(newValues);
    onChange?.(newValues);
  };

  const handleSubmit = (e: React.FormEvent) => {
    e.preventDefault();

    if (onSubmit) {
      onSubmit(values);
    }
  };

  const renderField = (key: string, field: any) => {
    // Пропускаем поля, которые не должны отображаться
    if (field.visible === false) {
      return null;
    }

    return (
      <SchemaFieldViewContainer
        key={key}
        fieldKey={key}
        field={field}
        values={values}
        baseValues={baseValues}
        onChange={handleChange}
      />
    );
  };

  return (
    <Box component="form" onSubmit={handleSubmit} noValidate>
      <Grid container spacing={3}>
        {Object.entries(schema).map(([key, field]) => (
          <Grid item key={key} size={field.grid?.size || 12} {...(field.grid?.props || {})}>
            {renderField(key, field)}
          </Grid>
        ))}
      </Grid>

      {error && <Alert severity="error" sx={{ mt: 2 }}>{JSON.stringify(error)}</Alert>}

      {actions && (
        <Box sx={{ mt: 3, display: 'flex', gap: 2, justifyContent: 'flex-end' }}>
          {onCancel && (
            <Button onClick={onCancel} disabled={loading}>
              {cancelText}
            </Button>
          )}
          {onSubmit && (
            <Button type="submit" variant="contained" disabled={loading}>
              {loading ? <CircularProgress size={24} /> : submitText}
            </Button>
          )}
        </Box>
      )}
    </Box>
  );
};