import React, { useCallback, useEffect, useMemo } from 'react';
import { Button, Box, IconButton, Paper, Stack, Typography, Tooltip, Fab } from '@mui/material';
import DeleteIcon from '@mui/icons-material/Delete';
import EditIcon from '@mui/icons-material/Edit';
import AddIcon from '@mui/icons-material/Add';
import { SchemaData } from './Schema';
import { v4 as uuidv4 } from 'uuid';
import RadioButtonCheckedIcon from '@mui/icons-material/RadioButtonChecked';
import AddCircleOutlineIcon from '@mui/icons-material/AddCircleOutline';
import CheckCircleOutlineIcon from '@mui/icons-material/CheckCircleOutline';
import ErrorOutlineIcon from '@mui/icons-material/ErrorOutline';
import RestoreIcon from '@mui/icons-material/Restore';
import DeleteForeverIcon from '@mui/icons-material/DeleteForever';
import { SchemaFieldView } from './SchemaFieldView';
import { SchemaGridEdit } from './SchemaGridEdit';

export interface ListItem {
  id?: number | string;
  uid?: string | number;
  [key: string]: any;
}

export interface ListFormSchemaProps<T extends ListItem> {
  /** Schema for each form item */
  schema: SchemaData;
  /** List of items */
  items: T[];
  /** Base list of items for comparison (items that differ will be highlighted) */
  baseItems?: T[];
  /** Callback when items change */
  onChange: (items: T[]) => void;
  /** Default values for new items */
  defaultItemValues?: Partial<T>;
  /** Button text for adding new items */
  addButtonText?: string | null;
  /** Title for the list section */
  title?: string;
  /** Allow item deletion */
  allowDelete?: boolean;
  allowAdd?: boolean;
  /** Show deleted items */
  showDeleted?: boolean;
  /** Additional component to render for each item */
  renderItemExtra?: (item: T, index: number) => React.ReactNode;
  readonly?: boolean; 
}

/**
 * A component that renders a list of forms based on a schema
 */
export const ListFormSchema = <T extends ListItem>({
  schema,
  items,
  baseItems,
  onChange,
  defaultItemValues = {} as Partial<T>,
  addButtonText ,
  title,
  allowDelete = true,
  allowAdd = true,
  showDeleted = true,
  renderItemExtra,
  readonly
}: ListFormSchemaProps<T>) => {

  if(addButtonText === undefined) addButtonText = 'Add Item';
  
  // Make sure we have an array even if items is undefined
  useEffect(() => {
    if (!Array.isArray(items)) {
      onChange([] as T[]);
    }
  }, []);

  // Find a matching base item for the current item
  const findMatchingBaseItem = useCallback( (item: T, baseItems?: T[]) => {
    if (!baseItems || baseItems.length === 0) return undefined;
    return baseItems.find( el => el.id === item.id || (item.uid && el.uid === item.uid))
  }, [baseItems, items])

  // Find items in baseItems that are not in items (deleted items)
  const deletedItems = useMemo(() => {
    if (!baseItems || baseItems.length === 0) return [];
    if (!items || items.length === 0) return [...baseItems];
    
    return baseItems.filter(baseItem => {
      // Check if this base item exists in the current items
      const existsInItems = items.some(item => 
        (item.id && baseItem.id && item.id === baseItem.id) || 
        (item.uid && baseItem.uid && item.uid === baseItem.uid)
      );
      
      return !existsInItems;
    });
  }, [baseItems, items]);

  // Check if item has differences from base item
  const hasChanges = (item: T, baseItem?: T , itemSchema?: SchemaData ) => {
    if (!baseItem) return false; // No base item to compare with
    
    if (itemSchema) {
      for(let key of Object.keys(itemSchema)) {
        if(itemSchema[key].type != 'autocomplete' && item[key] !== baseItem[key]) {
          // console.log(key, item[key], baseItem[key])
          return true
        }
      }
    }
    return false;
  }
  // Handle change of a specific item
  const handleItemChange = useCallback((updatedItem: T, index: number) => {
    const newItems = [...items];
    newItems[index] = updatedItem;
    onChange(newItems);
  }, [items, onChange]);

  // Add a new item to the list
  const addItem = useCallback(() => {
    const newItem = {
      ...defaultItemValues,
      uid: uuidv4() // Generate a unique id for the new item
    } as T;
    onChange([...items, newItem]);
  }, [items, defaultItemValues, onChange]);

  // Restore a deleted item
  const restoreItem = useCallback((item: T) => {
    // Add the deleted item back to the list
    onChange([...items, {...item, uid: item.uid || uuidv4()}]);
  }, [items, onChange]);

  // Remove an item from the list
  const removeItem = useCallback((index: number) => {
    const newItems = [...items];
    newItems.splice(index, 1);
    onChange(newItems);
  }, [items, onChange]);


  const printItem = (item: T, index: number) => {
    const baseItem = findMatchingBaseItem(item, baseItems);
    let itemHasChanges = false;
    let isNewItem = false;
    if(!readonly) {
      // Check if this item has changes
      itemHasChanges = baseItem ? hasChanges(item, baseItem, schema) : false;
      // Check if this is a new item (doesn't exist in baseItems)
      isNewItem = baseItems ? !baseItem : true;
    }
    return (
      <Paper
        key={item.id || item.uid || index}
        elevation={1}
        sx={{ 
          p: 2, 
          mb: 2, 
          position: 'relative',
          border: isNewItem 
            ? '1px solid rgba(76, 175, 80, 0.7)' // Green border for new
            : itemHasChanges 
              ? '1px solid rgba(255, 235, 59, 0.7)' // Yellow border for changed
              : 'none',
          boxShadow: isNewItem 
            ? '0 0 8px rgba(76, 175, 80, 0.4)' // Green shadow for new
            : itemHasChanges 
              ? '0 0 8px rgba(255, 235, 59, 0.4)' // Yellow shadow for changed
              : undefined,
        }}
      >
        <Stack direction="row" spacing={2} alignItems="flex-start">
          {
            isNewItem &&
            <Tooltip title="New Item">
              <AddCircleOutlineIcon  color="success"/>
            </Tooltip>
          }
          {itemHasChanges && 
            <Tooltip title='Has change'>
              <ErrorOutlineIcon color="warning"  />
            </Tooltip>
          }
          {
            !isNewItem && !itemHasChanges &&
              <CheckCircleOutlineIcon />
          }
          
          <Box flexGrow={1}>
            <SchemaGridEdit
              schema={schema}
              initialValues={item}
              baseValues={baseItem }
              onChange={(values) => handleItemChange(values as T, index)}
              actions={false}
              readonly={readonly}
              container='div'
            />
            {renderItemExtra && renderItemExtra(item, index)}
          </Box>
          
          {!readonly && allowDelete && (
            <IconButton 
              // disabled={items.length <= 1}
              color="error" 
              onClick={() => removeItem(index)}
              sx={{ mt: 1 }}
            >
              <DeleteIcon />
            </IconButton>
          )}
        </Stack>
      </Paper>
    );
  }


  return (
    <Box>
      <Stack
        direction="row"
        spacing={2}
        sx={{mb:2}}
        >
        {title && (
          <Typography variant="h6" gutterBottom>
            {title}
          </Typography>
        )}
        {!readonly && allowAdd && addButtonText && ( <Button variant="outlined" size="small" onClick={addItem}>{addButtonText}</Button> )}
      </Stack>
      
      {/* Current items */}
      {items && items.map((item, index) => (printItem(item,index)) )}
      
      {/* Deleted items */}
      {showDeleted && deletedItems.length > 0 && (
        <Box mt={4}>
          <Typography variant="h6" gutterBottom sx={{ color: 'text.secondary' }}>
            Удаленные элементы
          </Typography>
          
          {deletedItems.map((deletedItem, index) => (
            <Paper
              key={`deleted-${deletedItem.id || deletedItem.uid || index}`}
              elevation={1}
              sx={{ 
                p: 2, 
                mb: 2, 
                position: 'relative',
                border: '1px solid rgba(211, 47, 47, 0.3)',
                opacity: 0.8,
                backgroundColor: 'rgba(211, 47, 47, 0.05)',
              }}
            >
              <Stack direction="row" spacing={2} alignItems="flex-start">
                <Tooltip title="Deleted Item">
                  <DeleteForeverIcon color="error" />
                </Tooltip>
                
                <Box flexGrow={1}>
                  <SchemaGridEdit
                    schema={schema}
                    initialValues={deletedItem}
                    actions={false}
                    readonly={readonly}
                  />
                </Box>
                
                <IconButton 
                  color="primary" 
                  onClick={() => restoreItem(deletedItem)}
                  sx={{ mt: 1 }}
                  title="Restore item"
                >
                  <RestoreIcon />
                </IconButton>
              </Stack>
            </Paper>
          ))}
        </Box>
      )}
      
      {/* {addButtonText && (<Button 
        variant="outlined" 
        startIcon={<span>+</span>}
        onClick={addItem}
        sx={{ mt: 1 }}
      >
        {addButtonText}
      </Button>)} */}
    </Box>
  );
};
