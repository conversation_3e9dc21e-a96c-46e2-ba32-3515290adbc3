export interface SchemaDataValue {
  /** Тип поля */
  type: 'text' | 'number' | 'date' | 'select' | 'textarea' | 'autocomplete' | 'checkbox' | 'boolean' | 'progress' | 'error' | 'list_schema' | 'object_schema' | 'file' | 'array';
  /**schema  для 'list_schema' | 'object_schema' */
  schema?: any;

  

  /** Метка поля */
  label: string;
  /** Обязательное ли поле */
  required?: boolean | ((values: Record<string, any>, key?: string) => boolean);
  disabled?: boolean | ((values: Record<string, any>, key?: string) => boolean);
  readOnly?: boolean | ((values: Record<string, any>, key?: string) => boolean);
  /** Значение по умолчанию autocomplete*/
  defaultValue?: any;
  /** Опции для select или  */
  options?: (string | number | boolean | { value: string | number | boolean; label: string })[];
  /** Валидация */
  validation?: {
    pattern?: RegExp;
    message?: string;
  };
  // /** Размер поля в сетке (1-12) */
  // gridSize?: number;
  /** Множественный выбор для select */
  multiple?: boolean;
  /** Максимальная длина для текстовых полей */
  maxLength?: number;
  /** Минимальное значение для числовых полей */
  min?: number;
  /** Максимальное значение для числовых полей */
  max?: number;
  /** Настройки для автоподстановки */
  autocomplete?: {
    /** URL для загрузки данных */
    apiUrl?: string;
    /** Ключ для отображения в выпадающем списке */
    labelKey: string;
    /** Ключ для значения */
    valueKey: string;
    /** Задержка перед запросом (мс) */
    debounce?: number;
    /** Минимальное количество символов для поиска */
    minChars?: number;
    /** Параметры запроса */
    params?: Record<string, any>;
    /** Сохранять полный объект или только значение */
    saveFullObject?: boolean;
    /** Функция для форматирования отображения объекта */
    formatOptionLabel?: (option: any) => string;
    /** Загружать список при монтировании */
    loadOptionsOnMount?: boolean;
    options?: any[];
  };
  /** Функция для получения значения поля */
  getValue?: (values: Record<string, any>, key?: string) => any;
  /** Функция для установки значения поля */
  setValue?: (values: Record<string, any>, value: any) => Record<string, any>;
  /** Можно ли редактировать поле в форме (по умолчанию true) */
  editable?: boolean;
  /** Отображать ли поле в таблице (по умолчанию true) */
  visible?: boolean;
  /** Является ли поле многострочным текстом */
  multiline?: boolean;
  grid?: {
    size?: string| boolean| number| Array<string| boolean| number>| object,
    props?: any
  },
  catalog?: string,
  slotProps?: any,
  /** Если array не пуст описанное поле массив заполняется по описанной схеме*/
  array?: {
    /** Схема для описания ввода/вывода элемента массива */
    schema_value: SchemaDataValue;
    /** Максимальное количество элементов в массиве */
    maxItems?: number;
    /** Минимальное количество элементов в массиве */
    minItems?: number;
    /** Разрешить дублирование элементов */
    allowDuplicates?: boolean;
    /** Вариант отображения элементов: list - список, chips - чипы, table - таблица, grid - сетка */
    variant?: 'list' | 'chips' | 'table' | 'grid';
  }
}

export interface SchemaData {
  [key: string]: SchemaDataValue;
}