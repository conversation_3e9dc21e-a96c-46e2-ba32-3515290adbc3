import React, { useState, useEffect, useMemo } from 'react';
import {
  Table,
  TableBody,
  TableCell,
  TableContainer,
  TableHead,
  TableRow,
  Paper,
  TablePagination,
  Box,
  TextField,
  IconButton,
  Dialog,
  DialogTitle,
  DialogContent,
  DialogActions,
  Button,
  Fab,
  Tooltip,
  CircularProgress,
  LinearProgress,
  Stack,
} from '@mui/material';
import EditIcon from '@mui/icons-material/Edit';
import DeleteIcon from '@mui/icons-material/Delete';
import AddIcon from '@mui/icons-material/Add';
import { apiClient, PaginatedData } from '@/utils/api';
import { SchemaData, SchemaDataValue } from './Schema';
import { SchemaGridEdit } from './SchemaGridEdit';

export interface ListTableSchemaProps<T> {
  /** Схема таблицы */
  schema: SchemaData;
  /** URL API для получения данных (если используется API) */
  apiUrl?: string;
  /** Массив данных (если не используется API) */
  array?: T[];
  /** Параметры запроса для API */
  params?: Record<string, any>;
  /** Функция для дополнительных действий в колонке действий */
  renderActions?: (row: T) => React.ReactNode;
  /** Флаг отображения кнопки редактирования */
  enableEdit?: boolean;
  /** Флаг отображения кнопки удаления */
  enableDelete?: boolean;
  /** Флаг отображения кнопки добавления */
  enableAdd?: boolean;
  /** Колбэк при удалении записи */
  onDelete?: (id: number | string) => Promise<void>;
  /** Колбэк при создании записи */
  onCreate?: (item: T) => Promise<void>;
  /** Колбэк при обновлении записи */
  onUpdate?: (item: T) => Promise<void>;
  /** Заголовок диалога подтверждения удаления */
  deleteConfirmTitle?: string;
  /** Сообщение диалога подтверждения удаления */
  deleteConfirmMessage?: string;
  /** Максимальная высота таблицы */
  maxHeight?: number | string;
  /** Функция для обработки клика по строке */
  onRowClick?: (item: T) => void;
  /** Колбэк при клике на кнопку создания */
  onCreateClick?: () => void;
  /** Колбэк при изменении параметров */
  onChangeParams?: (params: any) => void;
  /** Кастомная кнопка добавления */
  addButton?: React.ReactNode;
  /** Заголовок для диалога создания/редактирования */
  dialogTitle?: string;
  /** Включить поиск */
  enableSearch?: boolean;
  /** Включить пагинацию */
  enablePagination?: boolean;
}

interface Column {
  field: string;
  headerName: string;
  renderCell?: (params: { row: any }) => React.ReactNode;
}

/**
 * Компонент для отображения таблицы с данными из API или массива
 * Основан на схеме и поддерживает CRUD операции
 */
export const ListTableSchema = <T extends { id?: number | string }>({
  schema,
  apiUrl,
  array,
  params = {},
  renderActions,
  enableEdit = true,
  enableDelete = true,
  enableAdd = true,
  onDelete,
  onCreate,
  onUpdate,
  deleteConfirmTitle = 'Подтверждение удаления',
  deleteConfirmMessage = 'Вы уверены, что хотите удалить эту запись?',
  maxHeight = 'calc(100vh - 300px)',
  onRowClick,
  onCreateClick,
  onChangeParams,
  addButton,
  dialogTitle = 'Редактирование записи',
  enableSearch = true,
  enablePagination = true,
}: ListTableSchemaProps<T>) => {
  const [data, setData] = useState<T[]>([]);
  const [filteredData, setFilteredData] = useState<T[]>([]);
  const [page, setPage] = useState(0);
  const [rowsPerPage, setRowsPerPage] = useState(100);
  const [total, setTotal] = useState(0);
  const [searchTerm, setSearchTerm] = useState('');
  const [loading, setLoading] = useState(false);
  const [editDialogOpen, setEditDialogOpen] = useState(false);
  const [deleteDialogOpen, setDeleteDialogOpen] = useState(false);
  const [selectedItem, setSelectedItem] = useState<T | null>(null);
  const [isCreating, setIsCreating] = useState(false);

  // Определяем колонки на основе схемы
  const columns: Column[] = useMemo(() => {
    const cols: Column[] = [];
    
    Object.entries(schema).forEach(([key, field]) => {
      if (field.visible !== false) {
        cols.push({
          field: key,
          headerName: field.label,
        });
      }
    });

    // Добавляем колонку действий если нужно
    if (enableEdit || enableDelete || renderActions) {
      cols.push({
        field: 'actions',
        headerName: 'Действия',
        renderCell: (params) => (
          <Box sx={{ display: 'flex', gap: 1 }}>
            {enableEdit && (
              <Tooltip title="Редактировать">
                <IconButton
                  size="small"
                  onClick={(e) => {
                    e.stopPropagation();
                    handleEdit(params.row);
                  }}
                >
                  <EditIcon fontSize="small" />
                </IconButton>
              </Tooltip>
            )}
            {enableDelete && (
              <Tooltip title="Удалить">
                <IconButton
                  size="small"
                  color="error"
                  onClick={(e) => {
                    e.stopPropagation();
                    handleDeleteClick(params.row);
                  }}
                >
                  <DeleteIcon fontSize="small" />
                </IconButton>
              </Tooltip>
            )}
            {renderActions?.(params.row)}
          </Box>
        ),
      });
    }

    return cols;
  }, [schema, enableEdit, enableDelete, renderActions]);

  // Загрузка данных из API
  const fetchData = async () => {
    if (!apiUrl) return;
    
    try {
      setLoading(true);
      const requestParams = {
        ...params,
        page: page + 1,
        limit: rowsPerPage,
        q: searchTerm,
      };
      
      const response = await apiClient.get<PaginatedData<T>>(apiUrl, requestParams);
      onChangeParams?.(requestParams);
      
      if (response && Array.isArray(response.items)) {
        setData(response.items);
        setTotal(response.count || 0);
      } else {
        setData([]);
        setTotal(0);
      }
    } catch (error) {
      console.error('Error fetching data:', error);
      setData([]);
      setTotal(0);
    } finally {
      setLoading(false);
    }
  };

  // Фильтрация данных из массива
  const filterArrayData = () => {
    if (!array) return;
    
    let filtered = [...array];
    
    // Применяем поиск
    if (searchTerm) {
      filtered = filtered.filter((item) => {
        return Object.entries(schema).some(([key, field]) => {
          if (field.visible === false) return false;
          const value = getCellValue(item, key, field);
          return String(value).toLowerCase().includes(searchTerm.toLowerCase());
        });
      });
    }
    
    setTotal(filtered.length);
    
    // Применяем пагинацию
    if (enablePagination) {
      const startIndex = page * rowsPerPage;
      const endIndex = startIndex + rowsPerPage;
      filtered = filtered.slice(startIndex, endIndex);
    }
    
    setFilteredData(filtered);
  };

  // Эффекты для загрузки данных
  useEffect(() => {
    if (apiUrl) {
      const timeoutId = setTimeout(() => {
        setPage(0);
        fetchData();
      }, 500);
      return () => clearTimeout(timeoutId);
    }
  }, [searchTerm]);

  useEffect(() => {
    if (apiUrl) {
      fetchData();
    }
  }, [page, rowsPerPage, params]);

  useEffect(() => {
    if (array) {
      filterArrayData();
    }
  }, [array, searchTerm, page, rowsPerPage]);

  // Получение значения ячейки
  const getCellValue = (item: any, field: string, fieldSchema: SchemaDataValue): any => {
    if (fieldSchema.getValue) {
      return fieldSchema.getValue(item, field);
    }
    return item[field];
  };

  // Форматирование значения ячейки
  const formatCellValue = (value: any, fieldSchema: SchemaDataValue): string => {
    if (value === null || value === undefined) return '';
    
    switch (fieldSchema.type) {
      case 'date':
        return new Date(value).toLocaleDateString();
      case 'boolean':
      case 'checkbox':
        return value ? 'Да' : 'Нет';
      case 'array':
        return Array.isArray(value) ? value.join(', ') : '';
      default:
        return String(value);
    }
  };

  // Обработчики событий
  const handleChangePage = (_: unknown, newPage: number) => {
    setPage(newPage);
  };

  const handleChangeRowsPerPage = (event: React.ChangeEvent<HTMLInputElement>) => {
    setRowsPerPage(parseInt(event.target.value, 10));
    setPage(0);
  };

  const handleSearch = (event: React.ChangeEvent<HTMLInputElement>) => {
    setSearchTerm(event.target.value);
  };

  const handleCreate = () => {
    if (onCreateClick) {
      onCreateClick();
    } else {
      setSelectedItem(null);
      setIsCreating(true);
      setEditDialogOpen(true);
    }
  };

  const handleEdit = (item: T) => {
    setSelectedItem(item);
    setIsCreating(false);
    setEditDialogOpen(true);
  };

  const handleDeleteClick = (item: T) => {
    setSelectedItem(item);
    setDeleteDialogOpen(true);
  };

  const handleDeleteConfirm = async () => {
    if (selectedItem && selectedItem.id && onDelete) {
      try {
        await onDelete(selectedItem.id);
        setDeleteDialogOpen(false);
        setSelectedItem(null);
        
        // Обновляем данные
        if (apiUrl) {
          fetchData();
        } else if (array) {
          filterArrayData();
        }
      } catch (error) {
        console.error('Error deleting item:', error);
      }
    }
  };

  const handleSave = async (formData: T) => {
    try {
      if (isCreating && onCreate) {
        await onCreate(formData);
      } else if (!isCreating && onUpdate) {
        await onUpdate(formData);
      }
      
      setEditDialogOpen(false);
      setSelectedItem(null);
      
      // Обновляем данные
      if (apiUrl) {
        fetchData();
      } else if (array) {
        filterArrayData();
      }
    } catch (error) {
      console.error('Error saving item:', error);
    }
  };

  // Определяем какие данные отображать
  const displayData = apiUrl ? data : filteredData;
  const isLoading = apiUrl ? loading : false;

  return (
    <Box>
      <Stack direction="row" spacing={2} sx={{ mb: 2 }}>
        {enableSearch && (
          <Box sx={{ flex: 1 }}>
            <TextField
              fullWidth
              variant="outlined"
              placeholder="Поиск..."
              value={searchTerm}
              onChange={handleSearch}
            />
          </Box>
        )}
        
        {addButton && addButton}
        
        {!addButton && enableAdd && (
          <Fab
            color="primary"
            onClick={handleCreate}
            size="medium"
          >
            <AddIcon />
          </Fab>
        )}
      </Stack>

      <TableContainer
        component={Paper}
        sx={{
          maxHeight,
          overflow: 'auto',
          '& .MuiTable-root': {
            minWidth: 650,
          },
          position: 'relative'
        }}
      >
        {isLoading && (
          <Box
            sx={{
              position: 'absolute',
              top: 0,
              left: 0,
              right: 0,
              zIndex: 1
            }}
          >
            <LinearProgress />
          </Box>
        )}
        
        <Table stickyHeader>
          <TableHead>
            <TableRow>
              {columns.map((column) => (
                <TableCell
                  key={column.field}
                  sx={{
                    backgroundColor: 'background.paper',
                    fontWeight: 'bold',
                    whiteSpace: 'nowrap'
                  }}
                >
                  {column.headerName}
                </TableCell>
              ))}
            </TableRow>
          </TableHead>
          <TableBody>
            {isLoading ? (
              <TableRow>
                <TableCell
                  colSpan={columns.length}
                  align="center"
                  sx={{ py: 8 }}
                >
                  <CircularProgress />
                </TableCell>
              </TableRow>
            ) : displayData.length === 0 ? (
              <TableRow>
                <TableCell
                  colSpan={columns.length}
                  align="center"
                  sx={{ py: 4 }}
                >
                  Нет данных
                </TableCell>
              </TableRow>
            ) : (
              displayData.map((item, index) => (
                <TableRow
                  key={item.id || index}
                  onClick={() => onRowClick?.(item)}
                  sx={{
                    cursor: onRowClick ? 'pointer' : 'default',
                    '&:hover': onRowClick ? {
                      backgroundColor: 'action.hover',
                    } : {}
                  }}
                >
                  {columns.map((column) => (
                    <TableCell
                      key={column.field}
                      sx={{ whiteSpace: 'nowrap' }}
                    >
                      {column.renderCell
                        ? column.renderCell({ row: item })
                        : formatCellValue(getCellValue(item, column.field, schema[column.field]), schema[column.field])}
                    </TableCell>
                  ))}
                </TableRow>
              ))
            )}
          </TableBody>
        </Table>
      </TableContainer>

      {enablePagination && (
        <TablePagination
          component="div"
          count={total}
          page={page}
          onPageChange={handleChangePage}
          rowsPerPage={rowsPerPage}
          onRowsPerPageChange={handleChangeRowsPerPage}
          labelRowsPerPage="Строк на странице:"
        />
      )}

      {/* Диалог редактирования */}
      <Dialog
        open={editDialogOpen}
        onClose={() => setEditDialogOpen(false)}
        maxWidth="md"
        fullWidth
      >
        <DialogTitle>
          {isCreating ? 'Создание записи' : dialogTitle}
        </DialogTitle>
        <DialogContent>
          <SchemaGridEdit
            schema={schema}
            initialValues={(selectedItem || {}) as T}
            onSubmit={handleSave}
            actions={false}
          />
        </DialogContent>
        <DialogActions>
          <Button onClick={() => setEditDialogOpen(false)}>
            Отмена
          </Button>
        </DialogActions>
      </Dialog>

      {/* Диалог подтверждения удаления */}
      <Dialog
        open={deleteDialogOpen}
        onClose={() => setDeleteDialogOpen(false)}
      >
        <DialogTitle>{deleteConfirmTitle}</DialogTitle>
        <DialogContent>
          {deleteConfirmMessage}
        </DialogContent>
        <DialogActions>
          <Button onClick={() => setDeleteDialogOpen(false)}>
            Отмена
          </Button>
          <Button onClick={handleDeleteConfirm} color="error">
            Удалить
          </Button>
        </DialogActions>
      </Dialog>
    </Box>
  );
};
