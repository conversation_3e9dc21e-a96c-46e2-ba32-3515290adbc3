import { SchemaData, SchemaDataValue } from "./Schema";

export interface SchemaFieldInputProps {
  /** Ключ поля */
  fieldKey: string;
  /** Схема поля */
  field: SchemaDataValue;
  /** Значение поля */
  value?: any;
  /** Значение  обьекта*/
  values?: any;
  /** Базовое значение поля */
  baseValue?: any;
  /** Обработчик изменения значения */
  onChange: (key: string, value: any) => void;
  /** Флаг, указывающий что поле изменено относительно базовых значений */
  isChanged?: boolean;
}


export interface SchemaFieldInputContainerProps {
  /** Ключ поля */
  fieldKey: string;
  /** Схема поля */
  field: SchemaDataValue;
  /** Значение поля */
  values?: any;
  /** Базовое значение поля */
  baseValue?: any;
  baseValues?: any;
  /** Ошибка валидации */
  /** Обработчик изменения значения */
  onChange: (key: string, value: any) => void;
  /** Опции для автоподстановки */
  
}


export interface SchemaGridFormProps<T extends Record<string, any>> {
  /** Схема формы */
  schema: SchemaData;
  /** Начальные значения */
  initialValues?: T;
  /** Базовые значения для сравнения (поля, отличающиеся от базовых, будут выделены) */
  baseValues?: T;
  /** Обработчик отправки формы */
  onSubmit?: (values: T) => void;
  /** Обработчик изменения значений */
  onChange?: (values: T) => void;
  /** Обработчик отмены */
  onCancel?: () => void;
  /** Текст кнопки отправки */
  submitText?: string;
  /** Текст кнопки отмены */
  cancelText?: string;
  /** Загрузка формы */
  loading?: boolean;
  /** Ошибки валидации */
  errors?: Record<string, string>;
  /** Ошибка */
  error?: any; //Record<string, string>;
  actions?: boolean;
}
