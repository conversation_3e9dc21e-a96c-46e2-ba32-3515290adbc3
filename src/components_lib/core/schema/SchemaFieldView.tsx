import React from 'react';
import { SchemaFieldInputProps } from './SchemaFieldProps';

/**
 * Компонент для просмотра поля схемы
 * Обертка над ViewField для единообразного API
 */
export const SchemaFieldView: React.FC<SchemaFieldInputProps> = ({
  fieldKey,
  field,
  value,
  values,
  baseValue,
  onChange,
  isChanged = false,
}) => {
  // Получаем отображаемое значение
  const displayValue = field.getValue ? field.getValue(values, fieldKey) : value;

  return (
    <></>
  );
};