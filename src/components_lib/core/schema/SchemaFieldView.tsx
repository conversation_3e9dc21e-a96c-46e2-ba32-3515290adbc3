import React, { useState } from 'react';
import {
  TextField,
  InputAdornment,
  IconButton,
  Menu,
  Box,
  Typography,
  Button,
  Tooltip,
  Checkbox,
  FormControlLabel,
  LinearProgress,
  Alert,
} from '@mui/material';
import { DatePicker } from '@mui/x-date-pickers/DatePicker';
import { SchemaFieldInputProps } from './SchemaFieldProps';
import { useCatalog } from '@/context/CatalogContext';
import HistoryIcon from '@mui/icons-material/History';
import RestoreIcon from '@mui/icons-material/RestoreFromTrash';
import dayjs from 'dayjs';

// Простая заглушка для просмотра поля в массиве
const SimpleFieldView: React.FC<any> = ({ field, value }) => {
  const formatValue = (val: any) => {
    if (val === null || val === undefined) return 'Пусто';
    if (typeof val === 'object') return JSON.stringify(val);
    return String(val);
  };

  return (
    <Typography variant="body2" sx={{ p: 1, backgroundColor: 'grey.100', borderRadius: 1 }}>
      {formatValue(value)}
    </Typography>
  );
};

/**
 * Компонент для просмотра поля схемы (только чтение)
 * Самостоятельная реализация без использования ViewField
 */
export const SchemaFieldView: React.FC<SchemaFieldInputProps> = ({
  fieldKey,
  field,
  value,
  values,
  baseValue,
  onChange,
  isChanged = false,
}) => {
  const [anchorEl, setAnchorEl] = useState<null | HTMLElement>(null);
  const open = Boolean(anchorEl);

  // Получаем отображаемое значение
  const displayValue = field.getValue ? field.getValue(values, fieldKey) : value;

  // Обработчики меню
  const handleMenuOpen = (event: React.MouseEvent<HTMLElement>) => {
    setAnchorEl(event.currentTarget);
  };

  const handleMenuClose = () => {
    setAnchorEl(null);
  };

  const handleRestore = () => {
    onChange(fieldKey, baseValue);
    handleMenuClose();
  };

  // Стили для измененных полей
  const changedFieldStyles = isChanged ? {
    backgroundColor: 'rgba(255, 235, 59, 0.1)',
    borderRadius: '4px',
  } : {};

  // Иконка истории для измененных полей
  const historyIcon = isChanged ? {
    endAdornment: (
      <InputAdornment position="end">
        <Tooltip title="Показать изменения">
          <IconButton
            edge="end"
            onClick={handleMenuOpen}
            size="small"
          >
            <HistoryIcon fontSize="small" />
          </IconButton>
        </Tooltip>
      </InputAdornment>
    )
  } : {};

  const commonProps = {
    fullWidth: true,
    disabled: true, // Всегда disabled для режима просмотра
    sx: {
      '& .MuiInputBase-root': {
        ...changedFieldStyles,
      },
    },
    InputProps: {
      readOnly: true,
      ...historyIcon,
    },
  };

  const renderLabel = (label: string) => {
    return label;
  };

  // Форматирование значения для отображения
  const formatValueForDisplay = (val: any) => {
    if (val === null || val === undefined) return 'Пусто';

    if (field.type === 'date' && val) {
      try {
        return dayjs(val).format('DD.MM.YYYY');
      } catch {
        return String(val);
      }
    }

    if (field.type === 'boolean' || field.type === 'checkbox') {
      return val ? 'Да' : 'Нет';
    }

    if (field.type === 'select') {
      const options = field.options || [];
      const option = options.find((opt: any) =>
        typeof opt === 'object'
          ? opt.value === val
          : opt === val
      );

      if (option) {
        return typeof option === 'object' ? option.label : String(option);
      }
    }

    if (field.type === 'array' && Array.isArray(val)) {
      return val.map(item => String(item)).join(', ');
    }

    return String(val);
  };

  const variant = 'standard';

  // Компонент меню с историей изменений
  function HistoryMenu() {
    return (
      <Menu
        anchorEl={anchorEl}
        open={open}
        onClose={handleMenuClose}
        anchorOrigin={{
          vertical: 'bottom',
          horizontal: 'right',
        }}
        transformOrigin={{
          vertical: 'top',
          horizontal: 'right',
        }}
      >
        <Box sx={{ p: 2, minWidth: '250px' }}>
          <Typography variant="subtitle2" gutterBottom color="text.secondary">
            Оригинальное значение:
          </Typography>
          <Box
            sx={{
              p: 1,
              backgroundColor: 'grey.100',
              borderRadius: 1,
              mb: 2,
              fontFamily: 'monospace',
              fontSize: '0.875rem',
            }}
          >
            {formatValueForDisplay(baseValue)}
          </Box>
          <Button
            onClick={handleRestore}
            startIcon={<RestoreIcon />}
            variant="outlined"
            color="primary"
            size="small"
            fullWidth
          >
            Восстановить
          </Button>
        </Box>
      </Menu>
    );
  }

  return (
    <>
      {renderFieldByType()}
      {isChanged && <HistoryMenu />}
    </>
  );

  function renderFieldByType() {
    switch (field.type) {
      case 'text':
      case 'number':
        return (
          <TextField
            {...commonProps}
            label={renderLabel(field.label)}
            value={formatValueForDisplay(displayValue)}
            variant={variant}
          />
        );

      case 'date':
        return (
          <TextField
            {...commonProps}
            label={renderLabel(field.label)}
            value={formatValueForDisplay(displayValue)}
            variant={variant}
          />
        );

      case 'boolean':
      case 'checkbox':
        return (
          <FormControlLabel
            control={
              <Checkbox
                checked={!!displayValue}
                disabled={true}
              />
            }
            label={renderLabel(field.label)}
          />
        );

      case 'select':
        return (
          <TextField
            {...commonProps}
            label={renderLabel(field.label)}
            value={formatValueForDisplay(displayValue)}
            variant={variant}
          />
        );

      case 'progress':
        return <LinearProgress variant="determinate" value={displayValue || 0} />;

      case 'error':
        return (
          <>
            {displayValue &&
              <Alert severity="error">{JSON.stringify(displayValue)}</Alert>
            }
          </>
        );

      case 'array':
        const arrayValue = Array.isArray(displayValue) ? displayValue : [];
        const arrayConfig = field.array;

        if (!arrayConfig || !arrayConfig.schema_value) {
          return <Alert severity="error">Не настроена схема для элементов массива</Alert>;
        }

        const variant_display = arrayConfig.variant || 'list';

        return (
          <Box>
            <Typography variant="subtitle2" gutterBottom>
              {renderLabel(field.label)}
            </Typography>

            {/* Отображение элементов массива в режиме просмотра */}
            {variant_display === 'list' && (
              <Box sx={{ mb: 2 }}>
                {arrayValue.map((item, index) => (
                  <Box key={index} sx={{ display: 'flex', gap: 1, mb: 1, alignItems: 'center' }}>
                    <SimpleFieldView
                      fieldKey={`item_${index}`}
                      field={arrayConfig.schema_value}
                      value={item}
                      values={{ [`item_${index}`]: item }}
                    />
                  </Box>
                ))}
              </Box>
            )}

            {variant_display === 'chips' && (
              <Box sx={{ display: 'flex', flexWrap: 'wrap', gap: 1, mb: 2 }}>
                {arrayValue.map((item, index) => (
                  <Box
                    key={index}
                    sx={{
                      display: 'inline-flex',
                      alignItems: 'center',
                      backgroundColor: 'grey.200',
                      borderRadius: 1,
                      px: 1,
                      py: 0.5,
                      fontSize: '0.875rem',
                    }}
                  >
                    {formatValueForDisplay(item)}
                  </Box>
                ))}
              </Box>
            )}

            {(variant_display === 'table' || variant_display === 'grid') && (
              <Box sx={{ mb: 2 }}>
                {arrayValue.map((item, index) => (
                  <Box key={index} sx={{ mb: 1, p: 1, border: 1, borderColor: 'divider', borderRadius: 1 }}>
                    <SimpleFieldView
                      fieldKey={`item_${index}`}
                      field={arrayConfig.schema_value}
                      value={item}
                      values={{ [`item_${index}`]: item }}
                    />
                  </Box>
                ))}
              </Box>
            )}

            <Typography variant="caption" color="text.secondary">
              Элементов: {arrayValue.length}
            </Typography>
          </Box>
        );

      default:
        return null;
    }
  }
};