import React from 'react';
import { SchemaFieldInputContainerProps } from './SchemaFieldProps';
import { SchemaFieldView } from './SchemaFieldView';

/**
 * Контейнер для поля просмотра схемы
 * Управляет состоянием поля для режима только чтения
 */
export const SchemaFieldViewContainer: React.FC<SchemaFieldInputContainerProps> = ({
  fieldKey,
  field,
  values,
  baseValues,
  onChange,
}) => {
  // Получаем значение поля
  const value = field.getValue ? field.getValue(values, fieldKey) : values?.[fieldKey];

  // Определяем, отличается ли значение от базового
  let isChanged = false;
  let baseFieldValue = null;
  if (baseValues) {
    baseFieldValue = field.getValue ? field.getValue(baseValues, fieldKey) : baseValues[fieldKey];
    // Для дат и сложных объектов используем JSON.stringify
    if (typeof value === 'object' || typeof baseFieldValue === 'object') {
      isChanged = JSON.stringify(value) !== JSON.stringify(baseFieldValue);
    } else {
      isChanged = value !== baseFieldValue || (value && !baseFieldValue);
    }
  }

  // Пропускаем поля, которые не должны отображаться
  if (field.visible === false) {
    return null;
  }

  return (
    <SchemaFieldView
      fieldKey={fieldKey}
      field={field}
      value={value}
      values={values}
      baseValue={baseFieldValue}
      onChange={onChange}
      isChanged={isChanged}
    />
  );
};