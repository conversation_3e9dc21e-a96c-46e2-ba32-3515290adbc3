
import React, { useState, useEffect } from 'react';
import {
  Box,
  Button,
  Grid,
  Alert,
  CircularProgress,
} from '@mui/material';
import { SchemaGridFormProps } from './SchemaFieldProps';
import { SchemaFieldInputContainer } from './SchemaFieldInputContainer';

/**
 * Компонент формы для редактирования, созданной на основе схемы
 * Использует SchemaFieldInputContainer для каждого поля
 */
export const SchemaGridEdit = <T extends Record<string, any>>({
  schema,
  initialValues = {} as T,
  baseValues,
  onSubmit,
  onChange,
  onCancel,
  submitText = 'Сохранить',
  cancelText = 'Отмена',
  loading = false,
  errors = {},
  error,
  actions = true
}: SchemaGridFormProps<T>) => {
  const [values, setValues] = useState<T>(initialValues);
  const [validationErrors, setValidationErrors] = useState<Record<string, string>>({});

  // Обновляем значения только при изменении initialValues
  useEffect(() => {
    setValues(initialValues);
  }, [JSON.stringify(initialValues)]);

  // Обновляем ошибки только при изменении errors
  useEffect(() => {
    setValidationErrors(errors);
  }, [JSON.stringify(errors)]);

  const handleChange = (key: string, value: any) => {
    const field = schema[key];
    if (!field) return;

    let newValue = value;

    // Применяем setValue если есть
    if (field.setValue) {
      const newValues = field.setValue(values, value);
      setValues(newValues as T);
      onChange?.(newValues as T);
      return;
    }

    const newValues = { ...values, [key]: newValue };
    setValues(newValues);
    onChange?.(newValues);
  };

  const validateForm = () => {
    const newErrors: Record<string, string> = {};
    let isValid = true;

    Object.entries(schema).forEach(([key, field]) => {
      const value = values[key];

      // Проверка обязательности
      const required = typeof field.required === 'function' ? field.required(values, key) : field.required;
      if (required && (!value || (typeof value === 'string' && value.trim() === ''))) {
        newErrors[key] = 'Поле обязательно для заполнения';
        isValid = false;
      }

      // Проверка паттерна
      if (field.validation?.pattern && value && !field.validation.pattern.test(String(value))) {
        newErrors[key] = field.validation.message || 'Неверный формат данных';
        isValid = false;
      }
    });

    setValidationErrors(newErrors);
    return isValid;
  };

  const handleSubmit = (e: React.FormEvent) => {
    e.preventDefault();

    if (validateForm() && onSubmit) {
      onSubmit(values);
    }
  };

  const renderField = (key: string, field: any) => {
    // Пропускаем поля, которые нельзя редактировать
    if (field.editable === false) {
      return null;
    }

    return (
      <SchemaFieldInputContainer
        key={key}
        fieldKey={key}
        field={field}
        values={values}
        baseValues={baseValues}
        onChange={handleChange}
      />
    );
  };

  return (
    <Box component="form" onSubmit={handleSubmit} noValidate>
      <Grid container spacing={3}>
        {Object.entries(schema).map(([key, field]) => (
          <Grid item key={key} size={field.grid?.size || 12} {...(field.grid?.props || {})}>
            {renderField(key, field)}
          </Grid>
        ))}
      </Grid>

      {error && <Alert severity="error" sx={{ mt: 2 }}>{JSON.stringify(error)}</Alert>}

      {actions && (
        <Box sx={{ mt: 3, display: 'flex', gap: 2, justifyContent: 'flex-end' }}>
          {onCancel && (
            <Button onClick={onCancel} disabled={loading}>
              {cancelText}
            </Button>
          )}
          <Button type="submit" variant="contained" disabled={loading}>
            {loading ? <CircularProgress size={24} /> : submitText}
          </Button>
        </Box>
      )}
    </Box>
  );
};