
import { SchemaGridFormProps } from './SchemaFieldProps';


/**
 * Компонент формы, созданной на основе схемы использует SchemaFieldInputContainer
 */
// export const FormBySchema = <T extends Record<string, any>>({
export const SchemaGridEdit = <T extends Record<string, any>>({
  schema,
  initialValues = {} as T,
  baseValues,
  onSubmit,
  onChange,
  onCancel,
  submitText = 'Save',
  cancelText = 'Cancel',
  loading = false,
  errors = {},
  error,
  actions = true
}: SchemaGridFormProps<T>) => {
  
}; 