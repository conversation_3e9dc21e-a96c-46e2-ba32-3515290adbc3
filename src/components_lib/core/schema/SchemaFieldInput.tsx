import React, { useState, useCallback, useEffect, useRef } from 'react';
import {
  TextField,
  Autocomplete,
  CircularProgress,
  InputAdornment,
  IconButton,
  Menu,
  MenuItem,
  Box,
  Typography,
  Button,
  Tooltip,
  Checkbox,
  FormControlLabel,
  LinearProgress,
  Alert,
  Grid,
} from '@mui/material';
import { DatePicker } from '@mui/x-date-pickers/DatePicker';
import { SchemaFieldInputProps } from './SchemaFieldProps';
import { useCatalog } from '@/context/CatalogContext';
import HistoryIcon from '@mui/icons-material/History';
import RestoreIcon from '@mui/icons-material/RestoreFromTrash';
import dayjs from 'dayjs';
import { ListFormBySchema } from '../../ListFormBySchema';
import {
  ArrayItemForm,
  ArrayListView,
  ArrayChipsView,
  ArrayTableView,
  ArrayGridView
} from './ArrayComponents';
import { apiClient } from '@/utils/api';

// Простая заглушка для просмотра поля в массиве
const SimpleFieldView: React.FC<any> = ({ field, value }) => {
  const formatValue = (val: any) => {
    if (val === null || val === undefined) return 'Пусто';
    if (typeof val === 'object') return JSON.stringify(val);
    return String(val);
  };

  return (
    <Typography variant="body2" sx={{ p: 1, backgroundColor: 'grey.100', borderRadius: 1 }}>
      {formatValue(value)}
    </Typography>
  );
};

/**
 * Компонент для ввода поля схемы
 * Самостоятельная реализация без использования FormField
 */
export const SchemaFieldInput: React.FC<SchemaFieldInputProps> = ({
  fieldKey,
  field,
  value,
  values,
  baseValue,
  onChange,
  isChanged = false,
  error,
}) => {
  const [anchorEl, setAnchorEl] = useState<null | HTMLElement>(null);
  const open = Boolean(anchorEl);

  // Состояние для autocomplete
  const [internalAutocompleteOptions, setInternalAutocompleteOptions] = useState<any[]>([]);
  const [internalAutocompleteLoading, setInternalAutocompleteLoading] = useState<boolean>(false);
  const debounceTimers = useRef<Record<string, NodeJS.Timeout>>({});

  // Получаем отображаемое значение
  const displayValue = field.getValue ? field.getValue(values, fieldKey) : value;
  const required = typeof field.required === 'function' ? field.required(values, fieldKey) : field.required || false;

  // Загрузка опций автоподстановки
  const loadAutocompleteOptions = useCallback(async (inputValue: string) => {
    if (field.type !== 'autocomplete' || !field.autocomplete) return;

    const { apiUrl, params = {}, minChars = 0 } = field.autocomplete;

    if (inputValue.length < minChars) {
      setInternalAutocompleteOptions([]);
      return;
    }

    if (!apiUrl) {
      // Используем статические опции
      if (field.autocomplete.options) {
        setInternalAutocompleteOptions(Array.isArray(field.autocomplete.options) ? field.autocomplete.options : []);
      }
      return;
    }

    setInternalAutocompleteLoading(true);
    try {
      console.log('🔍 Autocomplete API request:', {
        apiUrl,
        params: { ...params, q: inputValue },
        fieldKey,
        inputValue
      });

      const response: any = await apiClient.get(apiUrl, {
        ...params,
        q: inputValue,
      });

      console.log('📥 Autocomplete API response:', {
        fieldKey,
        response,
        responseData: response.data,
        responseType: typeof response,
        isArray: Array.isArray(response),
        isDataArray: Array.isArray(response.data)
      });

      let items = [];
      if (Array.isArray(response.items)) {
        items = response.items;
        console.log('📋 Using response.items:', items);
      } else if (response.data && Array.isArray(response.data.items)) {
        items = response.data.items;
        console.log('📋 Using response.data.items:', items);
      } else if (Array.isArray(response.data)) {
        items = response.data;
        console.log('📋 Using response.data:', items);
      } else if (Array.isArray(response)) {
        items = response;
        console.log('📋 Using response:', items);
      } else {
        console.log('⚠️ No array found in response, using empty array');
        items = [];
      }

      console.log('✅ Final autocomplete options:', {
        fieldKey,
        items,
        itemsLength: Array.isArray(items) ? items.length : 'not array',
        firstItem: Array.isArray(items) && items.length > 0 ? items[0] : null
      });

      // Убеждаемся, что items - это массив
      setInternalAutocompleteOptions(Array.isArray(items) ? items : []);
    } catch (error) {
      console.error('❌ Ошибка загрузки опций:', {
        fieldKey,
        apiUrl,
        error,
        errorMessage: error instanceof Error ? error.message : 'Unknown error',
        errorResponse: error && typeof error === 'object' && 'response' in error ? error.response : undefined
      });
      setInternalAutocompleteOptions([]);
    } finally {
      setInternalAutocompleteLoading(false);
    }
  }, [field]);

  // Обработчик изменения ввода автоподстановки
  const handleAutocompleteInputChange = useCallback((inputValue: string) => {
    console.log('⌨️ Autocomplete input change:', {
      fieldKey,
      inputValue,
      inputLength: inputValue.length,
      fieldType: field.type,
      hasAutocomplete: !!field.autocomplete
    });

    if (field.type !== 'autocomplete' || !field.autocomplete) return;

    const { debounce = 300 } = field.autocomplete;

    if (debounceTimers.current[fieldKey]) {
      clearTimeout(debounceTimers.current[fieldKey]);
    }

    debounceTimers.current[fieldKey] = setTimeout(() => {
      console.log('⏰ Debounce timeout triggered, loading options for:', fieldKey, inputValue);
      loadAutocompleteOptions(inputValue);
    }, debounce);
  }, [fieldKey, loadAutocompleteOptions]);

  // Используем внутренние опции и состояние загрузки
  const autocompleteOptions = Array.isArray(internalAutocompleteOptions)
    ? internalAutocompleteOptions
    : [];
  const autocompleteLoading = internalAutocompleteLoading;
  const onAutocompleteInputChange = handleAutocompleteInputChange;

  // Логируем состояние опций перед рендером
  console.log('🎯 Autocomplete render state:', {
    fieldKey,
    autocompleteOptions,
    optionsLength: autocompleteOptions.length,
    autocompleteLoading,
    internalAutocompleteOptions,
    internalOptionsLength: internalAutocompleteOptions.length
  });

  // Загружаем опции при монтировании
  useEffect(() => {
    console.log('🚀 Autocomplete useEffect triggered:', {
      fieldKey,
      fieldType: field.type,
      hasAutocomplete: !!field.autocomplete,
      loadOptionsOnMount: field.autocomplete?.loadOptionsOnMount,
      hasApiUrl: !!field.autocomplete?.apiUrl,
      hasStaticOptions: !!(field.autocomplete?.options && field.autocomplete?.options.length)
    });

    if (field.type === 'autocomplete' && field.autocomplete) {
      if (field.autocomplete.loadOptionsOnMount) {
        console.log('📡 Loading options on mount for field:', fieldKey);
        loadAutocompleteOptions('');
      } else if (field.autocomplete.options && field.autocomplete.options.length) {
        // Загружаем статические опции только если нет API URL
        if (!field.autocomplete.apiUrl) {
          console.log('📋 Loading static options for field:', fieldKey, field.autocomplete.options);
          setInternalAutocompleteOptions(Array.isArray(field.autocomplete.options) ? field.autocomplete.options : []);
        }
      }
    }
  }, [field, loadAutocompleteOptions]);

  // Очистка таймеров при размонтировании
  useEffect(() => {
    return () => {
      Object.values(debounceTimers.current).forEach(timer => {
        if (timer) clearTimeout(timer);
      });
    };
  }, []);

  // Обработчики меню
  const handleMenuOpen = (event: React.MouseEvent<HTMLElement>) => {
    setAnchorEl(event.currentTarget);
  };

  const handleMenuClose = () => {
    setAnchorEl(null);
  };

  const handleRestore = () => {
    onChange(fieldKey, baseValue);
    handleMenuClose();
  };

  // Стили для измененных полей
  const changedFieldStyles = isChanged ? {
    backgroundColor: 'rgba(255, 235, 59, 0.1)',
    borderRadius: '4px',
  } : {};

  // Иконка истории для измененных полей
  const historyIcon = isChanged ? {
    endAdornment: (
      <InputAdornment position="end">
        <Tooltip title="Показать изменения">
          <IconButton
            edge="end"
            onClick={handleMenuOpen}
            size="small"
          >
            <HistoryIcon fontSize="small" />
          </IconButton>
        </Tooltip>
      </InputAdornment>
    )
  } : {};

  const commonProps = {
    fullWidth: true,
    disabled: typeof field.disabled === 'function' ? field.disabled(values, fieldKey) : field.disabled || false,
    readOnly: typeof field.readOnly === 'function' ? field.readOnly(values, fieldKey) : field.readOnly || false,
    error: !!error,
    helperText: error,
    sx: {
      '& .MuiInputBase-root': {
        ...changedFieldStyles,
      },
    },
    InputProps: historyIcon,
  };

  const handleFieldChange = (newValue: any) => {
    if (field && field.readOnly) { return }
    onChange(fieldKey, newValue);
  };

  const renderLabel = (label: string) => {
    return label;
  };

  let options: (string | number | boolean | { value: string | number | boolean; label: string })[] = field.options || [];

  if (field.catalog && !field.options) {
    const catalog = useCatalog();
    options = catalog.getCatalogItem(field.catalog);
  }

  const variant = 'standard';

  // Компонент меню с историей изменений
  function HistoryMenu() {
    return (
      <Menu
        anchorEl={anchorEl}
        open={open}
        onClose={handleMenuClose}
        anchorOrigin={{
          vertical: 'bottom',
          horizontal: 'right',
        }}
        transformOrigin={{
          vertical: 'top',
          horizontal: 'right',
        }}
      >
        <Box sx={{ p: 2, minWidth: '250px' }}>
          <Typography variant="subtitle2" gutterBottom color="text.secondary">
            Оригинальное значение:
          </Typography>
          <Box
            sx={{
              p: 1,
              backgroundColor: 'grey.100',
              borderRadius: 1,
              mb: 2,
              fontFamily: 'monospace',
              fontSize: '0.875rem',
            }}
          >
            {baseValue !== null && baseValue !== undefined ? String(baseValue) : 'Пусто'}
          </Box>
          <Button
            onClick={handleRestore}
            startIcon={<RestoreIcon />}
            variant="outlined"
            color="primary"
            size="small"
            fullWidth
          >
            Восстановить
          </Button>
        </Box>
      </Menu>
    );
  }

  return (
    <>
      {renderFieldByType()}
      {isChanged && <HistoryMenu />}
    </>
  );

  function renderFieldByType() {
    switch (field.type) {
      case 'text':
        return (
          <TextField
            {...commonProps}
            label={renderLabel(field.label)}
            value={displayValue || ''}
            onChange={(e) => handleFieldChange(e.target.value)}
            required={required}
            multiline={field.multiline}
            maxRows={field.multiline ? 4 : 1}
            variant={variant}
            inputProps={{
              maxLength: field.maxLength,
            }}
          />
        );

      case 'number':
        return (
          <TextField
            {...commonProps}
            type="number"
            label={renderLabel(field.label)}
            value={displayValue || ''}
            onChange={(e) => handleFieldChange(e.target.value)}
            required={required}
            variant={variant}
            inputProps={{
              min: field.min,
              max: field.max,
            }}
            slotProps={field.slotProps}
          />
        );

      case 'date':
        return (
          <DatePicker
            value={dayjs(displayValue || '')}
            defaultValue={null}
            onChange={(newDate) => handleFieldChange(newDate?.toISOString())}
            label={renderLabel(field.label)}
            slotProps={{ textField: { size: 'small', variant: variant, fullWidth: true, error: false } }}
          />
        );

      case 'boolean':
      case 'checkbox':
        return (
          <FormControlLabel
            control={
              <Checkbox
                checked={!!displayValue}
                onChange={(e) => handleFieldChange(e.target.checked)}
                required={required}
              />
            }
            label={renderLabel(field.label)}
          />
        );

      case 'select':
        return (
          <TextField
            {...commonProps}
            select
            label={renderLabel(field.label)}
            value={displayValue || ''}
            onChange={(e) => handleFieldChange(e.target.value)}
            required={required}
            variant={variant}
            SelectProps={{
              multiple: field.multiple,
            }}
          >
            {options?.map((option: string | number | boolean | { value: string | number | boolean; label: string }) => {
              const value = typeof option === 'object'
                ? (typeof option.value === 'boolean' ? String(option.value) : option.value)
                : (typeof option === 'boolean' ? String(option) : option);
              const label = typeof option === 'object' ? option.label : String(option);
              return (
                <MenuItem
                  key={String(value)}
                  value={value}
                >
                  {label}
                </MenuItem>
              );
            })}
          </TextField>
        );

      case 'autocomplete':
        return (
          <Autocomplete
            fullWidth
            readOnly={commonProps.readOnly}
            options={autocompleteOptions}
            loading={autocompleteLoading}
            value={field.autocomplete?.saveFullObject ? displayValue : autocompleteOptions.find(opt => opt[field.autocomplete?.valueKey || 'value'] === displayValue) || null}
            onChange={(_, newValue) => handleFieldChange(newValue)}
            onInputChange={(_, newInputValue) => onAutocompleteInputChange?.(newInputValue)}
            getOptionLabel={(option) => {
              if (!option) return '';
              if (typeof option === 'string') return option;
              if (field.autocomplete?.formatOptionLabel) {
                return field.autocomplete.formatOptionLabel(option);
              }
              return option[field.autocomplete?.labelKey || 'label'] || '';
            }}
            isOptionEqualToValue={(option, value) => {
              if (!option || !value) return false;
              if (field.autocomplete?.saveFullObject) {
                return option[field.autocomplete.valueKey] === value[field.autocomplete.valueKey];
              }
              return option[field.autocomplete?.valueKey || 'value'] === value;
            }}
            renderInput={(params) => (
              <TextField
                {...params}
                {...commonProps}
                label={renderLabel(field.label)}
                variant={variant}
                InputProps={{
                  ...params.InputProps,
                  ...(autocompleteLoading ? {
                    endAdornment: (
                      <React.Fragment>
                        <CircularProgress color="inherit" size={20} />
                        {params.InputProps.endAdornment}
                      </React.Fragment>
                    ),
                  } : {}),
                  ...historyIcon,
                }}
              />
            )}
            disabled={commonProps.disabled}
          />
        );

      case 'progress':
        return <LinearProgress variant="determinate" value={displayValue || 0} />;

      case 'error':
        return (
          <>
            {displayValue &&
              <Alert severity="error">{JSON.stringify(displayValue)}</Alert>
            }
          </>
        );

      case 'array':
        return renderArrayField();

      default:
        return null;
    }
  }

  function renderArrayField() {
    const arrayConfig = field.array;
    if (!arrayConfig || !arrayConfig.schema_value) {
      return <Alert severity="error">Не настроена схема для элементов массива</Alert>;
    }

    const arrayValue = Array.isArray(displayValue) ? displayValue : [];
    const variant_display = arrayConfig.variant || 'list';

    const handleAddItem = (newItem: any) => {
      const newArray = [...arrayValue, newItem];

      // Проверяем ограничения
      if (arrayConfig.maxItems && newArray.length > arrayConfig.maxItems) {
        return; // Не добавляем, если превышен лимит
      }

      if (!arrayConfig.allowDuplicates) {
        // Проверяем дубликаты (простое сравнение через JSON)
        const itemStr = JSON.stringify(newItem);
        const exists = arrayValue.some(item => JSON.stringify(item) === itemStr);
        if (exists) {
          return; // Не добавляем дубликат
        }
      }

      handleFieldChange(newArray);
    };

    const handleRemoveItem = (index: number) => {
      const newArray = arrayValue.filter((_, i) => i !== index);
      handleFieldChange(newArray);
    };

    const handleUpdateItem = (index: number, newValue: any) => {
      const newArray = [...arrayValue];
      newArray[index] = newValue;
      handleFieldChange(newArray);
    };

    return (
      <Box>
        <Typography variant="subtitle2" gutterBottom>
          {renderLabel(field.label)}
          {required && <span style={{ color: 'red' }}> *</span>}
        </Typography>

        {/* Форма добавления нового элемента */}
        <ArrayItemForm
          schema={arrayConfig.schema_value}
          onAdd={handleAddItem}
          disabled={arrayConfig.maxItems ? arrayValue.length >= arrayConfig.maxItems : false}
          FieldComponent={SchemaFieldInput}
        />

        {/* Отображение элементов массива */}
        {variant_display === 'list' && (
          <ArrayListView
            items={arrayValue}
            schema={arrayConfig.schema_value}
            onRemove={handleRemoveItem}
            onUpdate={handleUpdateItem}
            readOnly={commonProps.readOnly}
            FieldInputComponent={SchemaFieldInput}
            FieldViewComponent={SimpleFieldView}
          />
        )}

        {variant_display === 'chips' && (
          <ArrayChipsView
            items={arrayValue}
            schema={arrayConfig.schema_value}
            onRemove={handleRemoveItem}
            readOnly={commonProps.readOnly}
            FieldInputComponent={SchemaFieldInput}
            FieldViewComponent={SimpleFieldView}
          />
        )}

        {variant_display === 'table' && (
          <ArrayTableView
            items={arrayValue}
            schema={arrayConfig.schema_value}
            onRemove={handleRemoveItem}
            onUpdate={handleUpdateItem}
            readOnly={commonProps.readOnly}
            FieldInputComponent={SchemaFieldInput}
            FieldViewComponent={SimpleFieldView}
          />
        )}

        {variant_display === 'grid' && (
          <ArrayGridView
            items={arrayValue}
            schema={arrayConfig.schema_value}
            onRemove={handleRemoveItem}
            onUpdate={handleUpdateItem}
            readOnly={commonProps.readOnly}
            FieldInputComponent={SchemaFieldInput}
            FieldViewComponent={SimpleFieldView}
          />
        )}

        <Typography variant="caption" color="text.secondary">
          Элементов: {arrayValue.length}
          {arrayConfig.maxItems && ` / ${arrayConfig.maxItems}`}
          {arrayConfig.minItems && arrayValue.length < arrayConfig.minItems &&
            ` (минимум: ${arrayConfig.minItems})`
          }
        </Typography>
      </Box>
    );
  }
};