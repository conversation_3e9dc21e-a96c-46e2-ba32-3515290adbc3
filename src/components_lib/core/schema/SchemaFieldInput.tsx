import React from 'react';
import { SchemaFieldInputProps } from './SchemaFieldProps';

/**
 * Компонент для отображения поля формы по образцу с FormField
 * работы с api для списков
 */
export const SchemaFieldInput: React.FC<SchemaFieldInputProps> = ({
  fieldKey,
  field,
  value,
  values,
  baseValue,
  onChange,
  isChanged = false,
}) => {
  // Получаем отображаемое значение
  return (
    <></>
  );
};