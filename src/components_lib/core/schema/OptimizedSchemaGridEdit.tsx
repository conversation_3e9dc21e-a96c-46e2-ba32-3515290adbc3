import React, { useMemo, useCallback } from 'react';
import { Grid, <PERSON>, Button, Stack } from '@mui/material';
import { SchemaGridFormProps } from './SchemaFieldProps';
import { MemoizedSchemaField } from './MemoizedSchemaField';
import { SchemaFieldInputContainer } from './SchemaFieldInputContainer';
import { useOptimizedFormState } from '@/hooks/useOptimizedFormState';

// Простые типы полей, которые можно мемоизировать
const SIMPLE_FIELD_TYPES = ['text', 'number', 'date', 'select', 'checkbox', 'boolean', 'textarea', 'autocomplete'];

/**
 * Оптимизированная версия SchemaGridEdit
 * Использует мемоизацию полей для предотвращения лишних ререндеров
 * Для сложных полей (object_schema, list_schema, array) не использует мемоизацию
 */
export const OptimizedSchemaGridEdit = <T extends Record<string, any>>({
  schema,
  initialValues = {} as T,
  baseValues,
  onSubmit,
  onChange,
  onCancel,
  submitText = 'Сохранить',
  cancelText = 'Отмена',
  loading = false,
  errors = {},
  actions = true,
  container: Container = Box,
  readonly = false,
}: SchemaGridFormProps<T>) => {
  // Используем оптимизированный хук для управления состоянием
  const {
    values,
    handleFieldChange,
    isFieldChanged,
    hasChanges,
  } = useOptimizedFormState({
    initialValues,
    baseValues,
    onChange,
    debounceDelay: 100, // Быстрый отклик для UI
  });

  // Мемоизируем ключи полей для предотвращения пересоздания массива
  const fieldKeys = useMemo(() => Object.keys(schema), [schema]);

  // Мемоизируем базовые значения
  const memoizedBaseValues: any = useMemo(() => baseValues || {}, [baseValues]);

  // Мемоизированный обработчик отправки
  const handleSubmit = useCallback((e: React.FormEvent) => {
    e.preventDefault();
    onSubmit?.(values);
  }, [onSubmit, values]);

  // Мемоизированные поля с предвычисленными значениями
  const renderedFields = useMemo(() => {
    return fieldKeys.map(fieldKey => {
      const field = schema[fieldKey];
      // Правильно вычисляем значение с учетом field.getValue
      const value = field.getValue ? field.getValue(values, fieldKey) : values[fieldKey];
      const baseValue = field.getValue && memoizedBaseValues
        ? field.getValue(memoizedBaseValues, fieldKey)
        : memoizedBaseValues?.[fieldKey];

      // Для простых полей используем мемоизированный компонент
      // Для сложных полей используем обычный контейнер
      if (SIMPLE_FIELD_TYPES.includes(field.type)) {
        return (
          <MemoizedSchemaField
            key={fieldKey}
            fieldKey={fieldKey}
            field={field}
            value={value}
            baseValue={baseValue}
            allValues={values}
            baseValues={memoizedBaseValues}
            onChange={handleFieldChange}
            readonly={readonly}
          />
        );
      } else {
        // Для сложных полей используем обычный контейнер без мемоизации
        return (
          <Grid key={fieldKey} size={field.grid?.size || 12} {...(field.grid?.props || {})}>
            <SchemaFieldInputContainer
              fieldKey={fieldKey}
              field={field}
              value={value}
              baseValue={baseValue}
              values={values}
              baseValues={memoizedBaseValues}
              onChange={handleFieldChange}
              readonly={readonly}
            />
          </Grid>
        );
      }
    });
  }, [fieldKeys, schema, values, memoizedBaseValues, handleFieldChange, readonly]);

  return (
    <Container component="form" onSubmit={handleSubmit}>
      <Grid container spacing={2}>
        {renderedFields}
        
        {actions && !readonly && (
          <Grid size={12}>
            <Stack direction="row" spacing={2} sx={{ mt: 2 }}>
              <Button
                type="submit"
                variant="contained"
                disabled={loading}
              >
                {loading ? 'Сохранение...' : submitText}
              </Button>
              {onCancel && (
                <Button
                  type="button"
                  variant="outlined"
                  onClick={onCancel}
                  disabled={loading}
                >
                  {cancelText}
                </Button>
              )}
            </Stack>
          </Grid>
        )}
      </Grid>
    </Container>
  );
};
