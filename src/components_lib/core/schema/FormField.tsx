import React, { useState, useCallback, useEffect, useRef } from 'react';
import {
  TextField,
  Autocomplete,
  CircularProgress,
  InputAdornment,
  IconButton,
  Menu,
  MenuItem,
  Box,
  Typography,
  Button,
  Tooltip,
  Checkbox,
  FormControlLabel,
  LinearProgress,
  Alert,
} from '@mui/material';
import { DatePicker } from '@mui/x-date-pickers/DatePicker';
import { SchemaDataValue } from './Schema';
import { useCatalog } from '@/context/CatalogContext';
import HistoryIcon from '@mui/icons-material/History';
import RestoreIcon from '@mui/icons-material/RestoreFromTrash';
import dayjs from 'dayjs';
import { Alarm } from '@mui/icons-material';
import { ListFormBySchema } from '../../ListFormBySchema';
import { FieldProps } from './FieldProps';
import { apiClient } from '@/utils/api';

/**
 * Компонент для отображения поля формы
 */
export const FormField: React.FC<FieldProps> = ({
  fieldKey,
  field,
  value,
  values,
  baseValue,
  error,
  onChange,
  autocompleteOptions,
  autocompleteLoading,
  onAutocompleteInputChange,
  isChanged = false,
}) => {

  if(field && field.readOnly) isChanged = false
  // Состояние для меню
  const [anchorEl, setAnchorEl] = useState<null | HTMLElement>(null);
  const open = Boolean(anchorEl);
  const required = typeof field.required == 'function' ? field.required(values, fieldKey) : field.required || false;

  // Внутреннее состояние для autocomplete
  const [internalAutocompleteOptions, setInternalAutocompleteOptions] = useState<any[]>([]);
  const [internalAutocompleteLoading, setInternalAutocompleteLoading] = useState<boolean>(false);
  const debounceTimers = useRef<Record<string, NodeJS.Timeout>>({});

  // Загрузка опций автоподстановки
  const loadAutocompleteOptions = useCallback(async (inputValue: string) => {
    if (field.type !== 'autocomplete' || !field.autocomplete) return;

    const { apiUrl, params = {}, minChars = 0 } = field.autocomplete;

    if (inputValue.length < minChars) {
      setInternalAutocompleteOptions([]);
      return;
    }

    if (!apiUrl) {
      // Используем статические опции
      if (field.autocomplete.options) {
        setInternalAutocompleteOptions(Array.isArray(field.autocomplete.options) ? field.autocomplete.options : []);
      }
      return;
    }

    setInternalAutocompleteLoading(true);
    try {
      const response: any = await apiClient.get(apiUrl, {
        ...params,
        q: inputValue,
      });

      let items = response.data || response;
      if (response.data && Array.isArray(response.data.items)) {
        items = response.data.items;
      }

      // Убеждаемся, что items - это массив
      setInternalAutocompleteOptions(Array.isArray(items) ? items : []);
    } catch (error) {
      console.error('Ошибка загрузки опций:', error);
      setInternalAutocompleteOptions([]);
    } finally {
      setInternalAutocompleteLoading(false);
    }
  }, [field]);

  // Обработчик изменения ввода автоподстановки
  const handleInternalAutocompleteInputChange = useCallback((fieldKey: string, inputValue: string) => {
    if (field.type !== 'autocomplete' || !field.autocomplete) return;

    const { debounce = 300 } = field.autocomplete;

    if (debounceTimers.current[fieldKey]) {
      clearTimeout(debounceTimers.current[fieldKey]);
    }

    debounceTimers.current[fieldKey] = setTimeout(() => {
      loadAutocompleteOptions(inputValue);
    }, debounce);
  }, [fieldKey, loadAutocompleteOptions]);

  // Определяем какие опции и состояние загрузки использовать
  const finalAutocompleteOptions = Array.isArray(autocompleteOptions)
    ? autocompleteOptions
    : Array.isArray(internalAutocompleteOptions)
    ? internalAutocompleteOptions
    : [];
  const finalAutocompleteLoading = autocompleteLoading || internalAutocompleteLoading;
  const finalOnAutocompleteInputChange = onAutocompleteInputChange || handleInternalAutocompleteInputChange;

  // Загружаем опции при монтировании
  useEffect(() => {
    if (field.type === 'autocomplete' && field.autocomplete?.loadOptionsOnMount) {
      loadAutocompleteOptions('');
    }

    if (field.type === 'autocomplete' && field.autocomplete?.options && field.autocomplete?.options.length) {
      setInternalAutocompleteOptions(Array.isArray(field.autocomplete.options) ? field.autocomplete.options : []);
    }
  }, [field, loadAutocompleteOptions]);

  // Открытие меню
  const handleMenuOpen = (event: React.MouseEvent<HTMLElement>) => {
    setAnchorEl(event.currentTarget);
  };

  // Закрытие меню
  const handleMenuClose = () => {
    setAnchorEl(null);
  };

  // Восстановление базового значения
  const handleRestore = () => {
    onChange(fieldKey, baseValue);
    handleMenuClose();
  };

  // Пропускаем поля, которые нельзя редактировать
  // if (field.editable === false) {
  //   return null;
  // }

  // Стили для измененных полей
  const changedFieldStyles = isChanged ? {
    backgroundColor: 'rgba(255, 235, 59, 0.1)', // Легкий желтый фон
    borderRadius: '4px',
  } : {};

  // Иконка истории для измененных полей
  const historyIcon = isChanged ? {
    endAdornment: (
      <InputAdornment position="end">
        <Tooltip title="Показать изменения">
          <IconButton
            edge="end"
            onClick={handleMenuOpen}
            size="small"
          >
            <HistoryIcon color="action" fontSize="small" />
          </IconButton>
        </Tooltip>
      </InputAdornment>
    )
  } : {};

  // Форматирование значения для отображения в меню
  const formatValueForDisplay = (val: any) => {
    if (val === null || val === undefined) return 'Пусто';

    if (field.type === 'date' && val) {
      try {
        return dayjs(new Date(val)).format('DD.MM.YYYY');
      } catch (e) {
        return String(val);
      }
    }

    if (typeof val === 'object') {
      if (field.autocomplete?.formatOptionLabel) {
        return field.autocomplete.formatOptionLabel(val);
      }
      return field.autocomplete?.labelKey ? val[field.autocomplete.labelKey] : JSON.stringify(val);
    }

    if (typeof val === 'boolean' || field.type === 'checkbox') {
      return val ? 'Да' : 'Нет';
    }

    if (field.type === 'select') {
      const options = field.options || [];
      const option = options.find((opt: any) =>
        typeof opt === 'object'
          ? opt.value === val
          : opt === val
      );

      if (option) {
        return typeof option === 'object' ? option.label : String(option);
      }
    }

    return String(val);
  };

  const commonProps = {
    fullWidth: true,
    // По умолчанию поле редактируемое, если явно не указано обратное
    disabled: typeof field.disabled === 'function' ? field.disabled(values, fieldKey) : field.disabled || false,
    readOnly: typeof field.readOnly === 'function' ? field.readOnly(values, fieldKey) : field.readOnly || false,
    error: !!error,
    helperText: error,
    sx: {
      '& .MuiInputBase-root': {
        ...changedFieldStyles,
      },
    },
    InputProps: historyIcon,
    // size:"small"
  };

  const renderLabel = (label: string) => {
    return  label // required ? `${label} *` : label;
  };

  // Получаем отображаемое значение
  const displayValue = value
  const handleFieldChange = (newValue: any) => {
    if(field && field.readOnly ) { return }
    onChange(fieldKey, newValue);
  };

  const handleListTabChange = (formData: any[], tab?: string) => {
    onChange(fieldKey, formData);  
  };


  let options: (string | number | boolean | { value: string | number | boolean; label: string })[] = field.options || [];

  if (field.catalog && !field.options) {
    const catalog = useCatalog();
    options = catalog.getCatalogItem(field.catalog)
  }

  const variant = 'standard'
  // Компонент меню с историей изменений
  function HistoryMenu() {
    return (
      <Menu
        anchorEl={anchorEl}
        open={open}
        onClose={handleMenuClose}
        anchorOrigin={{
          vertical: 'bottom',
          horizontal: 'right',
        }}
        transformOrigin={{
          vertical: 'top',
          horizontal: 'right',
        }}
      >
        <Box sx={{ p: 2, minWidth: '250px' }}>
          <Typography variant="subtitle2" gutterBottom color="text.secondary">
            Оригинальное значение:
          </Typography>

          <Box
            sx={{
              p: 1.5,
              mb: 1.5,
              bgcolor: 'background.paper',
              border: '1px solid',
              borderColor: 'divider',
              borderRadius: 1
            }}
          >
            <Typography>
              {formatValueForDisplay(baseValue)}
            </Typography>
          </Box>

          <Button
            startIcon={<RestoreIcon />}
            onClick={handleRestore}
            variant="outlined"
            size="small"
            fullWidth
          >
            Восстановить
          </Button>
        </Box>
      </Menu>
    );
  }

  switch (field.type) {
    case 'text':
      return (
        <>
          <TextField
            {...commonProps}
            label={renderLabel(field.label)}
            value={displayValue || ''}
            onChange={(e) => handleFieldChange(e.target.value)}
            required={required}
            multiline={field.multiline}
            maxRows={field.multiline ? 4 : 1}
            variant={variant}
            inputProps={{
              maxLength: field.maxLength,
            }}
          />
          {isChanged && (
            <HistoryMenu />
          )}
        </>
      );

    case 'number':
      return (
        <>
          <TextField
            {...commonProps}
            type="number"
            label={renderLabel(field.label)}
            value={displayValue || ''}
            onChange={(e) => handleFieldChange(e.target.value)}
            required={required}
            variant={variant}
            inputProps={{
              min: field.min,
              max: field.max,
            }}
            slotProps={field.slotProps}
          />
          {isChanged && (
            <HistoryMenu />
          )}
        </>
      );

    case 'date':
      return (
        <>
          <DatePicker
                value={dayjs(displayValue || '')}
                // name={field}
                defaultValue={null}
                
                onChange={(newDate) => handleFieldChange(newDate?.toISOString()) }
                label={renderLabel(field.label)}
                slotProps={{ textField: { size: 'small', variant: variant, fullWidth: true, error: false } }}
                />
          {isChanged && (
            <HistoryMenu />
          )}
        </>
      );
    case 'boolean':
    case 'checkbox':
      return (
        <>
          <FormControlLabel
            control={
              <Checkbox
                checked={!!displayValue}
                onChange={(e) => handleFieldChange(e.target.checked)}
                required={required}
              />
            }
            label={renderLabel(field.label)}
          />
          {isChanged && (
            <HistoryMenu />
          )}
        </>
      );

    case 'select':
      return (
        <>
          <TextField
            {...commonProps}
            select
            label={renderLabel(field.label)}
            value={displayValue || ''}
            onChange={(e) => handleFieldChange(e.target.value)}
            required={required}
            variant={variant}
            SelectProps={{
              multiple: field.multiple,
            }}
            slotProps={{
              input: {
                readOnly: commonProps.readOnly,
              },
            }}
          >
            { options?.map((option: string | number | boolean | { value: string | number | boolean; label: string }) => {
              // Convert value to string if it's a boolean
              const value = typeof option === 'object'
                ? (typeof option.value === 'boolean' ? String(option.value) : option.value)
                : (typeof option === 'boolean' ? String(option) : option);
              const label = typeof option === 'object' ? option.label : String(option);
              return (
                <MenuItem
                  key={String(value)}
                  value={value}
                >
                  {label}
                </MenuItem>
              );
            })}
          </TextField>
          {isChanged && (
            <HistoryMenu />
          )}
        </>
      );

    case 'autocomplete':
      return (
        <>
          <Autocomplete
            fullWidth
            readOnly={commonProps.readOnly}
            options={finalAutocompleteOptions}
            loading={finalAutocompleteLoading}
            value={field.autocomplete?.saveFullObject ? displayValue : finalAutocompleteOptions.find(opt => opt[field.autocomplete?.valueKey || 'value'] === displayValue) || null}
            onChange={(_, newValue) => handleFieldChange(newValue)}
            onInputChange={(_, newInputValue) => finalOnAutocompleteInputChange?.(fieldKey, newInputValue)}
            getOptionLabel={(option) => {
              if (!option) return '';
              if (typeof option === 'string') return option;
              if (field.autocomplete?.formatOptionLabel) {
                return field.autocomplete.formatOptionLabel(option);
              }
              return option[field.autocomplete?.labelKey || 'label'] || '';
            }}
            isOptionEqualToValue={(option, value) => {
              if (!option || !value) return false;
              if (field.autocomplete?.saveFullObject) {
                return option[field.autocomplete.valueKey] === value[field.autocomplete.valueKey];
              }
              return option[field.autocomplete?.valueKey || 'value'] === value;
            }}
            renderInput={(params) => (
              <TextField
                {...params}
                {...commonProps}
                label={renderLabel(field.label)}
                variant={variant}
                InputProps={{
                  ...params.InputProps,
                  ...(finalAutocompleteLoading ? {
                    endAdornment: (
                      <React.Fragment>
                        <CircularProgress color="inherit" size={20} />
                        {params.InputProps.endAdornment}
                      </React.Fragment>
                    ),
                  } : {}),
                  ...historyIcon,
                }}
              />
            )}
          />
          {isChanged && (
            <HistoryMenu />
          )}
        </>
      );
    case 'progress':
      return ( <LinearProgress variant="determinate" value={displayValue || 0} /> )

    case 'error':
      return  (<>
        { displayValue && 
          <Alert severity="error">{JSON.stringify(displayValue)}</Alert>
        }
      </>)
    case 'list_schema': 
        return (<>
          <ListFormBySchema
            schema={field.schema}
            items={value || []}
            baseItems={ baseValue || [] }
            onChange={(e: any) => handleListTabChange(e)}
            addButtonText="Add"
            title={field.label}
          />
        </>)
    default:
      return null;
  }
};