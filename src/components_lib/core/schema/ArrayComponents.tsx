import React, { useState } from 'react';
import {
  Box,
  Typography,
  Button,
  IconButton,
  Grid,
} from '@mui/material';
import DeleteIcon from '@mui/icons-material/Delete';

// Интерфейсы
interface ArrayItemFormProps {
  schema: any;
  onAdd: (item: any) => void;
  disabled?: boolean;
  FieldComponent: React.ComponentType<any>;
}

interface ArrayViewProps {
  items: any[];
  schema: any;
  onRemove: (index: number) => void;
  onUpdate?: (index: number, value: any) => void;
  readOnly?: boolean;
  FieldInputComponent: React.ComponentType<any>;
  FieldViewComponent: React.ComponentType<any>;
}

// Форма для добавления нового элемента
export const ArrayItemForm: React.FC<ArrayItemFormProps> = ({ 
  schema, 
  onAdd, 
  disabled, 
  FieldComponent 
}) => {
  const [newItem, setNewItem] = useState<any>(schema.defaultValue || '');

  const handleAdd = () => {
    if (newItem !== '' && newItem !== null && newItem !== undefined) {
      onAdd(newItem);
      setNewItem(schema.defaultValue || '');
    }
  };

  const handleKeyPress = (e: React.KeyboardEvent) => {
    if (e.key === 'Enter') {
      e.preventDefault();
      handleAdd();
    }
  };

  return (
    <Box sx={{ display: 'flex', gap: 1, mb: 2, alignItems: 'center' }}>
      <FieldComponent
        fieldKey="new_item"
        field={schema}
        value={newItem}
        values={{ new_item: newItem }}
        onChange={(_: any, value: any) => setNewItem(value)}
      />
      <Button
        variant="contained"
        onClick={handleAdd}
        disabled={disabled || !newItem}
        size="small"
        onKeyPress={handleKeyPress}
      >
        add
      </Button>
    </Box>
  );
};

// Отображение в виде списка
export const ArrayListView: React.FC<ArrayViewProps> = ({ 
  items, 
  schema, 
  onRemove, 
  onUpdate, 
  readOnly,
  FieldInputComponent,
  FieldViewComponent
}) => {
  return (
    <Box sx={{ mb: 2 }}>
      {items.map((item, index) => (
        <Box key={index} sx={{ display: 'flex', gap: 1, mb: 1, alignItems: 'center' }}>
          {onUpdate && !readOnly ? (
            <FieldInputComponent
              fieldKey={`item_${index}`}
              field={schema}
              value={item}
              values={{ [`item_${index}`]: item }}
              onChange={(_:any, value: any) => onUpdate(index, value)}
            />
          ) : (
            <FieldViewComponent
              fieldKey={`item_${index}`}
              field={schema}
              value={item}
              values={{ [`item_${index}`]: item }}
              onChange={() => {}}
            />
          )}
          {!readOnly && (
            <IconButton
              size="small"
              onClick={() => onRemove(index)}
              color="error"
            >
              <DeleteIcon />
            </IconButton>
          )}
        </Box>
      ))}
    </Box>
  );
};

// Отображение в виде чипов
export const ArrayChipsView: React.FC<ArrayViewProps> = ({ 
  items, 
  schema, 
  onRemove, 
  readOnly 
}) => {
  const formatChipLabel = (item: any) => {
    if (typeof item === 'string') return item;
    if (typeof item === 'object' && item !== null) {
      // Пытаемся найти подходящее поле для отображения
      return item.name || item.label || item.title || JSON.stringify(item);
    }
    return String(item);
  };

  return (
    <Box sx={{ display: 'flex', flexWrap: 'wrap', gap: 1, mb: 2 }}>
      {items.map((item, index) => (
        <Box
          key={index}
          sx={{
            display: 'inline-flex',
            alignItems: 'center',
            backgroundColor: 'primary.light',
            color: 'primary.contrastText',
            borderRadius: 1,
            px: 1,
            py: 0.5,
            fontSize: '0.875rem',
          }}
        >
          {formatChipLabel(item)}
          {!readOnly && (
            <IconButton
              size="small"
              onClick={() => onRemove(index)}
              sx={{ ml: 0.5, color: 'inherit' }}
            >
              <DeleteIcon fontSize="small" />
            </IconButton>
          )}
        </Box>
      ))}
    </Box>
  );
};

// Отображение в виде таблицы
export const ArrayTableView: React.FC<ArrayViewProps> = ({ 
  items, 
  schema, 
  onRemove, 
  onUpdate, 
  readOnly,
  FieldInputComponent,
  FieldViewComponent
}) => {
  return (
    <Box sx={{ mb: 2 }}>
      <Typography variant="subtitle2" gutterBottom>
        Элементы массива (таблица)
      </Typography>
      {items.map((item, index) => (
        <Box key={index} sx={{ display: 'flex', gap: 1, mb: 1, p: 1, border: 1, borderColor: 'divider', borderRadius: 1 }}>
          <Box sx={{ flex: 1 }}>
            {onUpdate && !readOnly ? (
              <FieldInputComponent
                fieldKey={`item_${index}`}
                field={schema}
                value={item}
                values={{ [`item_${index}`]: item }}
                onChange={(_: any, value: any) => onUpdate(index, value)}
              />
            ) : (
              <FieldViewComponent
                fieldKey={`item_${index}`}
                field={schema}
                value={item}
                values={{ [`item_${index}`]: item }}
                onChange={() => {}}
              />
            )}
          </Box>
          {!readOnly && (
            <IconButton
              size="small"
              onClick={() => onRemove(index)}
              color="error"
            >
              <DeleteIcon />
            </IconButton>
          )}
        </Box>
      ))}
    </Box>
  );
};

// Отображение в виде сетки
export const ArrayGridView: React.FC<ArrayViewProps> = ({ 
  items, 
  schema, 
  onRemove, 
  onUpdate, 
  readOnly,
  FieldInputComponent,
  FieldViewComponent
}) => {
  return (
    <Grid container spacing={2} sx={{ mb: 2 }}>
      {items.map((item, index) => (
        <Grid  size={{xs:12, sm:6, md:4}} key={index}>
          <Box sx={{ p: 2, border: 1, borderColor: 'divider', borderRadius: 1, position: 'relative' }}>
            {onUpdate && !readOnly ? (
              <FieldInputComponent
                fieldKey={`item_${index}`}
                field={schema}
                value={item}
                values={{ [`item_${index}`]: item }}
                onChange={(_: any, value: any) => onUpdate(index, value)}
              />
            ) : (
              <FieldViewComponent
                fieldKey={`item_${index}`}
                field={schema}
                value={item}
                values={{ [`item_${index}`]: item }}
                onChange={() => {}}
              />
            )}
            {!readOnly && (
              <IconButton
                size="small"
                onClick={() => onRemove(index)}
                color="error"
                sx={{ position: 'absolute', top: 4, right: 4 }}
              >
                <DeleteIcon />
              </IconButton>
            )}
          </Box>
        </Grid>
      ))}
    </Grid>
  );
};
