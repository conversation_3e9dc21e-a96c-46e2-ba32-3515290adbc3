import { SchemaDataValue } from '../Schema';

/**
 * Утилиты для работы со значениями полей схемы
 */

/**
 * Получает значение поля с учетом field.getValue
 * @param field - схема поля
 * @param values - все значения формы
 * @param fieldKey - ключ поля
 * @param directValue - прямо переданное значение (для оптимизированных компонентов)
 * @returns значение поля
 */
export function getFieldValue(
  field: SchemaDataValue,
  values: any,
  fieldKey: string,
  directValue?: any
): any {
  // Если передано прямое значение (оптимизированная версия), используем его
  if (directValue !== undefined) {
    return directValue;
  }

  // Иначе используем field.getValue или прямое обращение к values
  return field.getValue ? field.getValue(values, fieldKey) : values?.[fieldKey];
}

/**
 * Получает базовое значение поля с учетом field.getValue
 * @param field - схема поля
 * @param baseValues - базовые значения формы
 * @param fieldKey - ключ поля
 * @param directBaseValue - прямо переданное базовое значение (для оптимизированных компонентов)
 * @returns базовое значение поля
 */
export function getFieldBaseValue(
  field: SchemaDataValue,
  baseValues: any,
  fieldKey: string,
  directBaseValue?: any
): any {
  // Если передано прямое базовое значение (оптимизированная версия), используем его
  if (directBaseValue !== undefined) {
    return directBaseValue;
  }

  // Если нет базовых значений, возвращаем undefined
  if (!baseValues) {
    return undefined;
  }

  // Иначе используем field.getValue или прямое обращение к baseValues
  return field.getValue ? field.getValue(baseValues, fieldKey) : baseValues[fieldKey];
}

/**
 * Проверяет, изменилось ли значение поля
 * @param currentValue - текущее значение
 * @param baseValue - базовое значение
 * @param directIsChanged - прямо переданный флаг изменения (для оптимизированных компонентов)
 * @returns true если поле изменилось
 */
export function isFieldChanged(
  currentValue: any,
  baseValue: any,
  directIsChanged?: boolean
): boolean {
  // Если передан прямой флаг изменения (оптимизированная версия), используем его
  if (directIsChanged !== undefined) {
    return directIsChanged;
  }

  // Если нет базового значения, считаем что поле не изменилось
  if (baseValue === undefined) {
    return false;
  }

  // Для сложных объектов используем JSON.stringify
  if (typeof currentValue === 'object' || typeof baseValue === 'object') {
    return JSON.stringify(currentValue) !== JSON.stringify(baseValue);
  }

  // Для простых значений
  return currentValue !== baseValue;
}

/**
 * Получает все необходимые значения для поля
 * @param field - схема поля
 * @param values - все значения формы
 * @param baseValues - базовые значения формы
 * @param fieldKey - ключ поля
 * @param directValue - прямо переданное значение
 * @param directBaseValue - прямо переданное базовое значение
 * @param directIsChanged - прямо переданный флаг изменения
 * @returns объект с текущим значением, базовым значением и флагом изменения
 */
export function getFieldData(
  field: SchemaDataValue,
  values: any,
  baseValues: any,
  fieldKey: string,
  directValue?: any,
  directBaseValue?: any,
  directIsChanged?: boolean
) {
  const currentValue = getFieldValue(field, values, fieldKey, directValue);
  const baseValue = getFieldBaseValue(field, baseValues, fieldKey, directBaseValue);
  const changed = isFieldChanged(currentValue, baseValue, directIsChanged);

  return {
    currentValue,
    baseValue,
    isChanged: changed,
  };
}

/**
 * Форматирует значение для отображения в UI
 * @param value - значение для форматирования
 * @param emptyText - текст для пустых значений
 * @returns отформатированная строка
 */
export function formatValueForDisplay(value: any, emptyText: string = 'Пусто'): string {
  if (value === null || value === undefined) {
    return emptyText;
  }

  if (typeof value === 'object') {
    return JSON.stringify(value, null, 2);
  }

  return String(value);
}

/**
 * Проверяет, является ли тип поля простым (для мемоизации)
 * @param fieldType - тип поля
 * @returns true если поле простое
 */
export function isSimpleFieldType(fieldType: string): boolean {
  const SIMPLE_FIELD_TYPES = [
    'text', 
    'number', 
    'date', 
    'select', 
    'checkbox', 
    'boolean', 
    'textarea', 
    'autocomplete'
  ];
  
  return SIMPLE_FIELD_TYPES.includes(fieldType);
}

/**
 * Проверяет, является ли тип поля сложным (не подходит для мемоизации)
 * @param fieldType - тип поля
 * @returns true если поле сложное
 */
export function isComplexFieldType(fieldType: string): boolean {
  const COMPLEX_FIELD_TYPES = [
    'object_schema',
    'list_schema', 
    'array',
    'file'
  ];
  
  return COMPLEX_FIELD_TYPES.includes(fieldType);
}
