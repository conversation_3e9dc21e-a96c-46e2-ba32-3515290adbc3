import React, { useState, useEffect, useCallback } from 'react';
import { SchemaFieldInputContainerProps } from './SchemaFieldProps';
import { SchemaFieldInput } from './SchemaFieldInput';
import { apiClient } from '../../../utils/api';

/**
 * Контейнер для поля ввода схемы
 * Управляет состоянием поля, автоподстановкой, валидацией
 */
export const SchemaFieldInputContainer: React.FC<SchemaFieldInputContainerProps> = ({
  fieldKey,
  field,
  values,
  baseValues,
  onChange,
}) => {
  const [autocompleteOptions, setAutocompleteOptions] = useState<any[]>([]);
  const [autocompleteLoading, setAutocompleteLoading] = useState<boolean>(false);
  const [validationError, setValidationError] = useState<string>('');
  const debounceTimers = React.useRef<Record<string, NodeJS.Timeout>>({});

  // Получаем значение поля
  const value = field.getValue ? field.getValue(values, fieldKey) : values?.[fieldKey];

  // Определяем, отличается ли значение от базового
  let isChanged = false;
  let baseFieldValue = null;
  if (baseValues) {
    baseFieldValue = field.getValue ? field.getValue(baseValues, fieldKey) : baseValues[fieldKey];
    // Для дат и сложных объектов используем JSON.stringify
    if (typeof value === 'object' || typeof baseFieldValue === 'object') {
      isChanged = JSON.stringify(value) !== JSON.stringify(baseFieldValue);
    } else {
      isChanged = value !== baseFieldValue || (value && !baseFieldValue);
    }
  }

  // Загрузка опций автоподстановки
  const loadAutocompleteOptions = useCallback(async (inputValue: string) => {
    if (field.type !== 'autocomplete' || !field.autocomplete) return;

    const { apiUrl, params = {}, minChars = 0 } = field.autocomplete;

    if (inputValue.length < minChars) {
      setAutocompleteOptions([]);
      return;
    }

    if (!apiUrl) {
      // Используем статические опции
      if (field.autocomplete.options) {
        setAutocompleteOptions(field.autocomplete.options);
      }
      return;
    }

    setAutocompleteLoading(true);
    try {
      const response: any = await apiClient.get(apiUrl, {
        params: {
          ...params,
          search: inputValue,
        },
      });

      let items = response.data;
      if (response.data && Array.isArray(response.data.items)) {
        items = response.data.items;
      }

      setAutocompleteOptions(items);
    } catch (error) {
      console.error('Ошибка загрузки опций:', error);
      setAutocompleteOptions([]);
    } finally {
      setAutocompleteLoading(false);
    }
  }, [field]);

  // Обработчик изменения ввода автоподстановки
  const handleAutocompleteInputChange = useCallback((inputValue: string) => {
    if (field.type !== 'autocomplete' || !field.autocomplete) return;

    const { debounce = 300 } = field.autocomplete;

    if (debounceTimers.current[fieldKey]) {
      clearTimeout(debounceTimers.current[fieldKey]);
    }

    debounceTimers.current[fieldKey] = setTimeout(() => {
      loadAutocompleteOptions(inputValue);
    }, debounce);
  }, [fieldKey, loadAutocompleteOptions]);

  // Валидация поля
  const validateField = useCallback((fieldValue: any) => {
    if (!field.validation) {
      setValidationError('');
      return true;
    }

    const { pattern, message } = field.validation;

    // Проверка обязательности
    const required = typeof field.required === 'function' ? field.required(values, fieldKey) : field.required;
    if (required && (!fieldValue || (typeof fieldValue === 'string' && fieldValue.trim() === ''))) {
      setValidationError('Поле обязательно для заполнения');
      return false;
    }

    // Проверка паттерна
    if (pattern && fieldValue && !pattern.test(String(fieldValue))) {
      setValidationError(message || 'Неверный формат данных');
      return false;
    }

    setValidationError('');
    return true;
  }, [field, values, fieldKey]);

  // Обработчик изменения значения
  const handleChange = useCallback((key: string, newValue: any) => {
    // Валидируем новое значение
    validateField(newValue);

    // Передаем изменение наверх
    onChange(key, newValue);
  }, [onChange, validateField]);

  // Загружаем опции при монтировании
  useEffect(() => {
    if (field.type === 'autocomplete' && field.autocomplete?.loadOptionsOnMount) {
      loadAutocompleteOptions('');
    }

    if (field.type === 'autocomplete' && field.autocomplete?.options && field.autocomplete?.options.length) {
      setAutocompleteOptions(field.autocomplete.options);
    }
  }, [field, loadAutocompleteOptions]);

  // Пропускаем поля, которые нельзя редактировать
  if (field.editable === false) {
    return null;
  }

  return (
    <SchemaFieldInput
      fieldKey={fieldKey}
      field={field}
      value={value}
      values={values}
      baseValue={baseFieldValue}
      onChange={handleChange}
      isChanged={isChanged}
      // Дополнительные пропы для автоподстановки
      autocompleteOptions={autocompleteOptions}
      autocompleteLoading={autocompleteLoading}
      onAutocompleteInputChange={handleAutocompleteInputChange}
      error={validationError}
    />
  );
};