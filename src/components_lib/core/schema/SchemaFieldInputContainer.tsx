import React, { useState } from 'react';
import { SchemaFieldInputContainerProps } from './SchemaFieldProps';

/**
 * Компонент для отображения элемента формы по схеме
 * отметка отмена изменения values от baseValues
 * управление массивом schema array
 */
export const SchemaFieldInputContainer: React.FC<SchemaFieldInputContainerProps> = ({
  fieldKey,
  field,
  values,
  baseValue,
  onChange,
  
}) => {

  return (<></>)

};