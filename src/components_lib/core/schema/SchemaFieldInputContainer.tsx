import React, { useState, useCallback } from 'react';
import { SchemaFieldInputContainerProps } from './SchemaFieldProps';
import { SchemaFieldInput } from './SchemaFieldInput';

/**
 * Контейнер для поля ввода схемы
 * Управляет состоянием поля, автоподстановкой, валидацией
 */
export const SchemaFieldInputContainer: React.FC<SchemaFieldInputContainerProps> = ({
  fieldKey,
  field,
  values,
  baseValues,
  onChange,
}) => {
  const [validationError, setValidationError] = useState<string>('');

  // Получаем значение поля
  const value = field.getValue ? field.getValue(values, fieldKey) : values?.[fieldKey];

  // Определяем, отличается ли значение от базового
  let isChanged = false;
  let baseFieldValue = null;
  if (baseValues) {
    baseFieldValue = field.getValue ? field.getValue(baseValues, fieldKey) : baseValues[fieldKey];
    // Для дат и сложных объектов используем JSON.stringify
    if (typeof value === 'object' || typeof baseFieldValue === 'object') {
      isChanged = JSON.stringify(value) !== JSON.stringify(baseFieldValue);
    } else {
      isChanged = value !== baseFieldValue || (value && !baseFieldValue);
    }
  }



  // Валидация поля
  const validateField = useCallback((fieldValue: any) => {
    if (!field.validation) {
      setValidationError('');
      return true;
    }

    const { pattern, message } = field.validation;

    // Проверка обязательности
    const required = typeof field.required === 'function' ? field.required(values, fieldKey) : field.required;
    if (required && (!fieldValue || (typeof fieldValue === 'string' && fieldValue.trim() === ''))) {
      setValidationError('Поле обязательно для заполнения');
      return false;
    }

    // Проверка паттерна
    if (pattern && fieldValue && !pattern.test(String(fieldValue))) {
      setValidationError(message || 'Неверный формат данных');
      return false;
    }

    setValidationError('');
    return true;
  }, [field, values, fieldKey]);

  // Обработчик изменения значения
  const handleChange = useCallback((key: string, newValue: any) => {
    // Валидируем новое значение
    validateField(newValue);

    // Передаем изменение наверх
    onChange(key, newValue);
  }, [onChange, validateField]);



  // Пропускаем поля, которые нельзя редактировать
  if (field.editable === false) {
    return null;
  }

  return (
    <SchemaFieldInput
      fieldKey={fieldKey}
      field={field}
      value={value}
      values={values}
      baseValue={baseFieldValue}
      onChange={handleChange}
      isChanged={isChanged}
      error={validationError}
    />
  );
};