import React, { useState, useCallback } from 'react';
import { SchemaFieldInputContainerProps } from './SchemaFieldProps';
import { SchemaFieldInput } from './SchemaFieldInput';
import { getFieldData } from './utils/fieldValueUtils';

/**
 * Контейнер для поля ввода схемы
 * Управляет состоянием поля, автоподстановкой, валидацией
 */
export const SchemaFieldInputContainer: React.FC<SchemaFieldInputContainerProps> = ({
  fieldKey,
  field,
  value: propValue,
  baseValue: propBaseValue,
  isChanged: propIsChanged,
  values,
  baseValues,
  onChange,
  readonly = false
}) => {
  const [validationError, setValidationError] = useState<string>('');

  // Используем утилиту для получения всех данных поля
  const { currentValue: value, baseValue: baseFieldValue, isChanged } = getFieldData(
    field,
    values,
    baseValues,
    fieldKey,
    propValue,
    propBaseValue,
    propIsChanged
  );



  // Валидация поля
  const validateField = useCallback((fieldValue: any) => {
    if (!field.validation) {
      setValidationError('');
      return true;
    }

    const { pattern, message } = field.validation;

    // Проверка обязательности
    const required = typeof field.required === 'function' ? field.required(values, fieldKey) : field.required;
    if (required && (!fieldValue || (typeof fieldValue === 'string' && fieldValue.trim() === ''))) {
      setValidationError('Поле обязательно для заполнения');
      return false;
    }

    // Проверка паттерна
    if (pattern && fieldValue && !pattern.test(String(fieldValue))) {
      setValidationError(message || 'Неверный формат данных');
      return false;
    }

    setValidationError('');
    return true;
  }, [field, values, fieldKey]);

  // Обработчик изменения значения
  const handleChange = useCallback((key: string, newValue: any) => {
    console.log(key, newValue)
    // Валидируем новое значение
    validateField(newValue);
    // Передаем изменение наверх
    onChange(key, newValue);
  }, [onChange, validateField]);



  // Пропускаем поля, которые нельзя редактировать
  if (field.editable === false) {
    return null;
  }

  return (
    <SchemaFieldInput
      fieldKey={fieldKey}
      field={field}
      value={value}
      values={values}
      baseValue={baseFieldValue}
      onChange={handleChange}
      isChanged={isChanged}
      error={validationError}
      readonly={readonly}
    />
  );
};