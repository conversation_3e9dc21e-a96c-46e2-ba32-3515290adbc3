import React, { useState } from 'react';
import {
  TextField,
  Autocomplete,
  CircularProgress,
  InputAdornment,
  IconButton,
  Menu,
  MenuItem,
  Box,
  Typography,
  Button,
  Tooltip,
  Checkbox,
  FormControlLabel,
  LinearProgress,
  Alert,
} from '@mui/material';
import { DatePicker } from '@mui/x-date-pickers/DatePicker';
import { SchemaDataValue } from './Schema';
import { useCatalog } from '@/context/CatalogContext';
import HistoryIcon from '@mui/icons-material/History';
import RestoreIcon from '@mui/icons-material/RestoreFromTrash';
import dayjs from 'dayjs';
import { Alarm } from '@mui/icons-material';
import { ListFormBySchema } from '../../ListFormBySchema';
import { FieldProps } from './FieldProps';
import { FormField } from './FormField';
import { SchemaFieldInputContainerProps } from './SchemaFieldProps';

/**
 * Компонент для отображения элемента формы по схеме
 * отметка отмена изменения values от baseValues
 * управление массивом schema array
 */
export const SchemaFieldInputContainer: React.FC<SchemaFieldInputContainerProps> = ({
  fieldKey,
  field,
  values,
  baseValue,
  onChange,
  
}) => {

  return (<></>)

};