import { SchemaDataValue } from "./Schema";

export interface FieldProps {
  /** Ключ поля */
  fieldKey: string;
  /** Схема поля */
  field: SchemaDataValue;
  /** Значение поля */
  value?: any;
  values?: any;
  /** Базовое значение поля */
  baseValue?: any;
  /** Ошибка валидации */
  error?: string;
  /** Обработчик изменения значения */
  onChange: (key: string, value: any) => void;
  /** Опции для автоподстановки */
  autocompleteOptions?: any[];
  /** Загрузка автоподстановки */
  autocompleteLoading?: boolean;
  /** Обработчик изменения ввода автоподстановки */
  onAutocompleteInputChange?: (key: string, value: string) => void;
  /** Флаг, указывающий что поле изменено относительно базовых значений */
  isChanged?: boolean;
}
