import React, { useState, useEffect, useMemo, useCallback } from 'react';
import { Grid, Box, Button, Stack } from '@mui/material';
import { SchemaGridFormProps } from './SchemaFieldProps';
import { SchemaFieldInputContainer } from './SchemaFieldInputContainer';
import { useDebounce } from '@/hooks/useDebounce';

/**
 * Быстрая версия SchemaGridEdit для вложенных объектов
 * Использует минимальную мемоизацию и быстрый debounce
 */
export const FastSchemaGridEdit = <T extends Record<string, any>>({
  schema,
  initialValues = {} as T,
  baseValues,
  onSubmit,
  onChange,
  onCancel,
  submitText = 'Сохранить',
  cancelText = 'Отмена',
  loading = false,
  actions = true,
  container: Container = Box,
  readonly = false,
}: SchemaGridFormProps<T>) => {
  const [values, setValues] = useState<T>(initialValues);

  // Обновляем значения при изменении initialValues
  useEffect(() => {
    setValues(initialValues);
  }, [initialValues]);

  // Быстрый debounced обработчик изменений
  const debouncedOnChange = useDebounce(
    useCallback((newValues: T) => {
      onChange?.(newValues);
    }, [onChange]),
    50 // Очень быстрый debounce для вложенных объектов
  );

  // Обработчик изменения поля
  const handleFieldChange = useCallback((key: string, value: any) => {
    setValues(prev => {
      const newValues = { ...prev, [key]: value };
      debouncedOnChange(newValues);
      return newValues;
    });
  }, [debouncedOnChange]);

  // Обработчик отправки
  const handleSubmit = useCallback((e: React.FormEvent) => {
    e.preventDefault();
    onSubmit?.(values);
  }, [onSubmit, values]);

  // Мемоизируем только ключи полей
  const fieldKeys = useMemo(() => Object.keys(schema), [schema]);

  // Рендерим поля без дополнительной мемоизации
  const renderedFields = fieldKeys.map(fieldKey => {
    const field = schema[fieldKey];
    
    // Пропускаем поля, которые нельзя редактировать
    if (field.editable === false) {
      return null;
    }

    // Вычисляем значения
    const value = field.getValue ? field.getValue(values, fieldKey) : values[fieldKey];
    const baseValue = field.getValue && baseValues 
      ? field.getValue(baseValues, fieldKey) 
      : baseValues?.[fieldKey];

    return (
      <Grid key={fieldKey} size={field.grid?.size || 12} {...(field.grid?.props || {})}>
        <SchemaFieldInputContainer
          fieldKey={fieldKey}
          field={field}
          value={value}
          baseValue={baseValue}
          values={values}
          baseValues={baseValues}
          onChange={handleFieldChange}
          readonly={readonly}
        />
      </Grid>
    );
  });

  return (
    <Container component="form" onSubmit={handleSubmit}>
      <Grid container spacing={2}>
        {renderedFields}
        
        {actions && !readonly && (
          <Grid size={12}>
            <Stack direction="row" spacing={2} sx={{ mt: 2 }}>
              <Button
                type="submit"
                variant="contained"
                disabled={loading}
              >
                {loading ? 'Сохранение...' : submitText}
              </Button>
              {onCancel && (
                <Button
                  type="button"
                  variant="outlined"
                  onClick={onCancel}
                  disabled={loading}
                >
                  {cancelText}
                </Button>
              )}
            </Stack>
          </Grid>
        )}
      </Grid>
    </Container>
  );
};
