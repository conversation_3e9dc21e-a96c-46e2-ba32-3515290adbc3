import React, { useState } from 'react';
import {
  TextField,
  InputAdornment,
  IconButton,
  Menu,
  Box,
  Typography,
  Button,
  Tooltip,
  Checkbox,
  FormControlLabel,
  LinearProgress,
  Alert,
} from '@mui/material';
import { DatePicker } from '@mui/x-date-pickers/DatePicker';
import { SchemaFieldInputProps } from './SchemaFieldProps';
import { useCatalog } from '@/context/CatalogContext';
import HistoryIcon from '@mui/icons-material/History';
import RestoreIcon from '@mui/icons-material/RestoreFromTrash';
import dayjs from 'dayjs';
import { SchemaDataValue } from './Schema';
import tr from 'date-fns/locale/tr/index';
import { c } from 'vite/dist/node/moduleRunnerTransport.d-CXw_Ws6P';

export const valueFieldSchema = ( fieldKey: string, field? : SchemaDataValue, values: any = {}, value?: any) => {
  try {
    return field && field.getValue 
      ? field.getValue(values, fieldKey)
      : field && field.type == 'autocomplete' && field.autocomplete?.formatOptionLabel 
        ? field.autocomplete?.formatOptionLabel(value)
        : values && values[fieldKey] != undefined ? values[fieldKey] : value;
  } catch(err) {
    debugger
    return value
  }
  
}

export const formatValueForDisplay = (val: any, field?: SchemaDataValue) => {
    if (val === null || val === undefined) return 'Пусто';

    switch (field?.type) {
      case 'date':
        try {
          return dayjs(val).format('DD.MM.YYYY');
        } catch {
          return String(val);
        } 
      case 'boolean':
      case 'checkbox':
        return val ? 'Да' : 'Нет';
      case 'select':
        const options = field.options || [];
        const option = options.find((opt: any) =>
          typeof opt === 'object'
            ? opt.value === val
            : opt === val
        );

        if (option) {
          return typeof option === 'object' ? option.label : String(option);
        }
      case 'array': 
        return Array.isArray(val) ? val.map((item: any) => String(item)).join(', ') : val;

      default: return val
    }

    return String(val);
  };


/**
 * Коeпонент для просмотра поля схемы (только чтение)
 * Самостоятельная реализация без использования ViewField
 */
export const SchemaValue: React.FC<SchemaFieldInputProps> = ({
  fieldKey,
  field,
  value,
  values,
  baseValue,
  onChange,
  isChanged = false,
}) => {
  const [anchorEl, setAnchorEl] = useState<null | HTMLElement>(null);
  const open = Boolean(anchorEl);

  // Получаем отображаемое значение
  const displayValue = valueFieldSchema(fieldKey, field, values, value)

  

  return (
    <>
      {renderFieldByType()}
    </>
  );

  function renderFieldByType() {
    switch (field.type) {
      case 'text':
      case 'number':
        return (
          <Box>{displayValue}</Box>
          
        );

      case 'date':
        return (
          <Box>{displayValue}</Box>
        );

      case 'boolean':
      case 'checkbox':
        return (
          <Box>
            {formatValueForDisplay(displayValue, field)}
          </Box>
        )

      case 'select':
        return (
          <Box>
            {formatValueForDisplay(displayValue, field)}
          </Box>
          
        );

      case 'progress':
        return <LinearProgress variant="determinate" value={displayValue || 0} />;

      case 'error':
        return (
          <>
            {displayValue &&
              <Alert severity="error">{JSON.stringify(displayValue)}</Alert>
            }
          </>
        );

      case 'array':
        const arrayValue = Array.isArray(displayValue) ? displayValue : [];
        const arrayConfig = field.array;

        if (!arrayConfig || !arrayConfig.schema_value) {
          return <Alert severity="error">Не настроена схема для элементов массива</Alert>;
        }

        const variant_display = arrayConfig.variant || 'list';

        return (
          <Box>
            

            {/* Отображение элементов массива в режиме просмотра */}
            {variant_display === 'list' && (
              <Box sx={{ mb: 2 }}>
                {arrayValue.map((item, index) => (
                  <Box key={index} sx={{ display: 'flex', gap: 1, mb: 1, alignItems: 'center' }}>
                    {formatValueForDisplay(valueFieldSchema(fieldKey, field.array?.schema_value , null, item), field.array?.schema_value )}
                  </Box>
                ))}
              </Box>
            )}

            {variant_display === 'chips' && (
              <Box sx={{ display: 'flex', flexWrap: 'wrap', gap: 1, mb: 2 }}>
                {arrayValue.map((item, index) => (
                  <Box
                    key={index}
                    sx={{
                      display: 'inline-flex',
                      alignItems: 'center',
                      backgroundColor: 'grey.200',
                      borderRadius: 1,
                      px: 1,
                      py: 0.5,
                      fontSize: '0.875rem',
                    }}
                  >
                    {formatValueForDisplay(valueFieldSchema(fieldKey, field.array?.schema_value , null, item), field.array?.schema_value )}
                  </Box>
                ))}
              </Box>
            )}

            {(variant_display === 'table' || variant_display === 'grid') && (
              <Box sx={{ mb: 2 }}>
                {arrayValue.map((item, index) => (
                  <Box key={index} sx={{ mb: 1, p: 1, border: 1, borderColor: 'divider', borderRadius: 1 }}>
                    {formatValueForDisplay(valueFieldSchema(fieldKey, field.array?.schema_value , null, item), field.array?.schema_value )}
                  </Box>
                ))}
              </Box>
            )}

            {/* <Typography variant="caption" color="text.secondary">
              Элементов: {arrayValue.length}
            </Typography> */}
          </Box>
        );

      default:
        return null;
    }
  }
};