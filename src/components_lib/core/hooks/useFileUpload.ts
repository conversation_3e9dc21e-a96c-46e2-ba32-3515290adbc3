import { useState, useCallback } from 'react';

export interface UseFileUploadOptions {
  /** URL для загрузки файла */
  url: string;
  /** Дополнительные параметры для запроса */
  additionalParams?: Record<string, any>;
  /** Callback по окончанию загрузки */
  onComplete?: (response: any, file: File) => void;
  /** Callback при ошибке */
  onError?: (error: any, file: File) => void;
  /** Callback при изменении прогресса */
  onProgress?: (progress: number, file: File) => void;
  /** Максимальный размер файла в байтах */
  maxSize?: number;
}

export interface UploadState {
  isUploading: boolean;
  progress: number;
  error: string | null;
  response: any;
}

export const useFileUpload = (options: UseFileUploadOptions) => {
  const [uploadState, setUploadState] = useState<UploadState>({
    isUploading: false,
    progress: 0,
    error: null,
    response: null,
  });

  const uploadFile = useCallback(async (file: File): Promise<any> => {
    const { url, additionalParams = {}, maxSize, onComplete, onError, onProgress } = options;

    // Валидация размера файла
    if (maxSize && file.size > maxSize) {
      const error = `File size exceeds ${Math.round(maxSize / 1024 / 1024)}MB`;
      setUploadState(prev => ({ ...prev, error }));
      onError?.(new Error(error), file);
      throw new Error(error);
    }

    setUploadState({
      isUploading: true,
      progress: 0,
      error: null,
      response: null,
    });

    return new Promise((resolve, reject) => {
      const formData = new FormData();
      formData.append('file', file);
      
      // Добавляем дополнительные параметры
      Object.entries(additionalParams).forEach(([key, value]) => {
        formData.append(key, String(value));
      });

      const xhr = new XMLHttpRequest();

      // Обработчик прогресса
      xhr.upload.addEventListener('progress', (event) => {
        if (event.lengthComputable) {
          const progress = Math.round((event.loaded / event.total) * 100);
          setUploadState(prev => ({ ...prev, progress }));
          onProgress?.(progress, file);
        }
      });

      // Обработчик завершения
      xhr.addEventListener('load', () => {
        if (xhr.status >= 200 && xhr.status < 300) {
          let response;
          try {
            response = JSON.parse(xhr.responseText);
          } catch {
            response = xhr.responseText;
          }

          setUploadState({
            isUploading: false,
            progress: 100,
            error: null,
            response,
          });

          onComplete?.(response, file);
          resolve(response);
        } else {
          const error = `Upload failed: ${xhr.status} ${xhr.statusText}`;
          setUploadState({
            isUploading: false,
            progress: 0,
            error,
            response: null,
          });
          onError?.(new Error(error), file);
          reject(new Error(error));
        }
      });

      // Обработчик ошибки
      xhr.addEventListener('error', () => {
        const error = 'Network error occurred';
        setUploadState({
          isUploading: false,
          progress: 0,
          error,
          response: null,
        });
        onError?.(new Error(error), file);
        reject(new Error(error));
      });

      xhr.open('POST', url);
      xhr.send(formData);
    });
  }, [options]);

  const reset = useCallback(() => {
    setUploadState({
      isUploading: false,
      progress: 0,
      error: null,
      response: null,
    });
  }, []);

  return {
    uploadFile,
    reset,
    ...uploadState,
  };
};
