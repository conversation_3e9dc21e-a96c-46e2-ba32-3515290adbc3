import React, { useRef } from 'react';
import {
  Box,
  Button,
  Paper,
  Typography,
  IconButton,
} from '@mui/material';
import {
  CloudUpload as UploadIcon,
  AttachFile as AttachIcon,
} from '@mui/icons-material';
import { useUpload, UploadOptions } from './UploadContext';

export interface GlobalFileUploadProps extends Omit<UploadOptions, 'onComplete' | 'onError'> {
  /** Разрешенные типы файлов */
  accept?: string;
  /** Множественная загрузка */
  multiple?: boolean;
  /** Отключить компонент */
  disabled?: boolean;
  /** Текст кнопки */
  buttonText?: string;
  /** Вариант отображения */
  variant?: 'button' | 'dropzone' | 'icon';
  /** Размер кнопки */
  size?: 'small' | 'medium' | 'large';
  /** Callback по окончанию загрузки */
  onComplete?: (response: any, file: File) => void;
  /** Callback при ошибке */
  onError?: (error: any, file: File) => void;
}

export const GlobalFileUpload: React.FC<GlobalFileUploadProps> = ({
  url,
  additionalParams,
  showSuccessMessage = true,
  showErrorMessage = true,
  successMessage,
  maxSize,
  accept,
  multiple = false,
  disabled = false,
  buttonText = 'Upload File',
  variant = 'button',
  size = 'medium',
  onComplete,
  onError,
}) => {
  const fileInputRef = useRef<HTMLInputElement>(null);
  const { uploadFile } = useUpload();

  const handleFileSelect = async (files: FileList | null) => {
    if (!files) return;

    const fileArray = Array.from(files);
    
    // Загружаем файлы последовательно или параллельно
    for (const file of fileArray) {
      try {
        const response = await uploadFile(file, {
          url,
          additionalParams,
          showSuccessMessage,
          showErrorMessage,
          successMessage,
          maxSize,
          onComplete,
          onError,
        });
      } catch (error) {
        // Ошибка уже обработана в контексте
      }
    }
  };

  const handleButtonClick = () => {
    fileInputRef.current?.click();
  };

  const handleDragOver = (e: React.DragEvent) => {
    e.preventDefault();
  };

  const handleDrop = (e: React.DragEvent) => {
    e.preventDefault();
    handleFileSelect(e.dataTransfer.files);
  };

  if (variant === 'icon') {
    return (
      <Box>
        <input
          ref={fileInputRef}
          type="file"
          accept={accept}
          multiple={multiple}
          disabled={disabled}
          style={{ display: 'none' }}
          onChange={(e) => handleFileSelect(e.target.files)}
        />
        
        <IconButton
          onClick={handleButtonClick}
          disabled={disabled}
          size={size}
          color="primary"
        >
          <AttachIcon />
        </IconButton>
      </Box>
    );
  }

  if (variant === 'dropzone') {
    return (
      <Box>
        <input
          ref={fileInputRef}
          type="file"
          accept={accept}
          multiple={multiple}
          disabled={disabled}
          style={{ display: 'none' }}
          onChange={(e) => handleFileSelect(e.target.files)}
        />
        
        <Paper
          sx={{
            p: 3,
            border: '2px dashed',
            borderColor: 'grey.300',
            textAlign: 'center',
            cursor: disabled ? 'not-allowed' : 'pointer',
            transition: 'all 0.2s ease',
            '&:hover': {
              borderColor: disabled ? 'grey.300' : 'primary.main',
              backgroundColor: disabled ? 'inherit' : 'action.hover',
            },
          }}
          onDragOver={handleDragOver}
          onDrop={handleDrop}
          onClick={!disabled ? handleButtonClick : undefined}
        >
          <UploadIcon sx={{ fontSize: 48, color: 'grey.400', mb: 2 }} />
          <Typography variant="h6" gutterBottom>
            {buttonText}
          </Typography>
          <Typography variant="body2" color="text.secondary">
            Drag and drop files here or click to select
          </Typography>
        </Paper>
      </Box>
    );
  }

  return (
    <Box>
      <input
        ref={fileInputRef}
        type="file"
        accept={accept}
        multiple={multiple}
        disabled={disabled}
        style={{ display: 'none' }}
        onChange={(e) => handleFileSelect(e.target.files)}
      />
      
      <Button
        variant="contained"
        size={size}
        disabled={disabled}
        onClick={handleButtonClick}
        startIcon={<UploadIcon />}
      >
        {buttonText}
      </Button>
    </Box>
  );
};
