import React from 'react';
import {
  Box,
  Typography,
  LinearProgress,
  Chip,
  IconButton,
  List,
  ListItem,
  ListItemText,
  ListItemSecondaryAction,
  Paper,
  Collapse,
} from '@mui/material';
import {
  ExpandMore as ExpandIcon,
  ExpandLess as CollapseIcon,
  Clear as ClearIcon,
  CloudDone as DoneIcon,
  Error as ErrorIcon,
  CloudUpload as UploadIcon,
} from '@mui/icons-material';
import { useGlobalUpload } from './useGlobalUpload';

export interface UploadStatusProps {
  /** Показывать только активные загрузки */
  activeOnly?: boolean;
  /** Максимальное количество отображаемых задач */
  maxItems?: number;
  /** Компактный режим */
  compact?: boolean;
  /** Разворачиваемый список */
  collapsible?: boolean;
}

export const UploadStatus: React.FC<UploadStatusProps> = ({
  activeOnly = false,
  maxItems = 5,
  compact = false,
  collapsible = false,
}) => {
  const { uploadTasks, clearCompletedTasks, getUploadStats, getTotalProgress } = useGlobalUpload();
  const [expanded, setExpanded] = React.useState(!collapsible);

  const stats = getUploadStats();
  const totalProgress = getTotalProgress();

  const filteredTasks = activeOnly 
    ? uploadTasks.filter(task => task.status === 'uploading')
    : uploadTasks.slice(0, maxItems);

  if (uploadTasks.length === 0) {
    return null;
  }

  const getStatusIcon = (status: string) => {
    switch (status) {
      case 'uploading':
        return <UploadIcon color="primary" />;
      case 'completed':
        return <DoneIcon color="success" />;
      case 'error':
        return <ErrorIcon color="error" />;
      default:
        return <UploadIcon />;
    }
  };

  const getStatusColor = (status: string) => {
    switch (status) {
      case 'uploading':
        return 'primary';
      case 'completed':
        return 'success';
      case 'error':
        return 'error';
      default:
        return 'default';
    }
  };

  if (compact) {
    return (
      <Box sx={{ minWidth: 200 }}>
        {stats.isUploading && (
          <Box sx={{ mb: 1 }}>
            <Typography variant="caption" color="text.secondary">
              Uploading {stats.uploading} file(s)...
            </Typography>
            <LinearProgress 
              variant="determinate" 
              value={totalProgress} 
              size="small"
            />
          </Box>
        )}
        
        <Box sx={{ display: 'flex', gap: 1, flexWrap: 'wrap' }}>
          {stats.uploading > 0 && (
            <Chip 
              label={`${stats.uploading} uploading`} 
              color="primary" 
              size="small" 
            />
          )}
          {stats.completed > 0 && (
            <Chip 
              label={`${stats.completed} completed`} 
              color="success" 
              size="small" 
            />
          )}
          {stats.failed > 0 && (
            <Chip 
              label={`${stats.failed} failed`} 
              color="error" 
              size="small" 
            />
          )}
        </Box>
      </Box>
    );
  }

  return (
    <Paper sx={{ p: 2, maxWidth: 400 }}>
      <Box sx={{ display: 'flex', alignItems: 'center', justifyContent: 'space-between', mb: 1 }}>
        <Typography variant="h6">
          Upload Status
        </Typography>
        
        <Box sx={{ display: 'flex', alignItems: 'center', gap: 1 }}>
          {stats.completed > 0 && (
            <IconButton size="small" onClick={clearCompletedTasks}>
              <ClearIcon />
            </IconButton>
          )}
          
          {collapsible && (
            <IconButton size="small" onClick={() => setExpanded(!expanded)}>
              {expanded ? <CollapseIcon /> : <ExpandIcon />}
            </IconButton>
          )}
        </Box>
      </Box>

      {stats.isUploading && (
        <Box sx={{ mb: 2 }}>
          <Typography variant="body2" color="text.secondary" sx={{ mb: 1 }}>
            Overall Progress: {totalProgress}%
          </Typography>
          <LinearProgress variant="determinate" value={totalProgress} />
        </Box>
      )}

      <Box sx={{ display: 'flex', gap: 1, mb: 2, flexWrap: 'wrap' }}>
        <Chip 
          label={`Total: ${stats.total}`} 
          variant="outlined" 
          size="small" 
        />
        {stats.uploading > 0 && (
          <Chip 
            label={`Uploading: ${stats.uploading}`} 
            color="primary" 
            size="small" 
          />
        )}
        {stats.completed > 0 && (
          <Chip 
            label={`Completed: ${stats.completed}`} 
            color="success" 
            size="small" 
          />
        )}
        {stats.failed > 0 && (
          <Chip 
            label={`Failed: ${stats.failed}`} 
            color="error" 
            size="small" 
          />
        )}
      </Box>

      <Collapse in={expanded}>
        <List dense>
          {filteredTasks.map((task) => (
            <ListItem key={task.id}>
              <ListItemText
                primary={task.fileName}
                secondary={
                  <Box>
                    {task.status === 'uploading' && (
                      <LinearProgress 
                        variant="determinate" 
                        value={task.progress} 
                        sx={{ mt: 0.5, mb: 0.5 }}
                      />
                    )}
                    <Box sx={{ display: 'flex', alignItems: 'center', gap: 1 }}>
                      <Chip
                        label={task.status}
                        color={getStatusColor(task.status) as any}
                        size="small"
                      />
                      {task.status === 'uploading' && (
                        <Typography variant="caption">
                          {task.progress}%
                        </Typography>
                      )}
                      {task.error && (
                        <Typography variant="caption" color="error">
                          {task.error}
                        </Typography>
                      )}
                    </Box>
                  </Box>
                }
              />
              <ListItemSecondaryAction>
                {getStatusIcon(task.status)}
              </ListItemSecondaryAction>
            </ListItem>
          ))}
        </List>
        
        {uploadTasks.length > maxItems && (
          <Typography variant="caption" color="text.secondary" sx={{ mt: 1, display: 'block' }}>
            ... and {uploadTasks.length - maxItems} more
          </Typography>
        )}
      </Collapse>
    </Paper>
  );
};
