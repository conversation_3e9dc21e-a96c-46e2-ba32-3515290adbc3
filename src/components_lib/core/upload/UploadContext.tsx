import React, { createContext, useContext, useState, useCallback, ReactNode } from 'react';
import { Snackbar, Alert, LinearProgress, Box, Typography, IconButton } from '@mui/material';
import { Close as CloseIcon } from '@mui/icons-material';

export interface UploadTask {
  id: string;
  fileName: string;
  progress: number;
  status: 'uploading' | 'completed' | 'error';
  error?: string;
  response?: any;
}

export interface UploadOptions {
  url: string;
  additionalParams?: Record<string, any>;
  onComplete?: (response: any, file: File) => void;
  onError?: (error: any, file: File) => void;
  showSuccessMessage?: boolean;
  showErrorMessage?: boolean;
  successMessage?: string;
  maxSize?: number;
}

interface UploadContextType {
  uploadFile: (file: File, options: UploadOptions) => Promise<any>;
  uploadTasks: UploadTask[];
  clearCompletedTasks: () => void;
}

const UploadContext = createContext<UploadContextType | undefined>(undefined);

export const useUpload = () => {
  const context = useContext(UploadContext);
  if (!context) {
    throw new Error('useUpload must be used within UploadProvider');
  }
  return context;
};

interface UploadProviderProps {
  children: ReactNode;
}

export const UploadProvider: React.FC<UploadProviderProps> = ({ children }) => {
  const [uploadTasks, setUploadTasks] = useState<UploadTask[]>([]);
  const [snackbarOpen, setSnackbarOpen] = useState(false);
  const [snackbarMessage, setSnackbarMessage] = useState('');
  const [snackbarSeverity, setSnackbarSeverity] = useState<'success' | 'error' | 'info'>('info');

  const generateId = () => Math.random().toString(36).substr(2, 9);

  const formatFileSize = (bytes: number): string => {
    if (bytes === 0) return '0 Bytes';
    const k = 1024;
    const sizes = ['Bytes', 'KB', 'MB', 'GB'];
    const i = Math.floor(Math.log(bytes) / Math.log(k));
    return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i];
  };

  const showSnackbar = (message: string, severity: 'success' | 'error' | 'info') => {
    setSnackbarMessage(message);
    setSnackbarSeverity(severity);
    setSnackbarOpen(true);
  };

  const uploadFile = useCallback(async (file: File, options: UploadOptions): Promise<any> => {
    const {
      url,
      additionalParams = {},
      onComplete,
      onError,
      showSuccessMessage = true,
      showErrorMessage = true,
      successMessage,
      maxSize
    } = options;

    const taskId = generateId();

    // Валидация размера файла
    if (maxSize && file.size > maxSize) {
      const error = `File size exceeds ${formatFileSize(maxSize)}`;
      if (showErrorMessage) {
        showSnackbar(`${file.name}: ${error}`, 'error');
      }
      onError?.(new Error(error), file);
      throw new Error(error);
    }

    // Создаем задачу загрузки
    const newTask: UploadTask = {
      id: taskId,
      fileName: file.name,
      progress: 0,
      status: 'uploading',
    };

    setUploadTasks(prev => [...prev, newTask]);

    return new Promise((resolve, reject) => {
      const formData = new FormData();
      formData.append('file', file);
      
      // Добавляем дополнительные параметры
      Object.entries(additionalParams).forEach(([key, value]) => {
        formData.append(key, String(value));
      });

      const xhr = new XMLHttpRequest();

      // Обработчик прогресса
      xhr.upload.addEventListener('progress', (event) => {
        if (event.lengthComputable) {
          const progress = Math.round((event.loaded / event.total) * 100);
          setUploadTasks(prev => prev.map(task => 
            task.id === taskId ? { ...task, progress } : task
          ));
        }
      });

      // Обработчик завершения
      xhr.addEventListener('load', () => {
        if (xhr.status >= 200 && xhr.status < 300) {
          let response;
          try {
            response = JSON.parse(xhr.responseText);
          } catch {
            response = xhr.responseText;
          }

          setUploadTasks(prev => prev.map(task => 
            task.id === taskId 
              ? { ...task, status: 'completed', progress: 100, response }
              : task
          ));

          if (showSuccessMessage) {
            const message = successMessage || `${file.name} uploaded successfully`;
            showSnackbar(message, 'success');
          }

          onComplete?.(response, file);
          resolve(response);

          // Автоматически удаляем завершенную задачу через 3 секунды
          setTimeout(() => {
            setUploadTasks(prev => prev.filter(task => task.id !== taskId));
          }, 3000);

        } else {
          const error = `Upload failed: ${xhr.status} ${xhr.statusText}`;
          setUploadTasks(prev => prev.map(task => 
            task.id === taskId 
              ? { ...task, status: 'error', error }
              : task
          ));

          if (showErrorMessage) {
            showSnackbar(`${file.name}: ${error}`, 'error');
          }

          onError?.(new Error(error), file);
          reject(new Error(error));
        }
      });

      // Обработчик ошибки
      xhr.addEventListener('error', () => {
        const error = 'Network error occurred';
        setUploadTasks(prev => prev.map(task => 
          task.id === taskId 
            ? { ...task, status: 'error', error }
            : task
        ));

        if (showErrorMessage) {
          showSnackbar(`${file.name}: ${error}`, 'error');
        }

        onError?.(new Error(error), file);
        reject(new Error(error));
      });
      const baseUrl = import.meta.env.VITE_API_BASE_URL || '';
      xhr.open('POST', baseUrl + url);
      xhr.send(formData);
    });
  }, []);

  const clearCompletedTasks = useCallback(() => {
    setUploadTasks(prev => prev.filter(task => task.status === 'uploading'));
  }, []);

  // Получаем активную задачу для отображения в снэкбаре
  const activeTask = uploadTasks.find(task => task.status === 'uploading');

  return (
    <UploadContext.Provider value={{ uploadFile, uploadTasks, clearCompletedTasks }}>
      {children}
      
      {/* Snackbar для прогресса загрузки */}
      {activeTask && (
        <Snackbar
          open={true}
          anchorOrigin={{ vertical: 'bottom', horizontal: 'right' }}
          sx={{ mb: 2 }}
        >
          <Alert
            severity="info"
            sx={{ 
              width: '100%', 
              minWidth: 300,
              alignItems: 'flex-start'
            }}
          >
            <Box sx={{ width: '100%' }}>
              <Typography variant="body2" sx={{ mb: 1 }}>
                Uploading: {activeTask.fileName}
              </Typography>
              <LinearProgress 
                variant="determinate" 
                value={activeTask.progress} 
                sx={{ mb: 1 }}
              />
              <Typography variant="caption" color="text.secondary">
                {activeTask.progress}%
              </Typography>
            </Box>
          </Alert>
        </Snackbar>
      )}

      {/* Snackbar для уведомлений */}
      <Snackbar
        open={snackbarOpen}
        autoHideDuration={4000}
        onClose={() => setSnackbarOpen(false)}
        anchorOrigin={{ vertical: 'bottom', horizontal: 'left' }}
        action={
          <IconButton
            size="small"
            aria-label="close"
            color="inherit"
            onClick={() => setSnackbarOpen(false)}
          >
            <CloseIcon fontSize="small" />
          </IconButton>
        }
      >
        <Alert 
          onClose={() => setSnackbarOpen(false)} 
          severity={snackbarSeverity}
          sx={{ width: '100%' }}
        >
          {snackbarMessage}
        </Alert>
      </Snackbar>
    </UploadContext.Provider>
  );
};
