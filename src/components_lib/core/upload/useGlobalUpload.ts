import { useUpload, UploadOptions } from './UploadContext';

export interface GlobalUploadOptions extends UploadOptions {
  /** Автоматически показывать уведомления */
  showNotifications?: boolean;
}

export const useGlobalUpload = () => {
  const { uploadFile: contextUploadFile, uploadTasks, clearCompletedTasks } = useUpload();

  const uploadFile = async (file: File, options: GlobalUploadOptions) => {
    const {
      showNotifications = true,
      showSuccessMessage = showNotifications,
      showErrorMessage = showNotifications,
      ...restOptions
    } = options;

    return contextUploadFile(file, {
      ...restOptions,
      showSuccessMessage,
      showErrorMessage,
    });
  };

  const uploadFiles = async (files: File[], options: GlobalUploadOptions) => {
    const results = [];
    
    for (const file of files) {
      try {
        const result = await uploadFile(file, options);
        results.push({ file, result, success: true });
      } catch (error) {
        results.push({ file, error, success: false });
      }
    }
    
    return results;
  };

  const uploadFilesParallel = async (files: File[], options: GlobalUploadOptions) => {
    const promises = files.map(async (file) => {
      try {
        const result = await uploadFile(file, options);
        return { file, result, success: true };
      } catch (error) {
        return { file, error, success: false };
      }
    });

    return Promise.all(promises);
  };

  // Получение статистики загрузок
  const getUploadStats = () => {
    const total = uploadTasks.length;
    const uploading = uploadTasks.filter(task => task.status === 'uploading').length;
    const completed = uploadTasks.filter(task => task.status === 'completed').length;
    const failed = uploadTasks.filter(task => task.status === 'error').length;

    return {
      total,
      uploading,
      completed,
      failed,
      isUploading: uploading > 0,
    };
  };

  // Получение прогресса всех загрузок
  const getTotalProgress = () => {
    if (uploadTasks.length === 0) return 0;
    
    const totalProgress = uploadTasks.reduce((sum, task) => sum + task.progress, 0);
    return Math.round(totalProgress / uploadTasks.length);
  };

  return {
    uploadFile,
    uploadFiles,
    uploadFilesParallel,
    uploadTasks,
    clearCompletedTasks,
    getUploadStats,
    getTotalProgress,
  };
};
