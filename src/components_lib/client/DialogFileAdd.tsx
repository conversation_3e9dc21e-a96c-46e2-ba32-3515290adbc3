import { ModelApiFile, FileModel } from "@/api/file"
import { Alert, Avatar, Box, Button, CircularProgress, CircularProgressProps, Dialog, DialogActions, DialogContent, DialogContentText, DialogProps, DialogTitle, Fab, LinearProgress, List, ListItem, ListItemAvatar, ListItemText, Stack, Typography } from "@mui/material"
import Grid from '@mui/material/Grid';
import React, { useCallback, useEffect } from "react";
import FileUploadIcon from '@mui/icons-material/FileUpload'
import AddIcon from '@mui/icons-material/Add'
import AttachFileIcon from '@mui/icons-material/AttachFile';

// import { FormGrid } from "../Clients_2/_field_item";
// import DialogDelete from "@/components/Singles/DialogDelete";
import InsertDriveFileIcon from '@mui/icons-material/InsertDriveFile';
import { v4 as uuid } from 'uuid'
import { ListFormBySchema } from "../ListFormBySchema";
import { ClientFile, clientFileCreateSchema, clientFileSchema } from "../models/client/file";
import { apiClient } from "@/utils/api";

interface ModParams {
    clientId?: string|number,
    onAction?: Function 
} 

const Fields = ModelApiFile.formConfig

export default function DialogFileAdd ({clientId, onAction = ()=>{}}: ModParams) {
    
    const api = ModelApiFile.useApiFiles(clientId ? `clients/${clientId}` : undefined)
    const fullWidth = true
    const maxWidth = 'xl'

    const [open, setOpen] = React.useState(false);
    const [uploaded, setUploaded] = React.useState(false);
    const [scroll, setScroll] = React.useState<DialogProps['scroll']>('paper');

    const [fileList, setFileList] = React.useState<Array<FileModel>>([])

    useEffect( () => {
      setFileList([])
    }, [open])

    const handleClose = () => {
      debugger
      if(uploaded) {
        onAction({action: 'add_complete'})
      }
      setOpen(false);
    };

    const upload = React.useCallback( async (event: any) => {
      let req = fileList.map( (f: FileModel) => {
        f.isLoading = true
        return apiClient.upload<ClientFile>(`/clients/${clientId}/files`, f,  (ev: any) => {
        // return api.upload(f.file, {doc_type: f.doc_type, description: f.description }, (ev: any) => {
          const progress = ev.loaded / ev.total * 100;
          f.progress = Math.round(progress)
          setFileList((old) => {return [...old]})
        })
        .then((r) => {
          debugger
          deleteItem(f, r)
          return r
        })
        .finally(() => { 
          f.isLoading = false
          // setFileList((old) => {return [...old]})
        })
        .catch((err)=> { 
          f.error = err.response.data.msg 
          setFileList((old) => {return [...old]})
          console.log(fileList)
          return err
        })
      })
      
      return await Promise.all(req)
        .then((fileInfoResults) => {
          setUploaded(true)
        })
        .catch((error) => {
          debugger
          console.error('Error reading files:', error)
          // return []
        })
        .finally(() => {
          // onAction({action: 'add_complete'})
          if(fileList.length == 0) {
            debugger
            handleClose()
          }
        })
    }, [fileList])

    const selectFile = (event: any) => {
      let _files = Array.from(event.target.files)
      const newFiles = _files.map((file: any) => {
          return {
            ...ModelApiFile.newElement,
            name: file.name,
            file_type: file.type,
            doc_type: null,
            file,
            uid: uuid()
          }
      })
      setFileList((old) => { return [...old, ...newFiles]} )
    }

    const deleteItem = React.useCallback( (item: any, r?: any) => () => {
      debugger  
      setFileList( fileList.filter(f => f.uid !== item.uid) );          
    }, [fileList, setFileList])

    const handleListChange = (list: FileModel[]) => {
      debugger
      setFileList(list)
      // const newData = {...data, [param_name]: list };
      // onChange(newData);
    };

    return (<>
      <Fab
          color="primary"
          // sx={{ position: 'fixed', bottom: 16, right: 16 }}
          onClick={() => setOpen(true)}
        >
          <AddIcon />
      </Fab>
      {/* <Button
          variant="contained"
          startIcon={<AddIcon />}
          color="secondary"
          size="small"
          onClick={() => setOpen(true)}
          >
          Add
      </Button> */}
      { true && 
        <Dialog
          open={open}
          // onClose={handleClose}
          scroll={scroll}
          fullWidth={fullWidth}
          maxWidth={maxWidth}
          aria-labelledby="scroll-dialog-title"
          aria-describedby="scroll-dialog-description"
        >
          <DialogTitle id="scroll-dialog-title">
            <Stack
              direction="row"
              spacing={2}
              sx={{
                justifyContent: "space-between",
                alignItems: "center",
              }}
            >
              <span>Add Files</span>
              <Button
                component="label"
                variant="contained"
                startIcon={<AttachFileIcon />}>
                Select files
                <input type="file" name="new-files" onChange={selectFile} hidden multiple />
              </Button>
            </Stack>
                  
          </DialogTitle>
          <DialogContent dividers={scroll === 'paper'}>
            <Stack spacing={3}>
              <ListFormBySchema
                      schema={clientFileCreateSchema}
                      items={fileList}
                      onChange={handleListChange}
                      defaultItemValues={{}}
                      addButtonText={null}
                    />
            </Stack>
          </DialogContent>
          <DialogActions>
            <Button onClick={handleClose}>Cancel</Button>
            <Button onClick={upload} startIcon={<FileUploadIcon />}>Upload</Button>
          </DialogActions>
        </Dialog>
      }
    </>)
}


function CircularProgressWithLabel(
  props: CircularProgressProps & { value: number },
) {
  return (
    <Box sx={{ position: 'relative', display: 'inline-flex' }}>
      <CircularProgress variant="determinate" {...props} />
      <Box
        sx={{
          top: 0,
          left: 0,
          bottom: 0,
          right: 0,
          position: 'absolute',
          display: 'flex',
          alignItems: 'center',
          justifyContent: 'center',
        }}
      >
        <Typography
          variant="caption"
          component="div"
          sx={{ color: 'text.secondary' }}
        >{`${Math.round(props.value)}%`}</Typography>
      </Box>
    </Box>
  );
}