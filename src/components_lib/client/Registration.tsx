import React from "react";
import { FormBySchema } from "../FormBySchema";
import { clientRegistrationMainSchema } from "../models/client/registration";
import { Client, ClientTabProps } from "../models/client";
import { ClientRegistrationItem, clientRegistrationItemSchema } from "../models/client/registration_item";
import { ListFormBySchema } from "../ListFormBySchema";
import { v4 as uuidv4 } from 'uuid';  
  


export const ClientRegistration = <T extends Client>({
  data,
  baseData,
  onChange,
}: ClientTabProps<T>) => {

  React.useEffect(() => {
    if (!data.registrations) data.registrations = []
    if (!data.registrations.length) {
      // Initialize with one registration item that has primary=true
      const newData = {...data};
      // newData.registrations = [{ is_primary: true, uid: uuidv4() }];
      onChange(newData);
    }
  }, []);

  React.useEffect(() => {
    console.info('ClientRegistration', data)
  }, []);

  const handleListChange = (registrations: ClientRegistrationItem[]) => {
    const newData = {...data, registrations};
    onChange(newData);
  };

  return (
    <div>
      <FormBySchema
        schema={clientRegistrationMainSchema}
        initialValues={data}
        baseValues={baseData}
        onChange={onChange}
        actions={false}
      />

      <ListFormBySchema
        schema={clientRegistrationItemSchema}
        baseItems={baseData?.registrations || []}
        items={data.registrations || []}
        onChange={handleListChange}
        defaultItemValues={{}}
        addButtonText="Add Registration"
        title="Registrations"
      />
    </div>
  );
}