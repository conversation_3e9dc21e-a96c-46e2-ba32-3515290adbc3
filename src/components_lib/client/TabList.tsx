import React, { useState } from "react";
import { ListFormBySchema, ListItem } from "../ListFormBySchema";
import { Client, ClientTabListProps, ClientTabProps } from "../models/client";
  

export const ClientTabList = <T extends Client & Record<string, any>, C extends ListItem>({
  data,
  baseData,
  itemSchema,  
  itemModel,
  itemDefaultValues = {},
  itemsEl = 'test',
  onChange,
  onChangeList,
  param_name
}: ClientTabListProps<T>) => {

  const [list, setList] = useState<C[]>(data[param_name] || [])
  
  // React.useEffect(() => {
  //   if (!data[param_name]) data[param_name] = []
  // }, []);

  React.useEffect(() => {
    console.info(param_name, data)
  }, []);

  const handleListChange = (list: C[]) => {
    setList(list);
    Object.assign(data, {[param_name] : list})
    onChangeList(data);
  };

  return (
    <div>
      <ListFormBySchema
        schema={itemSchema}
        items={list}
        baseItems={baseData ? baseData[param_name] : []}
        onChange={handleListChange}
        defaultItemValues={itemDefaultValues}
        addButtonText="Add"
        // title="Addresses"
      />
    </div>
  );
}
















//   // React.useEffect(() => {
//   //   if (!data[itemsEl]) data[itemsEl] = []
//   // }, []);

//   React.useEffect(() => {
//     console.info('ClientAddressTab', data)
//   }, []);

//   const handleListChange = (items: any[]) => {
//     const newData = {...data, [itemsEl]: items};
//     onChange(newData);
//   };

//   return (
//     <div>
//       <ListFormBySchema
//         schema={itemSchema || {}}
//         items={data[itemsEl] || []}
//         baseItems={baseData?.[itemsEl] || []}
//         onChange={handleListChange}
//         defaultItemValues={itemDefaultValues}
//         addButtonText="Add Address"
//         title="Addresses"
//       />
//     </div>
//   );
// }