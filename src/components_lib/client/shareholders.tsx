import React from "react";
import { Client, ClientTabProps } from "../models/client";
import { ListFormBySchema } from "../ListFormBySchema";
import { ClientShareholders, clientShareholdersSchema } from "../models/client/shareholders";
  

export const ClientShareholdersTab = <T extends Client>({
  data,
  baseData,
  onChange,
}: ClientTabProps<T>) => {

  const param_name = 'shareholders'

  React.useEffect(() => {
    if (!data[param_name]) data[param_name] = []
  }, []);

  React.useEffect(() => {
    console.info(param_name, data)
  }, []);

  const handleListChange = (list: ClientShareholders[]) => {
    const newData = {...data, [param_name]: list };
    onChange(newData);
  };

  return (
    <div>
      <ListFormBySchema
        schema={clientShareholdersSchema}
        items={data[param_name] || []}
        baseItems={baseData ? baseData[param_name] : []}
        onChange={handleListChange}
        defaultItemValues={{}}
        addButtonText="Add"
        // title="Addresses"
      />
    </div>
  );
}