import React from "react";
import { Client, ClientTabProps } from "../models/client";
import { ListFormBySchema } from "../ListFormBySchema";
import { ClientPaymentCards, clientPaymentCardsSchema } from "../models/client/payment_card";
  

export const ClientPaymentCardsTab = <T extends Client>({
  data,
  baseData,
  onChange,
}: ClientTabProps<T>) => {

  const param_name = 'payment_cards'

  React.useEffect(() => {
    if (!data[param_name]) data[param_name] = []
  }, []);

  React.useEffect(() => {
    console.info(param_name, data)
  }, []);

  const handleListChange = (list: ClientPaymentCards[]) => {
    const newData = {...data, [param_name]: list };
    onChange(newData);
  };

  return (
    <div>
      <ListFormBySchema
        schema={clientPaymentCardsSchema}
        items={data[param_name] || []}
        baseItems={baseData ? baseData[param_name] : []}
        onChange={handleListChange}
        defaultItemValues={{}}
        addButtonText="Add"
        // title="Addresses"
      />
    </div>
  );
}