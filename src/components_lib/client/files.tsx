import React from 'react';
import { Box, Typography, Fab, IconButton } from '@mui/material';
import { TableByApi } from '@/components_lib/TableByApi';
import { Manager, managerSchema } from '@/components_lib/models/manager';
import { apiClient } from '@/utils/api';
import { SchemaData } from '@/components_lib/core/schema/Schema';
import { Client, ClientTabProps } from '../models/client';
import { ClientFile, clientFileSchema } from '../models/client/file';
import DialogFileAdd from './DialogFileAdd';
// import { SchemaData } from '@/components_lib/core/schemaDate';

// export const ClientFileTab: React.FC = () => {

export const ClientFileTab = <T extends Client>({
    data,
    baseData,
    onChange,
    // param_name? = 'contacts'
}: ClientTabProps<T>) => {
  
    const [addOpen, setAddOpen] = React.useState<boolean>(false)
    const apiUrl = `/clients/${data.id}/files`
  

  React.useEffect(() => {
    // getRoles()
  }, [])

  const onCreateClick = () => {
    debugger
  }

  return (
    <Box>

      <Box>
        <TableByApi<ClientFile>
          schema={clientFileSchema}
          apiUrl={apiUrl}
          params={{
            order: 'desc',
          }}
          onCreateClick= {onCreateClick}
          addButton={<DialogFileAdd clientId={data.id}></DialogFileAdd>}
        />
        {/* <pre>{JSON.stringify(schema, null, 2)}</pre> */}
      </Box>
      
    </Box>
  );
}; 
