import React from "react";
import { ListFormBySchema } from "../ListFormBySchema";
import { v4 as uuidv4 } from 'uuid';  
import { Client, ClientTabProps } from "../models/client";
import { clientAddressSchema, ClientAddress } from "../models/client/address";
  

export const ClientAddressTab = <T extends Client>({
  data,
  baseData,
  onChange,
}: ClientTabProps<T>) => {

  React.useEffect(() => {
    if (!data.addresses) data.addresses = []
    if (!data.addresses.length) {
      // Initialize with one registration item that has primary=true
      // const newData = {...data};
      // newData.addresses = [{ uid: uuidv4() }];
      // onChange(newData);
    }
  }, []);

  React.useEffect(() => {
    console.info('ClientAddressTab', data)
  }, []);

  const handleListChange = (addresses: ClientAddress[]) => {
    const newData = {...data, addresses};
    onChange(newData);
  };

  return (
    <div>
      <ListFormBySchema
        schema={clientAddressSchema}
        items={data.addresses || []}
        baseItems={baseData?.addresses || []}
        onChange={handleListChange}
        defaultItemValues={{}}
        addButtonText="Add Address"
        // title="Addresses"
      />
    </div>
  );
}