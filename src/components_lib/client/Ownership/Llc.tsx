import { ListFormBySchema } from "@/components_lib/ListFormBySchema";
import { Client, ClientTabProps } from "@/components_lib/models/client";
import { clientLLCShareholdersSchema } from "@/components_lib/models/client/llc_shareholders";
import { Box } from "@mui/material";

export const ClientOwnershipLLC = <T extends Client>(
    {
  data,
  baseData,
  onChange,
}: ClientTabProps<T>
) => {  

    const handleListTabChange = (formData: any[], tab: string) => {
      const newData: any = {...data, [tab]: formData};
      onChange(newData);
    };

    return (<Box>
        <ListFormBySchema
        schema={clientLLCShareholdersSchema}
        items={data['llc_shareholders'] || []}
        baseItems={baseData ? baseData['llc_shareholders'] : []}
        onChange={(formData) => handleListTabChange(formData, 'llc_shareholders')}
        defaultItemValues={{}}
        addButtonText="Add"
        // title="Addresses"
        />
    </Box>)
    

}