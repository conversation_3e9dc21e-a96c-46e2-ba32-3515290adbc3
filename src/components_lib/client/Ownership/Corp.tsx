import { ListFormBySchema } from "@/components_lib/ListFormBySchema";
import { Client, ClientTabProps } from "@/components_lib/models/client";
import { clientCorpCapitalizationTableSchema } from "@/components_lib/models/client/corp_capitalization_table";
import { clientSharesSchema } from "@/components_lib/models/client/shares";
import { Box, Typography } from "@mui/material";
import React from "react";

interface ShareCount {
    issued: number;
    authorized: number;
}

export const ClientOwnershipCorp = <T extends Client>(
    {
  data,
  baseData,
  onChange,
}: ClientTabProps<T>
) => {  

    const [countShare, setCountShare] = React.useState<ShareCount>({authorized: 0, issued: 0});
    const handleListTabChange = (formData: any[], tab: string) => {
      const newData: any = {...data, [tab]: formData};
      onChange(newData);
    };

    const setCorpShare = () => {
        let authorized = 0
        let issued = 0
        clientCorpCapitalizationTableSchema.share.options = data['shares']?.map( (v: any) => { 
            authorized += Number(v.authorized_amount || 0)
            issued += Number(v.issued_amount || 0)
            // debugger
            return {value: v.id || v.uid , label: v.type} })
        if(clientCorpCapitalizationTableSchema.share.autocomplete) 
            clientCorpCapitalizationTableSchema.share.autocomplete.options =  data['shares'] 
        setCountShare({authorized, issued})
    }
    // setCorpShare()

    React.useEffect(() => {
        setCorpShare()
      }, [data['shares']]);

    return (<>
    <Box>
        <ListFormBySchema
        schema={clientSharesSchema}
        items={data['shares'] || []}
        baseItems={baseData ? baseData['shares'] : []}
        onChange={(formData) => handleListTabChange(formData, 'shares')}
        defaultItemValues={{}}
        addButtonText="Add"
        title={`Shares Authorized: ${countShare.authorized}, Issued: ${countShare.issued}`}
        />
    </Box>

    <Box>
        <ListFormBySchema
        schema={clientCorpCapitalizationTableSchema}
        items={data['corp_capitalization_table'] || []}
        baseItems={baseData ? baseData['corp_capitalization_table'] : []}
        onChange={(formData) => handleListTabChange(formData, 'corp_capitalization_table')}
        defaultItemValues={{}}
        addButtonText="Add"
        title="Corp Capitalization Table "
        />
    </Box>
    </>
    )

}