import React from "react";
// import { ListFormBySchema } from "../ListFormBySchema";
// import { v4 as uuidv4 } from 'uuid';  
import { Client, ClientTabProps } from "../models/client";
// import { clientAddressSchema, ClientAddress } from "../models/client/address";
import { ClientContact, clientContactSchema } from "../models/client/contat";
import { ListFormBySchema } from "../ListFormBySchema";
import { ClientService, clientServiceSchema } from "../models/client/service";
import { ClientTask, clientTaskSchema } from "../models/client/task";
import { Box } from "@mui/material";
import { TableByApi } from "../TableByApi";
  

export const ClientTasksTab = <T extends Client>({
  data,
  baseData,
  onChange,
  // param_name? = 'contacts'
}: ClientTabProps<T>) => {

  // const param_name = 'tasks'

  // React.useEffect(() => {
  //   if (!data[param_name]) data[param_name] = []
  // }, []);

  // React.useEffect(() => {
  //   console.info(param_name, data)
  // }, []);

  // const handleListChange = (list: ClientTask[]) => {
  //   const newData = {...data, [param_name]: list };
  //   onChange(newData);
  // };

  return (

    <Box>
        <TableByApi<ClientTask>
          schema={clientTaskSchema}
          apiUrl={`/clients/${data.id}/tasks`}
          params={{
            order: 'desc',
          }}
        />
    </Box>
    // <div>
    //   <ListFormBySchema
    //     schema={clientTaskSchema}
    //     items={data[param_name] || []}
    //     baseItems={baseData ? baseData[param_name] : []}
    //     onChange={handleListChange}
    //     defaultItemValues={{}}
    //     addButtonText="Add"
    //     // title="Addresses"
    //   />
    // </div>
  );
}