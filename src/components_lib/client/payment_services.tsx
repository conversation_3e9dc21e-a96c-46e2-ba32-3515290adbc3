import React from "react";
import { Client, ClientTabProps } from "../models/client";
import { ListFormBySchema } from "../ListFormBySchema";
import { ClientPaymentCards, clientPaymentCardsSchema } from "../models/client/payment_card";
import { ClientPaymentServices, clientPaymentServicesSchema } from "../models/client/payment_services";
  

export const ClientPaymentSystemTab = <T extends Client>({
  data,
  baseData,
  onChange,
}: ClientTabProps<T>) => {

  const param_name = 'payment_services'

  React.useEffect(() => {
    if (!data[param_name]) data[param_name] = []
  }, []);

  React.useEffect(() => {
    console.info(param_name, data)
  }, []);

  const handleListChange = (list: ClientPaymentServices[]) => {
    const newData = {...data, [param_name]: list };
    onChange(newData);
  };

  return (
    <div>
      <ListFormBySchema
        schema={clientPaymentServicesSchema}
        items={data[param_name] || []}
        baseItems={baseData ? baseData[param_name] : []}
        onChange={handleListChange}
        defaultItemValues={{}}
        addButtonText="Add"
        // title="Addresses"
      />
    </div>
  );
}