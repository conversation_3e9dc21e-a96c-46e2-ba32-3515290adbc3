import { Box, Tabs } from "@mui/material";
import { ClientMain } from "./main";
import { Client } from "../models/client";
import { Tab } from "material-ui";
import React from "react";

const newTabs: any[] =[
  {name: 'summary', display_name: 'Summary (Legal info)'},
  {name: 'finances', display_name: 'Finances'},
  {name: 'files', display_name: 'Files'},
  {display_name : "Services", name: "services"},
  {display_name: "Tasks", name: "tasks"},
  {display_name: "Tax Reporting", name: "tax_reporting"}
]

interface TabPanelProps {
  children?: React.ReactNode;
  dir?: string;
  index: string | number;
  value: string | number;
}


function TabPanel(props: TabPanelProps) {
  const { children, value, index, ...other } = props;

  return (
    <div
      role="tabpanel"
      hidden={value !== index}
      id={`simple-tabpanel-${index}`}
      aria-labelledby={`simple-tab-${index}`}
      {...other}
    >
      <Box sx={{ p: 3 }}>{children}</Box>
      {/* {value === index && <Box sx={{ p: 3 }}>{children}</Box>} */}
    </div>
  );
  }


export default function ClientPage ({data, baseData, onChange}: any)     {
    const [newOpenTab, setNewOpenTab] = React.useState<string |number>('summary');  
    const handleChangeNewTabs = (event: React.SyntheticEvent, newValue: number) => {
        setNewOpenTab(newValue);
    };
    return (<></>)
    // return (
    //     <>
    //         <Box>
    //             <ClientMain
    //             data={data || {} as Client}
    //             baseData={baseData}
    //             onChange={onChange}
    //             />
    //         </Box>
    //         <>
    //           <Box sx={{ borderBottom: 1, borderColor: 'divider', marginBottom:3, bgcolor: 'primary.main', color: 'primary.contrastText',}}>
    //             <Tabs 
    //               value={newOpenTab} 
    //               onChange={handleChangeNewTabs} 
    //               aria-label="basic tabs example"
    //               variant="scrollable"
    //               indicatorColor="secondary"
    //               textColor="inherit"
    //               scrollButtons="auto"  
    //               >
    //               {newTabs.map( (tab: Tab) => (<Tab key={tab.name} label={tab.display_name} {...a11yProps(tab.name)} />))}
    //             </Tabs>
    //           </Box>
    //           <Box sx={{ borderLeft: 3, borderColor: 'divider' }}>
    //             {newTabs.map((tab: Tab)=>(
    //               <TabPanel key={tab.name} value={newOpenTab} index={tab.name} >
    //                 { tab.name == newOpenTab &&
    //                   tabContent(tab, {data: newClient || {} as Client, onChange: onChangeClient, onChangeList: onChangeClientList, baseData: client})
    //                 }
    //               </TabPanel>))
    //             }
    //           </Box>
    //         </>


    //         {tabs && openTab && (
    //           <>
    //             <Box sx={{ borderBottom: 1, borderColor: 'divider' }}>
    //               <Tabs 
    //                 value={openTab} 
    //                 onChange={handleChangeTabs} 
    //                 aria-label="basic tabs example"
    //                 variant="scrollable"
    //                 scrollButtons="auto"  
    //                 >
    //                 {tabs.map( (tab: Tab) => (<Tab key={tab.name} label={tab.display_name} {...a11yProps(tab.name)} />))}
    //               </Tabs>
    //             </Box>
    //             <Box>
    //               {tabs.map((tab: Tab)=>(
    //                 <TabPanel key={tab.name} value={openTab} index={tab.name} >
    //                   { tab.name == openTab &&
    //                     tabContent(tab, {data: newClient || {} as Client, onChange: onChangeClient, onChangeList: onChangeClientList, baseData: client})
    //                   }
    //                 </TabPanel>))
    //               }
    //             </Box>
    //           </>
    //         )}
    //     </>
    // )
}