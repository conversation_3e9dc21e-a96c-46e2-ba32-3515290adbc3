import React from "react";
import { ListFormBySchema } from "../ListFormBySchema";
import { Client, ClientTabProps } from "../models/client";
import { clientBankAccountSchema, ClientBankAccount } from "../models/client/bank_account";
import { Box, Tab, Tabs } from "@mui/material";
import { clientPaymentCardsSchema } from "../models/client/payment_card";
import { clientPaymentServicesSchema } from "../models/client/payment_services";
import { clientAuthorizedSignersSchema } from "../models/client/authorized_signers";
import { FormBySchema } from "../FormBySchema";
import { clientAccounting } from "../models/client/accounting";
  
interface TabPanelProps {
  children?: React.ReactNode;
  dir?: string;
  index: string | number;
  value: string | number;
}


function TabPanel(props: TabPanelProps) {
  const { children, value, index, ...other } = props;

  return (
    <div
      role="tabpanel"
      hidden={value !== index}
      id={`simple-tabpanel-${index}`}
      aria-labelledby={`simple-tab-${index}`}
      {...other}
    >
      <Box sx={{ p: 3 }}>{children}</Box>
      {/* {value === index && <Box sx={{ p: 3 }}>{children}</Box>} */}
    </div>
  );
}

export const ClientFinancesTab = <T extends Client>({
  data,
  baseData,
  onChange,
}: ClientTabProps<T>) => {

  const [openTab, setOpenTab] = React.useState<string |number>('accounting');
  const handleChangeTabs = (event: React.SyntheticEvent, newValue: number) => {
    setOpenTab(newValue);
  };
  function a11yProps(name: string) {
      return {
        id: `client-tab-${name}`,
        'aria-controls': `clien-tabpanel-${name}`,
        value: name
      };
    }

  React.useEffect(() => {
    // if (!data.bank_accounts) data.bank_accounts = []
  }, []);

  
  const handleListChange = (bank_accounts: ClientBankAccount[]) => {
    const newData = {...data, bank_accounts};
    onChange(newData);
  };

  const handleListTabChange = (formData: any[], tab: string) => {
      const newData: any = {...data, [tab]: formData};
      onChange(newData);
    };


  return (
    <Box>
      <Box sx={{ borderBottom: 1, borderColor: 'divider', marginTop: 3, bgcolor: 'text.secondary', color: 'background.paper', }}>
        <Tabs 
          value={openTab} 
          onChange={handleChangeTabs} 
          aria-label="basic tabs example"
          variant="scrollable"
          scrollButtons="auto"  
          textColor="inherit"
          
          >
          <Tab key='accounting' label='Accounting' {...a11yProps('accounting')} ></Tab>
          <Tab key='bank_accounts' label='Banks' {...a11yProps('bank_accounts')} ></Tab>
          <Tab key='payment_services' label='Payment services' {...a11yProps('payment_services')} ></Tab>
          {/* <Tab key='contacts' label='Contaccts' {...a11yProps('contacts')} ></Tab> */}
        </Tabs>
      </Box>
      <Box>
        
        <TabPanel key='accounting' value={openTab} index='accounting' >
          <FormBySchema
              schema={clientAccounting}
              initialValues={data}
              baseValues={baseData}
              onChange={onChange}
              actions={false}
          />
        </TabPanel>

        <TabPanel key='bank_accounts' value={openTab} index='bank_accounts' >
          <ListFormBySchema
            schema={clientBankAccountSchema}
            items={data.bank_accounts || []}
            baseItems={baseData?.bank_accounts || []}
            onChange={(formData) => handleListTabChange(formData, 'bank_accounts')}
            defaultItemValues={{}}
            addButtonText="Add" 
            title="Banks accounts"
          />   
        </TabPanel>


        <TabPanel key='payment_services' value={openTab} index='payment_services' >
          <ListFormBySchema
            schema={clientPaymentServicesSchema}
            items={data['payment_cards'] || []}
            baseItems={baseData ? baseData['payment_cards'] : []}
            onChange={(formData) => handleListTabChange(formData, 'payment_cards')}
            defaultItemValues={{}}
            addButtonText="Add"
            // title="Addresses"
          />
        </TabPanel>
      </Box>
      
    </Box>
  );
}