import React from "react";
// import { ListFormBySchema } from "../ListFormBySchema";
// import { v4 as uuidv4 } from 'uuid';  
import { Client, ClientTabProps } from "../models/client";
// import { clientAddressSchema, ClientAddress } from "../models/client/address";
import { ClientContact, clientContactSchema } from "../models/client/contat";
import { ListFormBySchema } from "../ListFormBySchema";
import { ClientTaxReporting, clientTaxReportingSchema } from "../models/client/tax_reporting";
import { ClientShareClasses, clientShareClassesSchema } from "../models/client/share_classes";
  

export const ClientShareClassesTab = <T extends Client>({
  data,
  baseData,
  onChange,
  // param_name? = 'contacts'
}: ClientTabProps<T>) => {

  const param_name = 'share_classes'

  React.useEffect(() => {
    if (!data[param_name]) data[param_name] = []
  }, []);

  React.useEffect(() => {
    console.info(param_name, data)
  }, []);

  const handleListChange = (list: ClientShareClasses[]) => {
    const newData = {...data, [param_name]: list };
    onChange(newData);
  };

  return (
    <div>
      <ListFormBySchema
        schema={clientShareClassesSchema}
        items={data[param_name] || []}
        baseItems={baseData ? baseData[param_name] : []}
        onChange={handleListChange}
        defaultItemValues={{}}
        addButtonText="Add"
        // title="Addresses"
      />
    </div>
  );
}