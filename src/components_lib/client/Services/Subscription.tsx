// ClientSubscriptionModel {
//     subscription_id: string | number
//     subscription: SubscriptionModel {id, title, price}
//     start_date: Date,
//     stop_date: Date,
//     services: Service[{title, price}, ...]
//     summ_price_services: number
// }

// ClientServicesModel: {
//     service_id: string | number
//     service: ServiceModel {id, title, price, type}
//     discount: number
//     start_date: Date
//     paid_date: Date
//     completed_date: Date //(optional)
//     Notes: string
// }
