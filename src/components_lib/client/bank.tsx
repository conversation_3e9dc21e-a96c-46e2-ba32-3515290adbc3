import React from "react";
import { ListFormBySchema } from "../ListFormBySchema";
import { Client, ClientTabProps } from "../models/client";
import { clientBankAccountSchema, ClientBankAccount } from "../models/client/bank_account";
  

export const ClientBankTab = <T extends Client>({
  data,
  baseData,
  onChange,
}: ClientTabProps<T>) => {

  React.useEffect(() => {
    if (!data.bank_accounts) data.bank_accounts = []
  }, []);

  React.useEffect(() => {
    console.info('ClientBankTab', data)
  }, []);

  const handleListChange = (bank_accounts: ClientBankAccount[]) => {
    const newData = {...data, bank_accounts};
    onChange(newData);
  };

  return (
    <div>
      <ListFormBySchema
        schema={clientBankAccountSchema}
        items={data.bank_accounts || []}
        baseItems={baseData?.bank_accounts || []}
        onChange={handleListChange}
        defaultItemValues={{}}
        addButtonText="Add Bank Account"
        // title="Addresses"
      />
    </div>
  );
}