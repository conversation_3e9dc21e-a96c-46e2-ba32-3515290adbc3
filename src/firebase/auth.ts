// import {
//     GoogleAuthProvider,
//     GithubAuthProvider,
//     signInWithPopup,
//     setPersistence,
//     browserSessionPersistence,
//     signInWithEmailAndPassword,
//     signOut,
//   } from 'firebase/auth';
//   import { firebaseAuth } from './firebaseConfig';
  
//   const googleProvider = new GoogleAuthProvider();
//   const githubProvider = new GithubAuthProvider();
  
  // Sign in with Google functionality
  export const signInWithGoogle = async () => {
    debugger
    // try {
    //   return setPersistence(firebaseAuth, browserSessionPersistence).then(async () => {
    //     const result = await signInWithPopup(firebaseAuth, googleProvider);
    //     return {
    //       success: true,
    //       user: result.user,
    //       error: null,
    //     };
    //   });
    // } catch (error: any) {
    //   return {
    //     success: false,
    //     user: null,
    //     error: error.message,
    //   };
    // }
  };
  
  // Sign in with GitHub functionality
  export const signInWithGithub = async () => {
    debugger
    // try {
    //   return setPersistence(firebaseAuth, browserSessionPersistence).then(async () => {
    //     const result = await signInWithPopup(firebaseAuth, githubProvider);
    //     return {
    //       success: true,
    //       user: result.user,
    //       error: null,
    //     };
    //   });
    // } catch (error: any) {
    //   return {
    //     success: false,
    //     user: null,
    //     error: error.message,
    //   };
    // }
  };
  
  // Sign in with email and password
  
  export async function signInWithCredentials(email: string, password: string) {
    debugger
    return {} as any
    // try {
    //   return setPersistence(firebaseAuth, browserSessionPersistence).then(async () => {
    //     const userCredential = await signInWithEmailAndPassword(firebaseAuth, email, password);
    //     return {
    //       success: true,
    //       user: userCredential.user,
    //       error: null,
    //     };
    //   });
    // } catch (error: any) {
    //   return {
    //     success: false,
    //     user: null,
    //     error: error.message || 'Failed to sign in with email/password',
    //   };
    // }
  }
  
  // Sign out functionality
  export const firebaseSignOut = async () => {
    debugger
    // try {
    //   await signOut(firebaseAuth);
    //   return { success: true };
    // } catch (error: any) {
    //   return {
    //     success: false,
    //     error: error.message,
    //   };
    // }
  };
  
  // Auth state observer
  export const onAuthStateChanged = (callback: (user: any) => void) => {
    debugger
    return new Promise(() => {})
    // return firebaseAuth.onAuthStateChanged(callback);
  };