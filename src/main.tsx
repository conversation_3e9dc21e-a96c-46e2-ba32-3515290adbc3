import * as React from 'react';
import * as ReactDOM from 'react-dom/client';
import { RouterProvider } from 'react-router';
// import App from './App';
// import DashboardPage from './pages';
// import OrdersPage from './pages/orders';
import router from './router';
import { AuthProvider } from './context/AuthContext';

// const router = createBrowserRouter([
//   {
//     Component: App, // root layout route
//   },
// ]);
// debugger;
// console.log('main.js')
ReactDOM.createRoot(document.getElementById('root')!).render(
  
  // <React.StrictMode>
  <AuthProvider>
  <RouterProvider router={router} />
  </AuthProvider>
  // </React.StrictMode>
);
