import * as ReactDOM from 'react-dom/client';
import { RouterProvider } from 'react-router';
// import App from './App';
// import DashboardPage from './pages';
// import OrdersPage from './pages/orders';
import router from './router';
import { AuthProvider } from './context/AuthContext';
import { UploadProvider } from './components_lib/core/upload';

// const router = createBrowserRouter([
//   {
//     Component: App, // root layout route
//   },
// ]);
// debugger;
// console.log('main.js')
ReactDOM.createRoot(document.getElementById('root')!).render(

  // <React.StrictMode>
  <AuthProvider>
    <UploadProvider>
      <RouterProvider router={router} />
    </UploadProvider>
  </AuthProvider>
  // </React.StrictMode>
);
