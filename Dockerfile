# FROM node:18.16
# COPY . /build

# #RUN cd /build \
# #  && export NG_CLI_ANALYTICS=ci \
# # && npm install -g @angular/cli@16.1.6

# #RUN cd /build \
# #  && npm install --force

# #RUN cd /build\ 
# #  && cd ext/ext-widgets \
# # && npm install 

# #RUN cd /build \
# #  && npm run build-chat-app

# FROM nginx
# COPY nginx/default.conf /etc/nginx/conf.d
# #COPY --from=0 /build/dist/app /usr/share/nginx/html





FROM node:20
COPY . /build

RUN cd /build \ 
    && npm install --legacy-peer-deps\
    && npm run build

#RUN cd /build \
#  && export NG_CLI_ANALYTICS=ci \
# && npm install -g @angular/cli@16.1.6

#RUN cd /build \
#  && npm install --force

#RUN cd /build\ 
#  && cd ext/ext-widgets \
# && npm install 

#RUN cd /build \
#  && npm run build-chat-app

FROM nginx
COPY nginx/default.conf /etc/nginx/conf.d
COPY --from=0 /build/dist /usr/share/nginx/html
