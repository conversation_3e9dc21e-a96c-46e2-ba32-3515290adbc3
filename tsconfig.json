{
  "compilerOptions": {
    "target": "ESNext",
    "useDefineForClassFields": true,
    "lib": ["DOM", "DOM.Iterable", "ESNext"],
    "allowJs": true,
    "skipLibCheck": true,
    "esModuleInterop": true,
    "allowSyntheticDefaultImports": true,
    "strict": true,
    "forceConsistentCasingInFileNames": true,
    "module": "ESNext",
    "moduleResolution": "Node",
    "resolveJsonModule": true,
    "isolatedModules": true,
    "noEmit": true,
    "jsx": "react-jsx",
    
  },
  "include": [
    "src",
    "tailwind.config.ts", "src/components_lib/models/client/corp_capitalization_table.ts_shareholders.ts",
  ],
  "references": [{ "path": "./tsconfig.node.json" }],
  "extends": ["./tsconfig.paths.json"]
}
