// /** @type {import('postcss-load-config').Config} */
// const config = {
//   plugins: {
//     'tailwindcss/nesting': {},
//     tailwindcss: {},
//     autoprefixer: {}
//   }
// }

// export default config

// module.exports = {
//   plugins: {
//     tailwindcss: {},
//     autoprefixer: {},
//   }
// }

/** @type {import('postcss-load-config').Config} */
const config = {
  plugins: {
    // 'tailwindcss/nesting': {},
    tailwindcss: {},
    autoprefixer: {}
  }
}

export default config
